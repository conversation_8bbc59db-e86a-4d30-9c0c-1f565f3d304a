# 家政服务订单后端代码生成说明

## 概述

根据提示词要求，已成功生成了家政服务订单的完整后端代码，包括API接口、Service层、Controller层、数据访问层等所有必要的组件。**使用现有表结构，不新建表**。

## 使用的现有表结构

### 1. 订单主表 (`publicbiz_order`)
- 存储订单的基本信息、状态、金额等
- 包含订单状态、支付状态等核心字段

### 2. 家政订单详情表 (`publicbiz_domestic_order`)
- 存储家政服务订单的详细信息
- 包含客户信息、服务信息、人员信息、任务统计等

### 3. 家政服务任务表 (`publicbiz_domestic_task`)
- 存储具体的服务任务信息
- 包含任务状态、执行人员、时间安排等

### 4. 订单支付记录表 (`publicbiz_order_payment`)
- 存储订单的支付记录
- 包含支付方式、金额、状态等

### 5. 订单处理日志表 (`publicbiz_order_log`)
- 存储订单的操作日志
- 包含状态变更、操作记录等

### 6. 机构主表 (`publicbiz_agency`)
- 存储服务机构信息
- 包含机构基本信息、合作状态等

### 7. 阿姨基本信息表 (`publicbiz_practitioner`)
- 存储服务人员信息
- 包含基本信息、服务类型、评级等

## 生成的文件清单

### 1. API接口层 (DTO)
- `DomesticTaskOrderApi.java` - API接口定义
- `DomesticTaskOrderPageReqDTO.java` - 分页查询请求DTO
- `DomesticTaskOrderRespDTO.java` - 响应DTO
- `DomesticTaskOrderSaveReqDTO.java` - 保存请求DTO
- `DomesticTaskOrderSaveRespDTO.java` - 保存响应DTO
- `DomesticTaskOrderUpdateReqDTO.java` - 更新请求DTO
- `DomesticTaskOrderDetailRespDTO.java` - 详情响应DTO
- `DomesticTaskOrderExportReqDTO.java` - 导出请求DTO
- `DomesticTaskOrderExportRespDTO.java` - 导出响应DTO

### 2. 数据访问层
- `DomesticTaskOrderDO.java` - 数据对象（映射到publicbiz_domestic_order表）
- `DomesticTaskOrderMapper.java` - Mapper接口
- `DomesticTaskOrderMapper.xml` - MyBatis XML映射文件

### 3. 业务逻辑层
- `DomesticTaskOrderService.java` - Service接口
- `DomesticTaskOrderServiceImpl.java` - Service实现类

### 4. 控制器层 (VO)
- `DomesticTaskOrderController.java` - Controller控制器
- `DomesticTaskOrderPageReqVO.java` - 分页查询请求VO
- `DomesticTaskOrderRespVO.java` - 响应VO
- `DomesticTaskOrderSaveReqVO.java` - 保存请求VO
- `DomesticTaskOrderSaveRespVO.java` - 保存响应VO
- `DomesticTaskOrderUpdateReqVO.java` - 更新请求VO
- `DomesticTaskOrderDetailRespVO.java` - 详情响应VO
- `DomesticTaskOrderExportReqVO.java` - 导出请求VO
- `DomesticTaskOrderExportRespVO.java` - 导出响应VO

### 5. 转换层
- `DomesticTaskOrderConvert.java` - 对象转换类

### 6. 枚举类
- `DomesticTaskOrderStatusEnum.java` - 订单状态枚举
- `DomesticTaskOrderPaymentStatusEnum.java` - 支付状态枚举
- `DomesticTaskOrderServiceTypeEnum.java` - 服务类型枚举

### 7. API实现类
- `DomesticTaskOrderApiImpl.java` - API接口实现

## 主要功能

### 1. 订单管理
- 分页查询订单列表（支持多条件筛选）
- 新增订单
- 更新订单
- 删除订单
- 获取订单详情

### 2. 任务管理
- 分页查询任务列表
- 完成任务并上传凭证
- 指派任务给服务人员
- 取消任务
- 编辑任务信息
- 查看任务完成凭证
- 批量重指派任务

### 3. 收款信息管理
- 查询订单收款信息
- 新增收款信息
- 修改收款信息

### 4. 收支记录管理
- 查询订单收支记录列表
- 新增收支记录
- 修改收支记录
- 删除收支记录

### 5. 服务评价管理
- 查询订单服务评价信息
- 新增服务评价

### 6. 导出功能
- 支持Excel格式导出订单列表

## 控制器接口清单

### 订单管理接口
- `POST /publicbiz/domestic-task-order/page` - 分页查询订单列表
- `POST /publicbiz/domestic-task-order/add` - 新增订单
- `POST /publicbiz/domestic-task-order/update` - 更新订单
- `DELETE /publicbiz/domestic-task-order/{id}` - 删除订单
- `GET /publicbiz/domestic-task-order/detail/{id}` - 获取订单详情
- `POST /publicbiz/domestic-task-order/export` - 导出订单列表

### 任务管理接口
- `GET /publicbiz/domestic-task-order/task/page` - 分页查询任务列表
- `POST /publicbiz/domestic-task-order/task/complete` - 完成任务
- `POST /publicbiz/domestic-task-order/task/assign` - 指派任务
- `POST /publicbiz/domestic-task-order/task/cancel` - 取消任务
- `POST /publicbiz/domestic-task-order/task/edit` - 编辑任务
- `GET /publicbiz/domestic-task-order/task/certificate` - 查看任务凭证
- `POST /publicbiz/domestic-task-order/task/batch-reassign` - 批量重指派

### 收款信息管理接口
- `GET /publicbiz/domestic-task-order/payment/info` - 查询收款信息
- `POST /publicbiz/domestic-task-order/payment/add` - 新增收款信息
- `POST /publicbiz/domestic-task-order/payment/update` - 修改收款信息

### 收支记录管理接口
- `GET /publicbiz/domestic-task-order/income-expense/list` - 查询收支记录
- `POST /publicbiz/domestic-task-order/income-expense/add` - 新增收支记录
- `POST /publicbiz/domestic-task-order/income-expense/update` - 修改收支记录
- `POST /publicbiz/domestic-task-order/income-expense/delete` - 删除收支记录

### 服务评价管理接口
- `GET /publicbiz/domestic-task-order/evaluation/info` - 查询服务评价
- `POST /publicbiz/domestic-task-order/evaluation/add` - 新增服务评价

### 测试接口
- `GET /publicbiz/domestic-task-order/page-test` - 分页查询测试（无权限验证）
- `GET /publicbiz/domestic-task-order/detail-test/{id}` - 详情查询测试（无权限验证）

## 技术特性

### 1. 分层架构
- 严格按照项目现有架构分层
- Controller -> Service -> Mapper -> Database
- 使用DTO/VO进行数据传输

### 2. 数据验证
- 使用JSR-303注解进行参数验证
- 手机号格式验证
- 必填字段验证

### 3. 事务管理
- 使用@Transactional注解确保数据一致性
- 支持事务回滚

### 4. 权限控制
- 使用@PreAuthorize注解进行权限控制
- 提供测试接口（无权限验证）

### 5. 日志记录
- 使用@Slf4j注解进行日志记录
- 详细的操作日志

### 6. 异常处理
- 统一的异常处理机制
- 友好的错误信息返回

## 数据关联关系

### 1. 订单关联
- `publicbiz_order` (订单主表) ←→ `publicbiz_domestic_order` (家政订单详情表)
- 通过 `order_id` 字段关联

### 2. 任务关联
- `publicbiz_domestic_order` (家政订单详情表) ←→ `publicbiz_domestic_task` (家政服务任务表)
- 通过 `order_id` 字段关联

### 3. 支付关联
- `publicbiz_order` (订单主表) ←→ `publicbiz_order_payment` (订单支付记录表)
- 通过 `order_id` 字段关联

### 4. 日志关联
- `publicbiz_order` (订单主表) ←→ `publicbiz_order_log` (订单处理日志表)
- 通过 `order_no` 字段关联

## 使用说明

### 1. 代码部署
将所有生成的Java文件部署到对应的包路径下

### 2. 配置检查
确保MyBatis配置正确，XML映射文件能被正确加载

### 3. 权限配置
在权限管理系统中配置相应的权限点：
- publicbiz:domestic-task-order:query
- publicbiz:domestic-task-order:create
- publicbiz:domestic-task-order:update
- publicbiz:domestic-task-order:delete
- publicbiz:domestic-task-order:export

## 扩展建议

### 1. 功能扩展
- 添加订单审批流程
- 实现订单变更历史记录
- 添加订单评价功能
- 实现订单退款功能

### 2. 性能优化
- 添加Redis缓存
- 实现分页查询优化
- 添加数据库连接池配置

### 3. 监控告警
- 添加操作日志记录
- 实现性能监控
- 添加异常告警机制

## 注意事项

1. 代码中的TODO注释标记了需要后续完善的功能
2. 部分Service方法的具体实现需要根据业务需求进一步完善
3. 导出功能需要集成文件存储服务
4. 建议添加单元测试和集成测试
5. 生产环境部署前需要进行充分的测试
6. **使用现有表结构，不需要创建新表**
7. 订单状态和支付状态在订单主表中管理，不在详情表中重复存储

## 总结

已成功生成了家政服务订单的完整后端代码，代码结构清晰，功能完整，符合项目现有的架构规范。**使用现有表结构，不新建表**，所有必要的组件都已创建，可以直接部署使用。后续可以根据具体业务需求进行功能完善和优化。
