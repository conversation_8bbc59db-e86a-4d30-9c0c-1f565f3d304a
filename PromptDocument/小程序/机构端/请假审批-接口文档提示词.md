### 请假审批页面（pages/agency-workbench/leave-approval.vue）后端接口文档提示词

> 目的：基于现有页面需求，定义可直接生成后端代码的接口契约与实现要点，支持机构端查看、审批阿姨的请假/调休申请，并显示受影响订单信息。

---

### 场景背景
- 页面入口：请假审批（机构端）
- 进入页面后：
  - 需要获取当前机构ID（agencyId）
  - 查询该机构下所有阿姨的请假/调休申请（状态为"审批中"）
  - 显示申请详情：申请人、请假类型、时间、理由等
  - 显示受影响订单信息（请假期间可能影响的订单）
  - 支持审批操作：同意/拒绝，并添加审批备注

---

### 数据库表结构（供实现参考）
- 工单表：`publicbiz_work_order`
  - 关键字段：
    - `id`(PK), `tenant_id`, `work_order_no`, `work_order_title`, `work_order_content`
    - `work_order_type`(take_leave-请假/leave_adjustment-调休)
    - `work_order_status`(pending-审批中/approved-已批准/rejected-已驳回)
    - `aunt_oneid`, `aunt_name`, `leave_type`(1-请假,2-调休)
    - `start_time`, `end_time`, `duration_hours`, `duration_days`
    - `status`(0-审批中,1-已批准,2-已驳回), `approve_time`, `approve_remark`
    - `agency_id`, `agency_name`, `deleted`

---

### 接口一：获取机构请假申请列表
- **Method**: GET
- **Path**: `/publicbiz/agency/{agencyId}/leave-requests`
- **Auth**: 必须
- **Query**: 
  - `status` 可选：`pending|approved|rejected`，默认返回 `pending`
  - `page` 可选：页码，默认1
  - `size` 可选：每页数量，默认20
- **Response**:
```json
{
  "code": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 123,
        "workOrderNo": "WO20250707001",
        "auntOneId": "GUID-xxx",
        "auntName": "李阿姨",
        "leaveType": 1,
        "leaveTypeText": "事假",
        "startTime": "2025-07-01 00:00:00",
        "endTime": "2025-07-03 23:59:59",
        "durationDays": 3.0,
        "durationHours": 72.0,
        "reason": "家中有急事需要处理",
        "workOrderStatus": "pending",
        "createTime": "2025-07-05 10:00:00",
        "affectedOrdersCount": 3
      }
    ],
    "total": 10,
    "page": 1,
    "size": 20
  }
}
```
- **实现要点**：
  - 过滤条件：`tenant_id`、`agency_id`、`work_order_type IN ('take_leave', 'leave_adjustment')`、`deleted=0`
  - 按 `create_time` 倒序排列
  - `leaveType` 映射：1=请假，2=调休
  - `affectedOrdersCount` 通过关联查询计算受影响订单数量

---

### 接口二：获取请假申请详情（包含受影响订单）
- **Method**: GET
- **Path**: `/publicbiz/agency/{agencyId}/leave-requests/{requestId}/detail`
- **Auth**: 必须
- **Response**:
```json
{
  "code": 0,
  "msg": "OK",
  "data": {
    "requestInfo": {
      "id": 123,
      "workOrderNo": "WO20250707001",
      "auntOneId": "GUID-xxx",
      "auntName": "李阿姨",
      "leaveType": 1,
      "leaveTypeText": "事假",
      "startTime": "2025-07-01 00:00:00",
      "endTime": "2025-07-03 23:59:59",
      "durationDays": 3.0,
      "durationHours": 72.0,
      "reason": "家中有急事需要处理",
      "workOrderStatus": "pending",
      "createTime": "2025-07-05 10:00:00"
    },
    "affectedOrders": [
      {
        "orderId": 456,
        "orderNo": "ORD20250707001",
        "orderType": "package",
        "orderTypeText": "套餐订单",
        "serviceName": "4次日常保洁(3小时)",
        "progress": "2/4次",
        "customerName": "张女士",
        "customerPhone": "138****5678",
        "address": "杨浦区莲花小区",
        "serviceTime": "2025-07-01 08:00:00",
        "serviceEndTime": "2025-07-01 11:00:00"
      }
    ]
  }
}
```
- **实现要点**：
  - 通过 `aunt_oneid` 和请假时间范围关联查询受影响订单
  - 订单类型映射：`package`=套餐订单，`single`=单次服务
  - 计算服务进度（已完成次数/总次数）

---

### 接口三：审批请假申请
- **Method**: POST
- **Path**: `/publicbiz/agency/{agencyId}/leave-requests/{requestId}/approve`
- **Auth**: 必须
- **Request Body**:
```json
{
  "action": "approve", // approve-同意, reject-拒绝
  "remark": "同意请假申请", // 审批备注
  "operatorId": 789, // 审批人ID
  "operatorName": "张经理" // 审批人姓名
}
```
- **Response**:
```json
{
  "code": 0,
  "msg": "审批成功",
  "data": {
    "requestId": 123,
    "newStatus": "approved",
    "approveTime": "2025-07-07 14:30:00"
  }
}
```
- **实现要点**：
  - 更新 `work_order_status`、`status`、`approve_time`、`approve_remark`
  - 记录审批人信息
  - 如果拒绝，可能需要通知阿姨
  - 如果同意，可能需要重新安排受影响订单

---

### 接口四：获取受影响订单详情（可选，用于优化性能）
- **Method**: GET
- **Path**: `/publicbiz/agency/{agencyId}/leave-requests/{requestId}/affected-orders`
- **Auth**: 必须
- **Response**:
```json
{
  "code": 0,
  "msg": "OK",
  "data": [
    {
      "orderId": 456,
      "orderNo": "ORD20250707001",
      "orderType": "package",
      "orderTypeText": "套餐订单",
      "serviceName": "4次日常保洁(3小时)",
      "progress": "2/4次",
      "customerName": "张女士",
      "customerPhone": "138****5678",
      "address": "杨浦区莲花小区",
      "serviceTime": "2025-07-01 08:00:00",
      "serviceEndTime": "2025-07-01 11:00:00"
    }
  ]
}
```

---

### SQL 参考（MySQL）

1) 查询请假申请列表：
```sql
-- 输入：:tenantId, :agencyId, :status, :page, :size
SELECT 
  wo.id,
  wo.work_order_no,
  wo.aunt_oneid,
  wo.aunt_name,
  wo.leave_type,
  wo.start_time,
  wo.end_time,
  wo.duration_days,
  wo.duration_hours,
  wo.work_order_content AS reason,
  wo.work_order_status,
  wo.create_time,
  COUNT(DISTINCT dt.id) AS affected_orders_count
FROM publicbiz_work_order wo
LEFT JOIN publicbiz_domestic_task dt ON (
  dt.practitioner_oneid = wo.aunt_oneid 
  AND dt.schedule_date BETWEEN DATE(wo.start_time) AND DATE(wo.end_time)
  AND dt.deleted = b'0'
  AND dt.task_status NOT IN ('cancelled', 'completed')
)
WHERE wo.tenant_id = :tenantId
  AND wo.agency_id = :agencyId
  AND wo.work_order_type IN ('take_leave', 'leave_adjustment')
  AND wo.work_order_status = :status
  AND wo.deleted = b'0'
GROUP BY wo.id
ORDER BY wo.create_time DESC
LIMIT :offset, :size;
```

2) 查询受影响订单：
```sql
-- 输入：:tenantId, :auntOneId, :startDate, :endDate
SELECT 
  dt.id AS order_id,
  dt.order_no,
  dt.task_type AS order_type,
  dt.service_category_name AS service_name,
  dt.customer_name,
  dt.customer_phone,
  dt.service_address AS address,
  dt.schedule_date AS service_time,
  DATE_ADD(dt.schedule_date, INTERVAL dt.duration HOUR) AS service_end_time
FROM publicbiz_domestic_task dt
WHERE dt.tenant_id = :tenantId
  AND dt.practitioner_oneid = :auntOneId
  AND dt.schedule_date BETWEEN :startDate AND :endDate
  AND dt.deleted = b'0'
  AND dt.task_status NOT IN ('cancelled', 'completed')
ORDER BY dt.schedule_date ASC;
```

3) 审批请假申请：
```sql
-- 输入：:requestId, :newStatus, :approveTime, :approveRemark, :operatorId, :operatorName
UPDATE publicbiz_work_order 
SET 
  work_order_status = :newStatus,
  status = CASE 
    WHEN :newStatus = 'approved' THEN 1 
    WHEN :newStatus = 'rejected' THEN 2 
    ELSE 0 
  END,
  approve_time = :approveTime,
  approve_remark = :approveRemark,
  assignee_id = :operatorId,
  assignee_name = :operatorName,
  update_time = NOW()
WHERE id = :requestId;
```

---

### 错误码约定
- `0` 成功
- `40001` 参数缺失或非法
- `40301` 无权限/租户或机构不匹配
- `40401` 请假申请不存在
- `40002` 请假申请状态不允许操作（如已审批）
- `50001` 服务器内部错误

---

### 性能与索引建议
- 索引：
  - `publicbiz_work_order(agency_id, work_order_type, work_order_status, tenant_id, deleted)`
  - `publicbiz_work_order(aunt_oneid, start_time, end_time)`
  - `publicbiz_domestic_task(practitioner_oneid, schedule_date, tenant_id, deleted)`
- 分页：建议使用游标分页优化大数据量场景

---

### 安全与合规
- 所有接口校验 `tenant_id` 与登录态一致
- 只允许访问本机构数据（`agencyId` 校验）
- 审批操作需要记录操作日志
- 敏感信息脱敏（如手机号）

---

### 前端对接契约（与 leave-approval.vue 对齐）
- 列表渲染：
  - 使用"接口一"返回的 `list` 作为请假申请卡片列表
  - `leaveType` 映射：1→"事假"，2→"调休"
  - `affectedOrdersCount` 显示在"受影响订单"区域
- 详情查看：
  - 点击"受影响订单"时调用"接口二"获取完整信息
  - 显示订单类型标签、服务名称、客户信息等
- 审批操作：
  - 点击"同意"/"拒绝"按钮时调用"接口三"
  - 成功后从列表中移除该申请或更新状态

---

### 示例请求/响应
1) 获取请假申请列表
```
GET /publicbiz/agency/10086/leave-requests?status=pending&page=1&size=20
```

2) 获取申请详情
```
GET /publicbiz/agency/10086/leave-requests/123/detail
```

3) 审批申请
```
POST /publicbiz/agency/10086/leave-requests/123/approve
{
  "action": "approve",
  "remark": "同意请假申请",
  "operatorId": 789,
  "operatorName": "张经理"
}
```

---

### 可生成代码的实现提示（伪指令）
- 控制器层：按照上述 Path/Method 定义 4 个接口；统一响应结构 `{code,msg,data}`
- 服务层：
  - 校验 `tenant_id`、`agencyId`、申请状态
  - 通过仓储执行上述 SQL/QueryDSL
  - 计算受影响订单数量（可缓存优化）
  - 处理审批逻辑和状态变更
- 异常处理：
  - 参数校验失败抛业务异常 → `40001`
  - 申请不存在 → `40401`
  - 状态不允许操作 → `40002`
  - 非预期异常 → `50001`
