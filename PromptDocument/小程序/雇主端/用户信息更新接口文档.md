# 用户信息更新接口文档

## 接口概述
用于更新用户的头像和昵称信息。

## 接口详情

### 1. 更新用户信息接口

**接口地址：** `PUT /publicbiz/employer/auth/user/update`

**请求方式：** `PUT`

**Content-Type：** `application/json`

**请求头：**
```
Authorization: Bearer {accessToken}
terminal: mp
tenant-id: {tenantId}
```

**请求参数：**
```json
{
  "avatar": "string",     // 用户头像URL（可选）
  "nickname": "string"    // 用户昵称（可选）
}
```

**响应格式：**
```json
{
  "code": 0,
  "msg": "更新成功",
  "data": {
    "userId": "123456",
    "avatar": "https://example.com/uploads/avatar/2024/01/user_avatar_123.jpg",
    "nickname": "张三",
    "updateTime": "2024-01-15 10:30:00"
  }
}
```

## 业务逻辑

### 1. 参数验证
- 验证用户是否已登录（通过accessToken）
- 验证avatar参数：如果提供，必须是有效的URL格式
- 验证nickname参数：如果提供，长度限制在1-20个字符
- 至少需要提供一个参数（avatar或nickname）

### 2. 头像更新逻辑
- 如果提供了avatar参数，验证URL是否可访问
- 更新用户表中的avatar字段
- 记录头像更新日志

### 3. 昵称更新逻辑
- 如果提供了nickname参数，检查昵称是否已被其他用户使用
- 更新用户表中的nickname字段
- 记录昵称更新日志

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 401 | 未授权 |
| 404 | 用户不存在 |
| 409 | 昵称已被使用 |
| 500 | 服务器内部错误 |

## 前端调用示例

```javascript
// 更新用户头像
const updateResult = await UserApi.updateUserInfo({
  avatar: uploadResult.url
})

// 更新用户昵称
const updateResult = await UserApi.updateUserInfo({
  nickname: '新昵称'
})

// 同时更新头像和昵称
const updateResult = await UserApi.updateUserInfo({
  avatar: uploadResult.url,
  nickname: '新昵称'
})
```
