package cn.bztmaster.cnt.module.publicbiz.service.partner;

import cn.bztmaster.cnt.framework.test.core.ut.BaseDbUnitTest;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.partner.PartnerMapper;
import cn.bztmaster.cnt.module.publicbiz.service.partner.impl.PartnerServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link PartnerServiceImpl} 新字段同步功能的单元测试
 */
@Import(PartnerServiceImpl.class)
public class PartnerServiceImplSyncTest extends BaseDbUnitTest {

    @Resource
    private PartnerServiceImpl partnerService;

    @Resource
    private PartnerMapper partnerMapper;

    @Resource
    private AgencyMapper agencyMapper;

    @Test
    public void testCreatePartner_WithNewFields_ShouldSyncToAgency() {
        // 创建家政机构类型的合作伙伴，包含新字段
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试家政机构");
        reqVO.setShortName("测试家政");
        reqVO.setType("家政机构");
        reqVO.setCreditCode("91110000123456789X");
        reqVO.setLegalPerson("张三");
        reqVO.setFoundationDate("2020-01-01");
        reqVO.setRegisterAddress("四川省成都市高新区注册地址");
        reqVO.setBusinessAddress("四川省成都市高新区经营地址");
        reqVO.setMainBusiness("家政服务、保洁服务");
        reqVO.setContactName("李四");
        reqVO.setContactPhone("***********");
        
        // 设置财务信息
        reqVO.setRegisteredCapital(new BigDecimal("100.50"));
        
        // 设置地址信息
        reqVO.setProvinceCode("510000");
        reqVO.setProvince("四川省");
        reqVO.setCityCode("510100");
        reqVO.setCity("成都市");
        reqVO.setDistrictCode("510107");
        reqVO.setDistrict("高新区");
        reqVO.setDetailAddress("天府大道中段1388号");
        
        // 设置地理位置信息
        reqVO.setLongitude(104.065735);
        reqVO.setLatitude(30.659462);

        // 设置合同信息
        reqVO.setContractNo("HT001");
        reqVO.setContractStart(java.sql.Date.valueOf("2024-01-01"));
        reqVO.setContractEnd(java.sql.Date.valueOf("2024-12-31"));

        // 设置开票信息
        reqVO.setInvoiceName("测试家政机构开票名称");
        reqVO.setTaxId("91110000123456789X");
        reqVO.setInvoiceBank("中国银行");
        reqVO.setInvoiceBankAccount("****************");

        // 创建合作伙伴
        Long partnerId = partnerService.createPartner(reqVO);
        assertNotNull(partnerId);

        // 验证合作伙伴记录
        PartnerDO partner = partnerMapper.selectById(partnerId);
        assertNotNull(partner);
        assertNotNull(partner.getAgencyId());
        assertNotNull(partner.getAgencyName());

        // 验证机构记录是否正确同步了新字段
        AgencyDO agency = agencyMapper.selectById(partner.getAgencyId());
        assertNotNull(agency);

        // 验证基础信息
        assertEquals("测试家政机构", agency.getAgencyName());
        assertEquals("测试家政", agency.getAgencyShortName());
        assertEquals("张三", agency.getLegalRepresentative());
        assertEquals(java.time.LocalDate.of(2020, 1, 1), agency.getEstablishmentDate());
        assertEquals("四川省成都市高新区注册地址", agency.getRegisteredAddress());
        assertEquals("四川省成都市高新区经营地址", agency.getOperatingAddress());
        assertEquals("家政服务、保洁服务", agency.getBusinessScope());
        assertEquals("李四", agency.getContactPerson());
        assertEquals("***********", agency.getContactPhone());

        // 验证财务信息
        assertEquals(new BigDecimal("100.50"), agency.getRegisteredCapital());
        
        // 验证地址信息
        assertEquals("510000", agency.getProvinceCode());
        assertEquals("四川省", agency.getProvince());
        assertEquals("510100", agency.getCityCode());
        assertEquals("成都市", agency.getCity());
        assertEquals("510107", agency.getDistrictCode());
        assertEquals("高新区", agency.getDistrict());
        assertEquals("天府大道中段1388号", agency.getDetailAddress());
        
        // 验证地理位置信息
        assertEquals(new BigDecimal("104.065735"), agency.getLongitude());
        assertEquals(new BigDecimal("30.659462"), agency.getLatitude());

        // 验证合同信息
        assertEquals("HT001", agency.getContractNo());
        assertEquals(java.time.LocalDate.of(2024, 1, 1), agency.getContractStartDate());
        assertEquals(java.time.LocalDate.of(2024, 12, 31), agency.getContractEndDate());

        // 验证开票信息
        assertEquals("测试家政机构开票名称", agency.getInvoiceName());
        assertEquals("91110000123456789X", agency.getTaxpayerId());
        assertEquals("中国银行", agency.getBankName());
        assertEquals("****************", agency.getBankAccount());
    }

    @Test
    public void testCreatePartner_NonDomesticAgency_ShouldNotSync() {
        // 创建非家政机构类型的合作伙伴
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试企业");
        reqVO.setShortName("测试");
        reqVO.setType("企业"); // 非家政机构
        reqVO.setCreditCode("91110000123456789Y");
        reqVO.setRegisteredCapital(new BigDecimal("200.00"));

        // 创建合作伙伴
        Long partnerId = partnerService.createPartner(reqVO);
        assertNotNull(partnerId);

        // 验证合作伙伴记录
        PartnerDO partner = partnerMapper.selectById(partnerId);
        assertNotNull(partner);
        
        // 验证没有同步到机构表（因为不是家政机构）
        assertNull(partner.getAgencyId());
        assertNull(partner.getAgencyName());
    }

    @Test
    public void testCreatePartner_WithInvalidLocationData_ShouldFilterOut() {
        // 创建家政机构，但包含无效的地理位置数据
        PartnerSaveReqVO reqVO = new PartnerSaveReqVO();
        reqVO.setName("测试家政机构2");
        reqVO.setShortName("测试家政2");
        reqVO.setType("家政机构");
        reqVO.setCreditCode("91110000123456789Z");

        // 设置必填的财务和地址信息
        reqVO.setRegisteredCapital(new BigDecimal("50.00"));
        reqVO.setProvinceCode("510000");
        reqVO.setProvince("四川省");
        reqVO.setCityCode("510100");
        reqVO.setCity("成都市");
        reqVO.setDistrictCode("510107");
        reqVO.setDistrict("高新区");
        reqVO.setDetailAddress("测试地址");

        // 设置无效的地理位置信息
        reqVO.setLongitude(200.0); // 超出有效范围
        reqVO.setLatitude(100.0);  // 超出有效范围

        // 创建合作伙伴
        Long partnerId = partnerService.createPartner(reqVO);
        assertNotNull(partnerId);

        // 验证机构记录中无效数据被过滤
        PartnerDO partner = partnerMapper.selectById(partnerId);
        AgencyDO agency = agencyMapper.selectById(partner.getAgencyId());
        assertNotNull(agency);

        // 验证财务和地址信息正常同步
        assertEquals(new BigDecimal("50.00"), agency.getRegisteredCapital());
        assertEquals("510000", agency.getProvinceCode());
        assertEquals("四川省", agency.getProvince());
        assertEquals("测试地址", agency.getDetailAddress());

        // 验证无效的地理位置信息没有被同步
        assertNull(agency.getLongitude());
        assertNull(agency.getLatitude());
    }
}
