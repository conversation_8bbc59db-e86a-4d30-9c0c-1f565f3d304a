package cn.bztmaster.cnt.module.publicbiz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 家政服务订单服务类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DomesticTaskOrderServiceTypeEnum {

    MATERNITY("maternity", "月嫂服务"),
    DEEP_CLEANING("deep_cleaning", "深度保洁"),
    HOURLY("hourly", "小时工"),
    NANNY("nanny", "育儿嫂服务"),
    RANGE_HOOD_CLEANING("range_hood_cleaning", "油烟机清洗"),
    DAILY_CLEANING("daily_cleaning", "日常保洁"),
    GLASS_CLEANING("glass_cleaning", "玻璃清洗");

    /**
     * 类型值
     */
    private final String value;
    /**
     * 类型名称
     */
    private final String name;

    /**
     * 根据类型值获取类型名称
     *
     * @param value 类型值
     * @return 类型名称
     */
    public static String getNameByValue(String value) {
        for (DomesticTaskOrderServiceTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type.getName();
            }
        }
        return "未知类型";
    }

    /**
     * 根据类型值获取枚举
     *
     * @param value 类型值
     * @return 枚举
     */
    public static DomesticTaskOrderServiceTypeEnum getByValue(String value) {
        for (DomesticTaskOrderServiceTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}

