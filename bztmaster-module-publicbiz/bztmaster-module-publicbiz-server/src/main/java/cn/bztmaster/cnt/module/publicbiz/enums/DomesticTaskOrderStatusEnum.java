package cn.bztmaster.cnt.module.publicbiz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 家政服务订单状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DomesticTaskOrderStatusEnum {

    PENDING("pending", "待派单"),
    ASSIGNED("assigned", "已派单"),
    IN_SERVICE("in_service", "服务中"),
    COMPLETED("completed", "已完成"),
    CANCELLED("cancelled", "已取消");

    /**
     * 状态值
     */
    private final String value;
    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态值获取状态名称
     *
     * @param value 状态值
     * @return 状态名称
     */
    public static String getNameByValue(String value) {
        for (DomesticTaskOrderStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    /**
     * 根据状态值获取枚举
     *
     * @param value 状态值
     * @return 枚举
     */
    public static DomesticTaskOrderStatusEnum getByValue(String value) {
        for (DomesticTaskOrderStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}

