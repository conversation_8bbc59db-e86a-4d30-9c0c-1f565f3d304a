package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.evaluation;

import cn.bztmaster.cnt.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单评价 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_aunt_review")
@KeySequence("publicbiz_aunt_review_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderEvaluationDO extends TenantBaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 阿姨OneID
     */
    private String auntId;

    /**
     * 评价人用户ID
     */
    private Long reviewerId;

    /**
     * 评价人姓名
     */
    private String reviewerName;

    /**
     * 评价人头像URL
     */
    private String reviewerAvatar;

    /**
     * 评分：1.0-5.0（生成列，由数据库自动计算）
     */
    @TableField(update = "false")
    private BigDecimal rating;

    /**
     * 服务态度评分（0-5.0）
     */
    private BigDecimal attitudeRating;

    /**
     * 技术专业性评分（0-5.0）
     */
    private BigDecimal professionalRating;

    /**
     * 责任心评分（0-5.0）
     */
    private BigDecimal responsibilityRating;

    /**
     * 评价标签（JSON格式，如：["专业","细心","守时"]）
     */
    private String reviewTags;

    /**
     * 评价内容
     */
    private String reviewContent;

    /**
     * 评价图片URL列表（JSON格式）
     */
    private String reviewImages;

    /**
     * 评价类型：service-服务评价，attitude-态度评价，professional-专业评价
     */
    private String reviewType;

    /**
     * 是否匿名评价：0-否，1-是
     */
    private Integer isAnonymous;

    /**
     * 是否推荐：0-否，1-是
     */
    private Integer isRecommend;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 回复时间
     */
    private LocalDateTime replyTime;

    /**
     * 状态：0-隐藏，1-显示
     */
    private Integer status;

    /**
     * 所属机构ID
     */
    private Long agencyId;

    /**
     * 服务套餐ID
     */
    private Long servicePackageId;

    // ========== 兼容字段（用于映射旧字段名） ==========

    /**
     * 订单号（兼容字段）
     */
    private String orderNo;

    /**
     * 客户姓名（兼容字段）
     */
    private String customerName;

    /**
     * 服务人员姓名（兼容字段）
     */
    private String practitionerName;

    /**
     * 服务评分（兼容字段）
     */
    private BigDecimal serviceRating;

    /**
     * 综合评分（兼容字段）
     */
    private BigDecimal overallRating;

    /**
     * 评价内容（兼容字段）
     */
    private String evaluationContent;

    /**
     * 评价标签（兼容字段）
     */
    private String evaluationTags;

    /**
     * 评价图片（兼容字段）
     */
    private String evaluationImages;

    /**
     * 评价时间（兼容字段）
     */
    private LocalDateTime evaluationTime;

    /**
     * 回复人ID（兼容字段）
     */
    private Long replyUserId;

    /**
     * 回复人姓名（兼容字段）
     */
    private String replyUserName;

    /**
     * 评价状态（兼容字段）
     */
    private String evaluationStatus;
}
