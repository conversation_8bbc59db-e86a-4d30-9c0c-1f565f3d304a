package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 家政服务订单详情 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_domestic_order")
@KeySequence("publicbiz_domestic_order_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DomesticTaskOrderDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 客户OneID GUID
     */
    private String customerOneid;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 客户地址
     */
    private String customerAddress;

    /**
     * 客户备注
     */
    private String customerRemark;

    /**
     * 服务分类ID
     */
    private Long serviceCategoryId;

    /**
     * 服务分类名称
     */
    private String serviceCategoryName;

    /**
     * 服务套餐ID
     */
    private Long servicePackageId;

    /**
     * 服务套餐名称
     */
    private String servicePackageName;

    /**
     * 服务开始日期
     */
    private LocalDate serviceStartDate;

    /**
     * 服务结束日期
     */
    private LocalDate serviceEndDate;

    /**
     * 服务时长
     */
    private String serviceDuration;

    /**
     * 服务频次
     */
    private String serviceFrequency;

    /**
     * 套餐主图URL
     */
    private String servicePackageThumbnail;

    /**
     * 套餐价格
     */
    private BigDecimal servicePackagePrice;

    /**
     * 套餐原价
     */
    private BigDecimal servicePackageOriginalPrice;

    /**
     * 价格单位：次/项/天/月
     */
    private String servicePackageUnit;

    /**
     * 服务时长，如：4小时、26天、90天
     */
    private String servicePackageDuration;

    /**
     * 套餐类型：long-term-长周期套餐/count-card-次数次卡套餐
     */
    private String servicePackageType;

    /**
     * 服务描述
     */
    private String serviceDescription;

    /**
     * 详细服务内容，富文本格式
     */
    private String serviceDetails;

    /**
     * 服务流程，富文本格式
     */
    private String serviceProcess;

    /**
     * 购买须知
     */
    private String purchaseNotice;

    /**
     * 服务次数
     */
    private Integer serviceTimes;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 实付金额
     */
    private BigDecimal actualAmount;

    /**
     * 服务地址
     */
    private String serviceAddress;

    /**
     * 详细地址
     */
    private String serviceAddressDetail;

    /**
     * 服务地址纬度
     */
    private BigDecimal serviceLatitude;

    /**
     * 服务地址经度
     */
    private BigDecimal serviceLongitude;

    /**
     * 服务时间安排(JSON格式)
     */
    private String serviceSchedule;

    /**
     * 服务人员OneID
     */
    private String practitionerOneid;

    /**
     * 服务人员姓名
     */
    private String practitionerName;

    /**
     * 服务人员电话
     */
    private String practitionerPhone;

    /**
     * 服务机构ID
     */
    private Long agencyId;

    /**
     * 服务机构名称
     */
    private String agencyName;

    /**
     * 任务总数
     */
    private Integer taskCount;

    /**
     * 已完成任务数
     */
    private Integer completedTaskCount;

    /**
     * 任务进度百分比
     */
    private BigDecimal taskProgress;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 机构费
     */
    private BigDecimal agencyFee;

    /**
     * 平台费
     */
    private BigDecimal platformFee;

    // 支付相关字段已移除，因为数据库表中不存在这些字段
    // 支付信息应该从 publicbiz_order 主表和 publicbiz_order_payment 支付记录表获取

    /**
     * 租户ID
     */
    private Long tenantId;
}
