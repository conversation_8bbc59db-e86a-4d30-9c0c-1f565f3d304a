package cn.bztmaster.cnt.module.publicbiz.api.order;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.*;
import cn.bztmaster.cnt.module.publicbiz.service.order.DomesticTaskOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 家政服务订单 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DomesticTaskOrderApiImpl implements DomesticTaskOrderApi {

    @Resource
    private DomesticTaskOrderService domesticTaskOrderService;

    @Override
    public PageResult<DomesticTaskOrderRespDTO> pageDomesticTaskOrder(DomesticTaskOrderPageReqDTO reqDTO) {
        return domesticTaskOrderService.pageDomesticTaskOrder(reqDTO);
    }

    @Override
    public DomesticTaskOrderSaveRespDTO createDomesticTaskOrder(DomesticTaskOrderSaveReqDTO reqDTO) {
        return domesticTaskOrderService.createDomesticTaskOrder(reqDTO);
    }

    @Override
    public Boolean updateDomesticTaskOrder(DomesticTaskOrderUpdateReqDTO reqDTO) {
        return domesticTaskOrderService.updateDomesticTaskOrder(reqDTO);
    }

    @Override
    public Boolean deleteDomesticTaskOrder(Long id) {
        return domesticTaskOrderService.deleteDomesticTaskOrder(id);
    }

    @Override
    public DomesticTaskOrderDetailRespDTO getDomesticTaskOrderDetail(Long id) {
        return domesticTaskOrderService.getDomesticTaskOrderDetail(id);
    }

    @Override
    public DomesticTaskOrderExportRespDTO exportDomesticTaskOrder(DomesticTaskOrderExportReqDTO reqDTO) {
        return domesticTaskOrderService.exportDomesticTaskOrder(reqDTO);
    }
}

