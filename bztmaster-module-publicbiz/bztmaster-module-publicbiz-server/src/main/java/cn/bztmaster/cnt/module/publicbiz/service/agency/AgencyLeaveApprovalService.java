package cn.bztmaster.cnt.module.publicbiz.service.agency;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalRespVO;

public interface AgencyLeaveApprovalService {

    /**
     * 获取机构请假申请列表
     *
     * @param agencyId 机构ID
     * @param status   状态
     * @param page     页码
     * @param size     每页数量
     * @return 分页结果
     */
    PageResult<LeaveApprovalListRespVO> getLeaveRequests(Long agencyId, String status, Integer page, Integer size);

    /**
     * 获取请假申请详情（包含受影响订单）
     *
     * @param agencyId  机构ID
     * @param requestId 申请ID
     * @return 申请详情
     */
    LeaveApprovalDetailRespVO getLeaveRequestDetail(Long agencyId, Long requestId);

    /**
     * 审批请假申请
     *
     * @param agencyId  机构ID
     * @param requestId 申请ID
     * @param reqVO     审批请求
     * @return 审批结果
     */
    LeaveApprovalRespVO approveLeaveRequest(Long agencyId, Long requestId, LeaveApprovalReqVO reqVO);

    /**
     * 获取受影响订单详情
     *
     * @param agencyId  机构ID
     * @param requestId 申请ID
     * @return 受影响订单列表
     */
    LeaveApprovalDetailRespVO.AffectedOrderVO[] getAffectedOrders(Long agencyId, Long requestId);
}
