package cn.bztmaster.cnt.module.publicbiz.service.order.impl;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.*;
import cn.bztmaster.cnt.module.publicbiz.convert.order.DomesticTaskOrderConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.DomesticTaskOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.evaluation.OrderEvaluationDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.DomesticTaskOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderPaymentMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.evaluation.OrderEvaluationMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizIncomeExpenseMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizIncomeExpenseDO;
import cn.bztmaster.cnt.module.publicbiz.enums.IncomeExpenseTypeEnum;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO;
import org.springframework.beans.BeanUtils;
import java.time.format.DateTimeFormatter;
import java.util.List;
import cn.bztmaster.cnt.module.publicbiz.service.order.DomesticTaskOrderService;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderPaymentService;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderLogQueryService;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionReqDTO;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.context.LogRecordContext;
import cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants;
import cn.bztmaster.cnt.framework.common.biz.system.logger.OperateLogCommonApi;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import java.util.Random;
import java.util.ArrayList;
import java.util.Set;
import java.util.stream.Collectors;
import cn.hutool.core.date.DateUtil;
import cn.bztmaster.cnt.framework.common.util.json.JsonUtils;
import java.util.Objects;

import static cn.bztmaster.cnt.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;

/**
 * 家政服务订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DomesticTaskOrderServiceImpl implements DomesticTaskOrderService {

    private static final Logger log = LoggerFactory.getLogger(DomesticTaskOrderServiceImpl.class);

    @Resource
    private DomesticTaskOrderMapper domesticTaskOrderMapper;

    @Resource
    private PublicbizOrderMapper publicbizOrderMapper;

    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Resource
    private PublicbizOrderPaymentMapper orderPaymentMapper;

    @Resource
    private OrderEvaluationMapper orderEvaluationMapper;

    @Resource
    private OrderPaymentService orderPaymentService;

    @Resource
    private OperateLogCommonApi operateLogApi;

    @Resource
    private ServicePackageMapper servicePackageMapper;

    @Resource
    private AgencyMapper agencyMapper;

    @Resource
    private PractitionerMapper practitionerMapper;

    @Resource
    private PublicbizOrderLogMapper publicbizOrderLogMapper;

    @Resource
    private OrderLogQueryService orderLogQueryService;

    @Resource
    private PublicbizIncomeExpenseMapper incomeExpenseMapper;

    @Resource
    private WorkOrderMapper workOrderMapper;

    @Override
    public PageResult<DomesticTaskOrderRespDTO> pageDomesticTaskOrder(DomesticTaskOrderPageReqDTO reqDTO) {
        log.info("开始分页查询家政服务订单列表，查询条件: {}", reqDTO);
        
        // 构建查询条件 - 查询主表
        LambdaQueryWrapperX<PublicbizOrderDO> queryWrapper = new LambdaQueryWrapperX<>();
        
        // 只查询家政服务订单
        queryWrapper.eq(PublicbizOrderDO::getOrderType, "domestic");
        
        // 订单状态筛选
        if (StrUtil.isNotBlank(reqDTO.getOrderStatus())) {
            queryWrapper.eq(PublicbizOrderDO::getOrderStatus, reqDTO.getOrderStatus());
        }
        
        // 支付状态筛选
        if (StrUtil.isNotBlank(reqDTO.getPaymentStatus())) {
            queryWrapper.eq(PublicbizOrderDO::getPaymentStatus, reqDTO.getPaymentStatus());
        }
        
        // 日期范围筛选
        if (reqDTO.getStartDate() != null) {
            queryWrapper.ge(PublicbizOrderDO::getStartDate, reqDTO.getStartDate());
        }
        if (reqDTO.getEndDate() != null) {
            queryWrapper.le(PublicbizOrderDO::getEndDate, reqDTO.getEndDate());
        }
        
        // 按创建时间倒序
        queryWrapper.orderByDesc(PublicbizOrderDO::getCreateTime);
        
        // 分页查询主表
        PageResult<PublicbizOrderDO> pageResult = publicbizOrderMapper.selectPage(reqDTO, queryWrapper);
        
        // 转换为DTO - 这里需要手动构建，因为查询的是主表而不是详情表
        List<DomesticTaskOrderRespDTO> respList = new ArrayList<>();
        for (PublicbizOrderDO order : pageResult.getList()) {
            // 查询对应的详情表数据
            DomesticTaskOrderDO detail = domesticTaskOrderMapper.selectByOrderId(order.getId());
            if (detail != null) {
                // 关键词搜索过滤
                if (StrUtil.isNotBlank(reqDTO.getKeyword())) {
                    String keyword = reqDTO.getKeyword().toLowerCase();
                    boolean matchKeyword = (detail.getCustomerName() != null && detail.getCustomerName().toLowerCase().contains(keyword)) ||
                            (detail.getPractitionerName() != null && detail.getPractitionerName().toLowerCase().contains(keyword)) ||
                            (detail.getAgencyName() != null && detail.getAgencyName().toLowerCase().contains(keyword));
                    if (!matchKeyword) {
                        continue;
                    }
                }
                
                // 服务类型筛选
                if (StrUtil.isNotBlank(reqDTO.getServiceType())) {
                    if (!reqDTO.getServiceType().equals(detail.getServiceCategoryName())) {
                        continue;
                    }
                }
                
                DomesticTaskOrderRespDTO respDTO = buildListResponse(order, detail);
                respList.add(respDTO);
            }
        }
        
        log.info("分页查询家政服务订单列表完成，总记录数: {}, 当前页记录数: {}", pageResult.getTotal(), respList.size());
        return new PageResult<>(respList, pageResult.getTotal());
    }

    /**
     * 构建列表响应数据
     */
    private DomesticTaskOrderRespDTO buildListResponse(PublicbizOrderDO order, DomesticTaskOrderDO detail) {
        DomesticTaskOrderRespDTO respDTO = new DomesticTaskOrderRespDTO();
        respDTO.setId(detail.getId());
        respDTO.setOrderNumber(order.getOrderNo());
        respDTO.setOrderStatus(order.getOrderStatus());
        respDTO.setPaymentStatus(order.getPaymentStatus());
        respDTO.setCustomerName(detail.getCustomerName());
        respDTO.setCustomerPhone(detail.getCustomerPhone());
        respDTO.setServiceType(detail.getServiceCategoryName());
        respDTO.setServicePersonnel(detail.getPractitionerName());
        respDTO.setServiceAgency(detail.getAgencyName());
        respDTO.setServiceAmount(order.getTotalAmount());
        respDTO.setTotalTasks(detail.getTaskCount());
        respDTO.setCompletedTasks(detail.getCompletedTaskCount());
        respDTO.setAppointmentTime(detail.getServiceStartDate() != null ? 
            detail.getServiceStartDate().atStartOfDay() : null);
        respDTO.setCreateTime(order.getCreateTime());
        respDTO.setUpdateTime(order.getUpdateTime());
        return respDTO;
    }

    /**
     * 获取订单统计信息
     */
    public Map<String, Object> getOrderStatistics() {
        log.info("开始获取订单统计信息");
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 1. 总订单数
        LambdaQueryWrapperX<PublicbizOrderDO> totalQuery = new LambdaQueryWrapperX<>();
        totalQuery.eq(PublicbizOrderDO::getOrderType, "domestic");
        totalQuery.eq(PublicbizOrderDO::getDeleted, false);
        Long totalOrders = publicbizOrderMapper.selectCount(totalQuery);
        statistics.put("totalOrders", totalOrders);
        
        // 2. 待处理订单数（pending状态）
        LambdaQueryWrapperX<PublicbizOrderDO> pendingQuery = new LambdaQueryWrapperX<>();
        pendingQuery.eq(PublicbizOrderDO::getOrderType, "domestic");
        pendingQuery.eq(PublicbizOrderDO::getOrderStatus, "pending");
        pendingQuery.eq(PublicbizOrderDO::getDeleted, false);
        Long pendingOrders = publicbizOrderMapper.selectCount(pendingQuery);
        statistics.put("pendingOrders", pendingOrders);
        
        // 3. 本月订单金额
        LocalDate now = LocalDate.now();
        LocalDate firstDayOfMonth = now.withDayOfMonth(1);
        LocalDate lastDayOfMonth = now.withDayOfMonth(now.lengthOfMonth());
        
        LambdaQueryWrapperX<PublicbizOrderDO> monthlyQuery = new LambdaQueryWrapperX<>();
        monthlyQuery.eq(PublicbizOrderDO::getOrderType, "domestic");
        monthlyQuery.ge(PublicbizOrderDO::getCreateTime, firstDayOfMonth.atStartOfDay());
        monthlyQuery.le(PublicbizOrderDO::getCreateTime, lastDayOfMonth.atTime(23, 59, 59));
        monthlyQuery.eq(PublicbizOrderDO::getDeleted, false);
        
        List<PublicbizOrderDO> monthlyOrders = publicbizOrderMapper.selectList(monthlyQuery);
        BigDecimal monthlyAmount = monthlyOrders.stream()
            .map(PublicbizOrderDO::getTotalAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        statistics.put("monthlyAmount", monthlyAmount);
        
        // 4. 订单完成率
        LambdaQueryWrapperX<PublicbizOrderDO> completedQuery = new LambdaQueryWrapperX<>();
        completedQuery.eq(PublicbizOrderDO::getOrderType, "domestic");
        completedQuery.eq(PublicbizOrderDO::getOrderStatus, "completed");
        completedQuery.eq(PublicbizOrderDO::getDeleted, false);
        Long completedOrders = publicbizOrderMapper.selectCount(completedQuery);
        
        BigDecimal completionRate = totalOrders > 0 ? 
            new BigDecimal(completedOrders).divide(new BigDecimal(totalOrders), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100")) : BigDecimal.ZERO;
        statistics.put("completionRate", completionRate);
        
        log.info("订单统计信息获取完成: {}", statistics);
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.DOMESTIC_TASK_ORDER_TYPE, subType = "创建家政服务订单", bizNo = "{{#orderDO.orderNo}}", success = "创建了家政服务订单【{{#orderDO.orderNo}}】")
    public DomesticTaskOrderSaveRespDTO createDomesticTaskOrder(DomesticTaskOrderSaveReqDTO reqDTO) {
        log.info("开始创建家政服务订单，客户姓名: {}", reqDTO.getCustomerName());
        
        // 1. 创建订单主表记录
        PublicbizOrderDO orderDO = createMainOrder(reqDTO);
        
        // 2. 创建家政服务订单详情记录
        DomesticTaskOrderDO domesticOrderDO = createDomesticOrderDetail(orderDO, reqDTO);
        
        // 3. 设置操作日志上下文
        LogRecordContext.putVariable("orderDO", orderDO);
        LogRecordContext.putVariable("reqDTO", reqDTO);
        
        // 4. 手动记录操作日志（JSON格式）
        recordOrderCreateLog(reqDTO, orderDO, domesticOrderDO);
        
        // 5. 构建响应
        DomesticTaskOrderSaveRespDTO respDTO = new DomesticTaskOrderSaveRespDTO();
        respDTO.setOrderId(domesticOrderDO.getId());
        respDTO.setOrderNo(orderDO.getOrderNo());
        respDTO.setCustomerName(reqDTO.getCustomerName());
        respDTO.setCustomerPhone(reqDTO.getCustomerPhone());
        respDTO.setServiceType(reqDTO.getServiceType());
        respDTO.setTotalAmount(reqDTO.getTotalAmount());
        respDTO.setPaymentStatus(reqDTO.getPaymentStatus());
        
        log.info("家政服务订单创建成功，订单号: {}", orderDO.getOrderNo());
        return respDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.DOMESTIC_TASK_ORDER_TYPE, subType = "订单编辑", bizNo = "{{#existingOrder.orderNo}}", success = "更新了家政服务订单【{{#existingOrder.orderNo}}】")
    public Boolean updateDomesticTaskOrder(DomesticTaskOrderUpdateReqDTO reqDTO) {
        log.info("开始更新家政服务订单，订单ID: {}", reqDTO.getId());
        
        // 1. 验证订单是否存在
        DomesticTaskOrderDO existingOrder = domesticTaskOrderMapper.selectById(reqDTO.getId());
        if (existingOrder == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "家政服务订单不存在");
        }
        
        // 2. 保存原始数据用于比较
        DomesticTaskOrderDO originalOrder = new DomesticTaskOrderDO();
        BeanUtils.copyProperties(existingOrder, originalOrder);
        
        // 3. 检查服务人员是否变更
        boolean practitionerChanged = false;
        String oldPractitionerId = existingOrder.getPractitionerOneid();
        String newPractitionerId = reqDTO.getPractitionerOneid();
        if (oldPractitionerId != null && newPractitionerId != null && !oldPractitionerId.equals(newPractitionerId)) {
            practitionerChanged = true;
            log.info("检测到服务人员变更，原人员ID: {}, 新人员ID: {}", oldPractitionerId, newPractitionerId);
        }
        
        // 4. 更新家政服务订单详情
        DomesticTaskOrderDO updateDO = new DomesticTaskOrderDO();
        updateDO.setId(reqDTO.getId());
        updateDO.setUpdateTime(LocalDateTime.now());
        updateDO.setUpdater(SecurityFrameworkUtils.getLoginUserId().toString());
        
        // 确保金额字段正确设置
        if (reqDTO.getUnitPrice() != null) {
            updateDO.setUnitPrice(reqDTO.getUnitPrice());
        }
        if (reqDTO.getTotalAmount() != null) {
            updateDO.setTotalAmount(reqDTO.getTotalAmount());
        }
        if (reqDTO.getActualAmount() != null) {
            updateDO.setActualAmount(reqDTO.getActualAmount());
        }
        
        // 确保其他重要字段也被更新
        if (reqDTO.getCustomerName() != null) {
            updateDO.setCustomerName(reqDTO.getCustomerName());
        }
        if (reqDTO.getCustomerPhone() != null) {
            updateDO.setCustomerPhone(reqDTO.getCustomerPhone());
        }
        if (reqDTO.getServiceAddress() != null) {
            updateDO.setServiceAddress(reqDTO.getServiceAddress());
        }
        if (reqDTO.getServiceStartDate() != null) {
            updateDO.setServiceStartDate(reqDTO.getServiceStartDate());
        }
        if (reqDTO.getAgencyId() != null) {
            updateDO.setAgencyId(reqDTO.getAgencyId());
        }
        if (reqDTO.getPractitionerOneid() != null) {
            updateDO.setPractitionerOneid(reqDTO.getPractitionerOneid());
        }
        if (reqDTO.getPractitionerName() != null) {
            updateDO.setPractitionerName(reqDTO.getPractitionerName());
        }
        if (reqDTO.getPractitionerPhone() != null) {
            updateDO.setPractitionerPhone(reqDTO.getPractitionerPhone());
        }
        
        int updateResult = domesticTaskOrderMapper.updateById(updateDO);
        if (updateResult <= 0) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "家政服务订单更新失败");
        }
        
        // 5. 同步更新订单主表（如果需要）
        updateMainOrderIfNeeded(existingOrder.getOrderId(), reqDTO);
        
        // 6. 如果订单状态为已支付，更新支付流水表
        if ("paid".equals(reqDTO.getPaymentStatus()) && reqDTO.getReceivedAmount() != null) {
            updatePaymentRecordIfNeeded(existingOrder.getOrderId(), reqDTO);
        }
        
        // 7. 如果服务人员变更，记录人员变动日志
        if (practitionerChanged) {
            recordPersonnelChangeLog(existingOrder, originalOrder, reqDTO);
        }
        
        // 8. 设置操作日志上下文
        LogRecordContext.putVariable("existingOrder", existingOrder);
        LogRecordContext.putVariable("reqDTO", reqDTO);
        
        // 9. 手动记录操作日志（JSON格式）
        recordOrderUpdateLog(reqDTO, originalOrder, existingOrder);
        
        log.info("家政服务订单更新成功，订单ID: {}", reqDTO.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDomesticTaskOrder(Long id) {
        log.info("开始删除家政服务订单，订单ID: {}", id);
        
        // 1. 验证订单是否存在
        DomesticTaskOrderDO existingOrder = domesticTaskOrderMapper.selectById(id);
        if (existingOrder == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "家政服务订单不存在");
        }
        
        // 2. 删除家政服务订单详情
        int deleteResult = domesticTaskOrderMapper.deleteById(id);
        if (deleteResult <= 0) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "家政服务订单删除失败");
        }
        
        // 3. 删除订单主表记录
        if (existingOrder.getOrderId() != null) {
            publicbizOrderMapper.deleteById(existingOrder.getOrderId());
        }
        
        log.info("家政服务订单删除成功，订单ID: {}", id);
        return true;
    }

    @Override
    public DomesticTaskOrderDetailRespDTO getDomesticTaskOrderDetail(Long id) {
        log.info("查询家政服务订单详情，订单ID: {}", id);
        
        // 1. 查询家政服务订单详情
        DomesticTaskOrderDO domesticOrder = domesticTaskOrderMapper.selectById(id);
        if (domesticOrder == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "家政服务订单不存在");
        }
        
        // 2. 查询订单主表信息
        PublicbizOrderDO mainOrder = null;
        if (domesticOrder.getOrderId() != null) {
            mainOrder = publicbizOrderMapper.selectById(domesticOrder.getOrderId());
        }
        
        // 3. 构建详情响应
        return buildDetailResponse(domesticOrder, mainOrder);
    }

    @Override
    public DomesticTaskOrderExportRespDTO exportDomesticTaskOrder(DomesticTaskOrderExportReqDTO reqDTO) {
        log.info("开始导出家政服务订单列表");
        
        // TODO: 实现导出逻辑
        // 1. 根据条件查询订单列表
        // 2. 生成Excel文件
        // 3. 上传到文件服务器
        // 4. 返回下载链接
        
        DomesticTaskOrderExportRespDTO respDTO = new DomesticTaskOrderExportRespDTO();
        respDTO.setFileUrl("https://example.com/export/domestic-task-order.xlsx");
        respDTO.setFileName("家政服务订单列表.xlsx");
        
        log.info("家政服务订单列表导出成功");
        return respDTO;
    }

    @Override
    public DomesticTaskOrderRespDTO getDomesticTaskOrderByOrderId(Long orderId) {
        DomesticTaskOrderDO domesticOrder = domesticTaskOrderMapper.selectByOrderId(orderId);
        if (domesticOrder == null) {
            return null;
        }
        return DomesticTaskOrderConvert.INSTANCE.convert(domesticOrder);
    }

    @Override
    public DomesticTaskOrderRespDTO getDomesticTaskOrderByOrderNo(String orderNo) {
        DomesticTaskOrderDO domesticOrder = domesticTaskOrderMapper.selectByOrderNo(orderNo);
        if (domesticOrder == null) {
            return null;
        }
        return DomesticTaskOrderConvert.INSTANCE.convert(domesticOrder);
    }

    @Override
    public Boolean updateTaskStatistics(Long orderId, Integer completedTasks, Integer totalTasks) {
        log.info("更新任务统计信息，订单ID: {}, 已完成: {}, 总数: {}", orderId, completedTasks, totalTasks);
        
        BigDecimal taskProgress = totalTasks > 0 ? 
            new BigDecimal(completedTasks).divide(new BigDecimal(totalTasks), 2, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100")) : BigDecimal.ZERO;
        
        int updateResult = domesticTaskOrderMapper.updateTaskStatistics(orderId, completedTasks, totalTasks, taskProgress);
        return updateResult > 0;
    }

    // ========== 私有方法 ==========

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "DS" + DateUtil.format(new Date(), "yyyyMMdd") + String.format("%04d", new Random().nextInt(10000));
    }

    /**
     * 创建订单主表记录
     */
    private PublicbizOrderDO createMainOrder(DomesticTaskOrderSaveReqDTO reqDTO) {
        PublicbizOrderDO orderDO = new PublicbizOrderDO();
        orderDO.setOrderNo(generateOrderNo());
        orderDO.setOrderType("domestic");
        orderDO.setBusinessLine("家政服务");
        orderDO.setOpportunityId(reqDTO.getBusinessOpportunity());
        orderDO.setLeadId(reqDTO.getLead());
        orderDO.setProjectName(reqDTO.getCustomerName() + "的" + getServiceTypeName(reqDTO.getServiceType()) + "订单");
        orderDO.setProjectDescription(reqDTO.getRemark());
        orderDO.setStartDate(reqDTO.getServiceStartDate());
        orderDO.setEndDate(reqDTO.getServiceEndDate());
        orderDO.setTotalAmount(reqDTO.getTotalAmount());
        
        // 根据支付状态设置已支付金额
        if ("paid".equals(reqDTO.getPaymentStatus()) && reqDTO.getReceivedAmount() != null) {
            orderDO.setPaidAmount(reqDTO.getReceivedAmount());
        } else {
            orderDO.setPaidAmount(BigDecimal.ZERO);
        }
        
        orderDO.setRefundAmount(BigDecimal.ZERO);
        orderDO.setPaymentStatus(reqDTO.getPaymentStatus() != null ? reqDTO.getPaymentStatus() : "pending");
        orderDO.setOrderStatus("pending");
        orderDO.setCreateTime(LocalDateTime.now());
        orderDO.setUpdateTime(LocalDateTime.now());
        
        publicbizOrderMapper.insert(orderDO);
        return orderDO;
    }

    /**
     * 创建家政服务订单详情记录
     */
    private DomesticTaskOrderDO createDomesticOrderDetail(PublicbizOrderDO orderDO, DomesticTaskOrderSaveReqDTO reqDTO) {
        DomesticTaskOrderDO domesticOrderDO = DomesticTaskOrderConvert.INSTANCE.convert(reqDTO);
        domesticOrderDO.setOrderId(orderDO.getId());
        domesticOrderDO.setOrderNo(orderDO.getOrderNo());
        
        // 设置客户地址（必填字段）
        domesticOrderDO.setCustomerAddress(reqDTO.getServiceAddress());
        
        // 设置客户OneID（必填字段，如果没有则使用默认值）
        if (StrUtil.isBlank(domesticOrderDO.getCustomerOneid())) {
            domesticOrderDO.setCustomerOneid("");
        }
        
        // 设置服务分类ID（必填字段）
        if (reqDTO.getServiceCategoryId() != null) {
            domesticOrderDO.setServiceCategoryId(reqDTO.getServiceCategoryId());
        } else {
            domesticOrderDO.setServiceCategoryId(1L); // 默认分类ID
        }
        
        // 设置服务套餐ID
        if (reqDTO.getServicePackageId() != null) {
            domesticOrderDO.setServicePackageId(reqDTO.getServicePackageId());
        }
        
        // 设置服务套餐名称
        if (StrUtil.isNotBlank(reqDTO.getServicePackageName())) {
            domesticOrderDO.setServicePackageName(reqDTO.getServicePackageName());
        }
        
        // 设置服务套餐价格（必填字段）
        if (domesticOrderDO.getServicePackagePrice() == null) {
            domesticOrderDO.setServicePackagePrice(reqDTO.getUnitPrice());
        }
        
        // 设置价格单位（必填字段）
        if (StrUtil.isBlank(domesticOrderDO.getServicePackageUnit())) {
            domesticOrderDO.setServicePackageUnit("次");
        }
        
        // 设置套餐类型（必填字段）
        if (StrUtil.isBlank(domesticOrderDO.getServicePackageType())) {
            domesticOrderDO.setServicePackageType("count-card");
        }
        
        // 设置单价（必填字段）
        if (domesticOrderDO.getUnitPrice() == null) {
            domesticOrderDO.setUnitPrice(reqDTO.getUnitPrice());
        }
        
        // 设置总金额（必填字段）
        if (domesticOrderDO.getTotalAmount() == null) {
            domesticOrderDO.setTotalAmount(reqDTO.getTotalAmount());
        }
        
        // 设置实付金额（必填字段）
        if (domesticOrderDO.getActualAmount() == null) {
            domesticOrderDO.setActualAmount(reqDTO.getActualAmount());
        }
        
        // 设置服务地址（必填字段）
        if (StrUtil.isBlank(domesticOrderDO.getServiceAddress())) {
            domesticOrderDO.setServiceAddress(reqDTO.getServiceAddress());
        }
        
        // 设置服务人员信息
        if (StrUtil.isNotBlank(reqDTO.getPractitionerName())) {
            domesticOrderDO.setPractitionerName(reqDTO.getPractitionerName());
        }
        if (StrUtil.isNotBlank(reqDTO.getPractitionerPhone())) {
            domesticOrderDO.setPractitionerPhone(reqDTO.getPractitionerPhone());
        }
        if (StrUtil.isNotBlank(reqDTO.getPractitionerOneid())) {
            domesticOrderDO.setPractitionerOneid(reqDTO.getPractitionerOneid());
        }
        
        // 设置服务机构信息
        if (reqDTO.getAgencyId() != null) {
            domesticOrderDO.setAgencyId(reqDTO.getAgencyId());
        }
        if (StrUtil.isNotBlank(reqDTO.getAgencyName())) {
            domesticOrderDO.setAgencyName(reqDTO.getAgencyName());
        }
        
        // 设置服务类型名称
        if (StrUtil.isNotBlank(reqDTO.getServiceCategoryName())) {
            domesticOrderDO.setServiceCategoryName(reqDTO.getServiceCategoryName());
        } else {
            domesticOrderDO.setServiceCategoryName(getServiceTypeName(reqDTO.getServiceType()));
        }
        
        // 设置默认值
        domesticOrderDO.setTaskCount(0);
        domesticOrderDO.setCompletedTaskCount(0);
        domesticOrderDO.setTaskProgress(BigDecimal.ZERO);
        domesticOrderDO.setCreateTime(LocalDateTime.now());
        domesticOrderDO.setUpdateTime(LocalDateTime.now());
        
        domesticTaskOrderMapper.insert(domesticOrderDO);
        return domesticOrderDO;
    }

    /**
     * 更新订单主表（如果需要）
     */
    private void updateMainOrderIfNeeded(Long orderId, DomesticTaskOrderUpdateReqDTO reqDTO) {
        if (orderId == null) {
            return;
        }
        
        PublicbizOrderDO mainOrder = publicbizOrderMapper.selectById(orderId);
        if (mainOrder == null) {
            return;
        }
        
        boolean needUpdate = false;
        
        // 更新项目描述
        if (StrUtil.isNotBlank(reqDTO.getRemark())) {
            mainOrder.setProjectDescription(reqDTO.getRemark());
            needUpdate = true;
        }
        
        // 更新开始日期
        if (reqDTO.getServiceStartDate() != null) {
            mainOrder.setStartDate(reqDTO.getServiceStartDate());
            needUpdate = true;
        }
        
        // 更新结束日期
        if (reqDTO.getServiceEndDate() != null) {
            mainOrder.setEndDate(reqDTO.getServiceEndDate());
            needUpdate = true;
        }
        
        if (needUpdate) {
            mainOrder.setUpdateTime(LocalDateTime.now());
            publicbizOrderMapper.updateById(mainOrder);
        }
    }

    /**
     * 构建家政服务订单DO
     */
    private DomesticTaskOrderDO buildDomesticOrderDO(DomesticTaskOrderSaveReqDTO reqDTO, Long orderId) {
        DomesticTaskOrderDO domesticOrderDO = DomesticTaskOrderConvert.INSTANCE.convert(reqDTO);
        domesticOrderDO.setOrderId(orderId);
        domesticOrderDO.setOrderNo(generateOrderNo());
        domesticOrderDO.setCreateTime(LocalDateTime.now());
        domesticOrderDO.setUpdateTime(LocalDateTime.now());
        
        // 设置默认值
        domesticOrderDO.setTaskCount(0);
        domesticOrderDO.setCompletedTaskCount(0);
        domesticOrderDO.setTaskProgress(BigDecimal.ZERO);
        
        return domesticOrderDO;
    }

    /**
     * 构建订单详情响应
     */
    private DomesticTaskOrderDetailRespDTO buildDetailResponse(DomesticTaskOrderDO domesticOrder, PublicbizOrderDO mainOrder) {
        DomesticTaskOrderDetailRespDTO respDTO = new DomesticTaskOrderDetailRespDTO();
        
        // 订单基本信息
        DomesticTaskOrderDetailRespDTO.OrderInfo orderInfo = new DomesticTaskOrderDetailRespDTO.OrderInfo();
        orderInfo.setId(domesticOrder.getId());
        orderInfo.setOrderNumber(domesticOrder.getOrderNo());
        if (mainOrder != null) {
            orderInfo.setOrderStatus(mainOrder.getOrderStatus());
            orderInfo.setPaymentStatus(mainOrder.getPaymentStatus());
            // 添加商机ID和线索ID - 确保从主订单表获取
            orderInfo.setOpportunityId(mainOrder.getOpportunityId());
            orderInfo.setLeadId(mainOrder.getLeadId());
            // 添加订单金额
            orderInfo.setTotalAmount(mainOrder.getTotalAmount());
        } else {
            // 如果主订单为空，设置默认状态
            orderInfo.setOrderStatus("pending");
            orderInfo.setPaymentStatus("unpaid");
                    // 尝试从详情表获取商机ID和线索ID - 这些字段在主订单表中，详情表中没有
        // orderInfo.setOpportunityId(domesticOrder.getOpportunityId());
        // orderInfo.setLeadId(domesticOrder.getLeadId());
            orderInfo.setTotalAmount(domesticOrder.getTotalAmount());
        }
        // 添加服务套餐信息
        orderInfo.setServicePackage(domesticOrder.getServicePackageName());
        // 添加服务地址
        orderInfo.setServiceAddress(domesticOrder.getServiceAddress());
        // 添加阿姨oneid
        orderInfo.setPractitionerOneid(domesticOrder.getPractitionerOneid());
        // 添加预约时间 - 使用服务开始日期作为预约时间
        orderInfo.setAppointmentTime(domesticOrder.getServiceStartDate() != null ? 
            domesticOrder.getServiceStartDate().atStartOfDay() : null);
        // 添加服务开始日期
        orderInfo.setServiceStartDate(domesticOrder.getServiceStartDate() != null ? 
            domesticOrder.getServiceStartDate().toString() : null);
        // 添加服务结束日期
        orderInfo.setServiceEndDate(domesticOrder.getServiceEndDate() != null ? 
            domesticOrder.getServiceEndDate().toString() : null);
        // 添加服务时长
        orderInfo.setServiceDuration(domesticOrder.getServiceDuration());
        // 添加服务频次
        orderInfo.setServiceFrequency(domesticOrder.getServiceFrequency());
        // 添加服务描述
        orderInfo.setServiceDescription(domesticOrder.getServiceDescription());
        orderInfo.setCreateTime(domesticOrder.getCreateTime());
        orderInfo.setUpdateTime(domesticOrder.getUpdateTime());
        respDTO.setOrderInfo(orderInfo);
        
        // 客户信息
        DomesticTaskOrderDetailRespDTO.CustomerInfo customerInfo = new DomesticTaskOrderDetailRespDTO.CustomerInfo();
        customerInfo.setCustomerName(domesticOrder.getCustomerName());
        customerInfo.setCustomerPhone(domesticOrder.getCustomerPhone());
        customerInfo.setServiceAddress(domesticOrder.getServiceAddress());
        customerInfo.setCustomerRemark(domesticOrder.getCustomerRemark());
        respDTO.setCustomerInfo(customerInfo);
        
        // 服务信息
        DomesticTaskOrderDetailRespDTO.ServiceInfo serviceInfo = new DomesticTaskOrderDetailRespDTO.ServiceInfo();
        serviceInfo.setServiceType(domesticOrder.getServiceCategoryName());
        serviceInfo.setServicePackage(domesticOrder.getServicePackageName());
        serviceInfo.setServiceStartDate(domesticOrder.getServiceStartDate() != null ? 
            domesticOrder.getServiceStartDate().toString() : null);
        serviceInfo.setServiceEndDate(domesticOrder.getServiceEndDate() != null ? 
            domesticOrder.getServiceEndDate().toString() : null);
        serviceInfo.setServiceDuration(domesticOrder.getServiceDuration());
        serviceInfo.setServiceAmount(domesticOrder.getActualAmount());
        // 设置预约时间 - 使用服务开始日期作为预约时间
        serviceInfo.setAppointmentTime(domesticOrder.getServiceStartDate() != null ? 
            domesticOrder.getServiceStartDate().atStartOfDay() : null);
        serviceInfo.setServiceFrequency(domesticOrder.getServiceFrequency());
        serviceInfo.setServiceDescription(domesticOrder.getServiceDescription());
        respDTO.setServiceInfo(serviceInfo);
        
        // 支付信息 - 从订单支付表查询
        DomesticTaskOrderDetailRespDTO.PaymentInfo paymentInfo = new DomesticTaskOrderDetailRespDTO.PaymentInfo();
        if (mainOrder != null) {
            paymentInfo.setTotalAmount(mainOrder.getTotalAmount());
            paymentInfo.setPaidAmount(mainOrder.getPaidAmount());
            
            // 查询订单支付记录
            List<PublicbizOrderPaymentDO> paymentList = orderPaymentMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                    .eq(PublicbizOrderPaymentDO::getOrderId, mainOrder.getId())
                    .eq(PublicbizOrderPaymentDO::getPaymentStatus, "success")
                    .orderByDesc(PublicbizOrderPaymentDO::getPaymentTime)
            );
            
            if (!paymentList.isEmpty()) {
                // 使用最新的支付记录
                PublicbizOrderPaymentDO latestPayment = paymentList.get(0);
                paymentInfo.setPaymentMethod(getPaymentMethodDisplayName(latestPayment.getPaymentType()));
                paymentInfo.setPaymentTime(latestPayment.getPaymentTime());
                paymentInfo.setPaymentRemark(latestPayment.getPaymentRemark());
                
                // 添加收款金额（支付金额）
                paymentInfo.setReceivedAmount(latestPayment.getPaymentAmount());
                
                // 如果有多个支付记录，合并支付方式和金额
                if (paymentList.size() > 1) {
                    Set<String> paymentMethods = paymentList.stream()
                        .map(payment -> getPaymentMethodDisplayName(payment.getPaymentType()))
                        .collect(Collectors.toSet());
                    paymentInfo.setPaymentMethod(String.join("、", paymentMethods));
                    
                    // 计算总收款金额
                    BigDecimal totalReceivedAmount = paymentList.stream()
                        .map(PublicbizOrderPaymentDO::getPaymentAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    paymentInfo.setReceivedAmount(totalReceivedAmount);
                }
            } else {
                paymentInfo.setPaymentMethod("待支付");
                paymentInfo.setPaymentTime(null);
                paymentInfo.setPaymentRemark("暂无支付记录");
                paymentInfo.setReceivedAmount(BigDecimal.ZERO);
            }
        } else {
            paymentInfo.setTotalAmount(domesticOrder.getTotalAmount());
            paymentInfo.setPaidAmount(BigDecimal.ZERO);
            paymentInfo.setPaymentMethod("待支付");
            paymentInfo.setPaymentTime(null);
            paymentInfo.setPaymentRemark("暂无支付记录");
        }
        
        paymentInfo.setDiscountAmount(domesticOrder.getDiscountAmount());
        paymentInfo.setActualAmount(domesticOrder.getActualAmount());
        respDTO.setPaymentInfo(paymentInfo);
        
        // 任务信息
        DomesticTaskOrderDetailRespDTO.TaskInfo taskInfo = new DomesticTaskOrderDetailRespDTO.TaskInfo();
        taskInfo.setTotalTasks(domesticOrder.getTaskCount());
        taskInfo.setCompletedTasks(domesticOrder.getCompletedTaskCount());
        taskInfo.setTaskProgress(domesticOrder.getTaskProgress());
        respDTO.setTaskInfo(taskInfo);
        
        // 服务人员信息 - 完善服务人员详细信息
        DomesticTaskOrderDetailRespDTO.PractitionerInfo practitionerInfo = new DomesticTaskOrderDetailRespDTO.PractitionerInfo();
        practitionerInfo.setPractitionerOneid(domesticOrder.getPractitionerOneid());
        
        // 查询服务人员详细信息
        if (StrUtil.isNotBlank(domesticOrder.getPractitionerOneid())) {
            try {
                PractitionerDO practitioner = practitionerMapper.selectByOneid(domesticOrder.getPractitionerOneid());
                if (practitioner != null) {
                    // 优先使用查询到的详细信息
                    practitionerInfo.setPractitionerName(practitioner.getName());
                    practitionerInfo.setPractitionerPhone(practitioner.getPhone());
                    practitionerInfo.setRating(practitioner.getRating() != null ? practitioner.getRating() : new BigDecimal("4.5"));
                    practitionerInfo.setExperienceYears(practitioner.getExperienceYears() != null ? practitioner.getExperienceYears() : 3);
                    
                    log.info("使用查询到的服务人员详细信息: name={}, phone={}, rating={}, experienceYears={}", 
                        practitioner.getName(), practitioner.getPhone(), practitioner.getRating(), practitioner.getExperienceYears());
                } else {
                    // 如果查询不到详细信息，使用DomesticTaskOrderDO中的信息
                    practitionerInfo.setPractitionerName(domesticOrder.getPractitionerName());
                    practitionerInfo.setPractitionerPhone(domesticOrder.getPractitionerPhone());
                    practitionerInfo.setRating(new BigDecimal("4.5"));
                    practitionerInfo.setExperienceYears(3);
                    log.warn("未查询到服务人员详细信息，使用DomesticTaskOrderDO中的信息");
                }
            } catch (Exception e) {
                log.warn("查询服务人员详细信息失败: {}", e.getMessage());
                // 使用DomesticTaskOrderDO中的信息
                practitionerInfo.setPractitionerName(domesticOrder.getPractitionerName());
                practitionerInfo.setPractitionerPhone(domesticOrder.getPractitionerPhone());
                practitionerInfo.setRating(new BigDecimal("4.5"));
                practitionerInfo.setExperienceYears(3);
            }
        } else {
            // 使用DomesticTaskOrderDO中的信息
            practitionerInfo.setPractitionerName(domesticOrder.getPractitionerName());
            practitionerInfo.setPractitionerPhone(domesticOrder.getPractitionerPhone());
            practitionerInfo.setRating(new BigDecimal("4.5"));
            practitionerInfo.setExperienceYears(3);
            log.warn("服务人员OneID为空，使用DomesticTaskOrderDO中的信息");
        }
        respDTO.setPractitionerInfo(practitionerInfo);
        
        // 服务机构信息 - 完善服务机构详细信息
        DomesticTaskOrderDetailRespDTO.AgencyInfo agencyInfo = new DomesticTaskOrderDetailRespDTO.AgencyInfo();
        agencyInfo.setAgencyId(domesticOrder.getAgencyId());
        
        // 查询服务机构详细信息
        if (domesticOrder.getAgencyId() != null) {
            try {
                AgencyDO agency = agencyMapper.selectById(domesticOrder.getAgencyId());
                if (agency != null) {
                    // 优先使用查询到的详细信息
                    agencyInfo.setAgencyName(agency.getAgencyName());
                    agencyInfo.setAgencyCode(agency.getAgencyNo());
                    agencyInfo.setAgencyType(agency.getAgencyType());
                    agencyInfo.setCooperationStatus(agency.getCooperationStatus());
                    agencyInfo.setContactPerson(agency.getContactPerson());
                    agencyInfo.setContactPhone(agency.getContactPhone());
                    
                    log.info("使用查询到的机构详细信息: name={}, code={}, type={}, contactPerson={}, contactPhone={}", 
                        agency.getAgencyName(), agency.getAgencyNo(), agency.getAgencyType(), 
                        agency.getContactPerson(), agency.getContactPhone());
                } else {
                    // 如果查询不到详细信息，使用DomesticTaskOrderDO中的信息
                    agencyInfo.setAgencyName(domesticOrder.getAgencyName());
                    agencyInfo.setAgencyCode(domesticOrder.getAgencyId().toString());
                    agencyInfo.setAgencyType("cooperation");
                    agencyInfo.setCooperationStatus("cooperating");
                    agencyInfo.setContactPerson("李经理");
                    agencyInfo.setContactPhone("13812345678");
                    log.warn("未查询到机构详细信息，使用DomesticTaskOrderDO中的信息");
                }
            } catch (Exception e) {
                log.warn("查询服务机构详细信息失败: {}", e.getMessage());
                // 使用DomesticTaskOrderDO中的信息
                agencyInfo.setAgencyName(domesticOrder.getAgencyName());
                agencyInfo.setAgencyCode(domesticOrder.getAgencyId().toString());
                agencyInfo.setAgencyType("cooperation");
                agencyInfo.setCooperationStatus("cooperating");
                agencyInfo.setContactPerson("李经理");
                agencyInfo.setContactPhone("13812345678");
            }
        } else {
            // 使用DomesticTaskOrderDO中的信息
            agencyInfo.setAgencyName(domesticOrder.getAgencyName());
            agencyInfo.setAgencyCode("");
            agencyInfo.setAgencyType("cooperation");
            agencyInfo.setCooperationStatus("cooperating");
            agencyInfo.setContactPerson("李经理");
            agencyInfo.setContactPhone("13812345678");
            log.warn("机构ID为空，使用DomesticTaskOrderDO中的信息");
        }
        respDTO.setAgencyInfo(agencyInfo);
        
        log.info("最终设置的服务人员信息: {}", practitionerInfo);
        log.info("最终设置的机构信息: {}", agencyInfo);
        
        return respDTO;
    }

    /**
     * 获取服务类型名称
     */
    private String getServiceTypeName(String serviceType) {
        if (StrUtil.isBlank(serviceType)) {
            return "未知类型";
        }
        
        switch (serviceType) {
            case "maternity":
                return "月嫂服务";
            case "deep_cleaning":
                return "深度保洁";
            case "hourly":
                return "小时工";
            case "nanny":
                return "育儿嫂服务";
            default:
                return serviceType;
        }
    }

    /**
     * 获取支付方式显示名称
     */
    private String getPaymentMethodDisplayName(String paymentType) {
        if (StrUtil.isBlank(paymentType)) {
            return "未知";
        }
        
        switch (paymentType) {
            case "cash":
                return "现金";
            case "wechat":
                return "微信支付";
            case "alipay":
                return "支付宝";
            case "bank_transfer":
                return "银行转账";
            case "pos":
                return "POS机刷卡";
            case "other":
                return "其他";
            default:
                return paymentType;
        }
    }

    // ========== 任务管理接口实现 ==========

    @Override
    public PageResult<Map<String, Object>> pageTasks(Long orderId, Integer page, Integer size, String taskStatus, String executor) {
        // 计算偏移量
        int offset = (page - 1) * size;
        
        // 查询任务列表
        List<DomesticTaskDO> taskList = domesticTaskMapper.selectByOrderIdWithConditions(orderId, taskStatus, executor, offset, size);
        
        // 查询总数
        Long total = domesticTaskMapper.countByOrderIdWithConditions(orderId, taskStatus, executor);
        
        // 转换为Map格式
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (DomesticTaskDO task : taskList) {
            Map<String, Object> taskMap = new HashMap<>();
            taskMap.put("id", task.getId());
            taskMap.put("taskNo", task.getTaskNo());
            taskMap.put("taskSequence", task.getTaskSequence());
            taskMap.put("taskName", task.getTaskName());
            taskMap.put("taskDescription", task.getTaskDescription());
            taskMap.put("taskType", task.getTaskType());
            taskMap.put("taskStatus", task.getTaskStatus());
            taskMap.put("plannedStartTime", task.getPlannedStartTime());
            taskMap.put("plannedEndTime", task.getPlannedEndTime());
            taskMap.put("actualStartTime", task.getActualStartTime());
            taskMap.put("actualEndTime", task.getActualEndTime());
            taskMap.put("duration", task.getDuration());
            taskMap.put("practitionerOneid", task.getPractitionerOneid());
            taskMap.put("practitionerName", task.getPractitionerName());
            taskMap.put("practitionerPhone", task.getPractitionerPhone());
            taskMap.put("scheduleDate", task.getScheduleDate());
            taskMap.put("serviceCategoryName", task.getServiceCategoryName());
            taskMap.put("customerName", task.getCustomerName());
            taskMap.put("customerPhone", task.getCustomerPhone());
            taskMap.put("serviceAddress", task.getServiceAddress());
            taskMap.put("punchInTime", task.getPunchInTime());
            taskMap.put("punchOutTime", task.getPunchOutTime());
            taskMap.put("punchLocation", task.getPunchLocation());
            taskMap.put("createTime", task.getCreateTime());
            taskMap.put("updateTime", task.getUpdateTime());
            
            resultList.add(taskMap);
        }
        
        return new PageResult<>(resultList, total);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeTask(Map<String, Object> reqVO) {
        Long taskId = Long.valueOf(reqVO.get("taskId").toString());
        String certificateUrl = (String) reqVO.get("certificateUrl");
        String remark = (String) reqVO.get("remark");
        
        // 查询任务
        DomesticTaskDO task = domesticTaskMapper.selectById(taskId);
        if (task == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务不存在");
        }
        
        // 检查任务状态
        if (!"in_progress".equals(task.getTaskStatus())) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务状态不正确，无法完成");
        }
        
        // 更新任务状态为已完成
        LocalDateTime now = LocalDateTime.now();
        int updateResult = domesticTaskMapper.updateTaskStatus(taskId, "completed", task.getActualStartTime(), now);
        
        if (updateResult > 0) {
            // 更新订单的任务统计信息
            updateTaskStatistics(task.getOrderId(), null, null);
            
            // TODO: 保存完成凭证信息到相关表
            log.info("任务完成，任务ID: {}, 凭证URL: {}, 备注: {}", taskId, certificateUrl, remark);
            
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean assignTask(Map<String, Object> reqVO) {
        Long taskId = Long.valueOf(reqVO.get("taskId").toString());
        String practitionerOneid = (String) reqVO.get("practitionerOneid");
        String practitionerName = (String) reqVO.get("practitionerName");
        String practitionerPhone = (String) reqVO.get("practitionerPhone");
        
        // 查询任务
        DomesticTaskDO task = domesticTaskMapper.selectById(taskId);
        if (task == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务不存在");
        }
        
        // 检查任务状态
        if (!"pending".equals(task.getTaskStatus())) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务状态不正确，无法指派");
        }
        
        // 更新任务状态为已分配
        int updateResult = domesticTaskMapper.updateTaskStatus(taskId, "assigned", null, null);
        
        if (updateResult > 0) {
            // 更新服务人员信息
            domesticTaskMapper.updatePunchInfo(taskId, null, null, null, null, null);
            
            // 更新服务人员信息（这里需要添加一个新的更新方法）
            // TODO: 添加更新服务人员信息的方法
            
            log.info("任务指派成功，任务ID: {}, 服务人员: {}", taskId, practitionerName);
            
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelTask(Map<String, Object> reqVO) {
        Long taskId = Long.valueOf(reqVO.get("taskId").toString());
        String cancelReason = (String) reqVO.get("cancelReason");
        
        // 查询任务
        DomesticTaskDO task = domesticTaskMapper.selectById(taskId);
        if (task == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务不存在");
        }
        
        // 检查任务状态
        if ("completed".equals(task.getTaskStatus()) || "cancelled".equals(task.getTaskStatus())) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务状态不正确，无法取消");
        }
        
        // 更新任务状态为已取消
        int updateResult = domesticTaskMapper.updateTaskStatus(taskId, "cancelled", null, null);
        
        if (updateResult > 0) {
            // 更新订单的任务统计信息
            updateTaskStatistics(task.getOrderId(), null, null);
            
            log.info("任务取消成功，任务ID: {}, 取消原因: {}", taskId, cancelReason);
            
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editTask(Map<String, Object> reqVO) {
        Long taskId = Long.valueOf(reqVO.get("taskId").toString());
        String taskName = (String) reqVO.get("taskName");
        String taskDescription = (String) reqVO.get("taskDescription");
        LocalDateTime plannedStartTime = reqVO.get("plannedStartTime") != null ? 
            LocalDateTime.parse(reqVO.get("plannedStartTime").toString()) : null;
        LocalDateTime plannedEndTime = reqVO.get("plannedEndTime") != null ? 
            LocalDateTime.parse(reqVO.get("plannedEndTime").toString()) : null;
        
        // 查询任务
        DomesticTaskDO task = domesticTaskMapper.selectById(taskId);
        if (task == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务不存在");
        }
        
        // 检查任务状态
        if ("completed".equals(task.getTaskStatus()) || "cancelled".equals(task.getTaskStatus())) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务状态不正确，无法编辑");
        }
        
        // 更新任务信息
        int updateResult = domesticTaskMapper.updateTaskInfo(taskId, taskName, taskDescription, plannedStartTime, plannedEndTime);
        
        if (updateResult > 0) {
            log.info("任务编辑成功，任务ID: {}, 任务名称: {}", taskId, taskName);
            
            return true;
        }
        
        return false;
    }

    @Override
    public Map<String, Object> getTaskCertificate(String taskId) {
        // 查询任务
        DomesticTaskDO task = domesticTaskMapper.selectById(Long.valueOf(taskId));
        if (task == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务不存在");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("taskId", task.getId());
        result.put("taskNo", task.getTaskNo());
        result.put("taskName", task.getTaskName());
        result.put("taskStatus", task.getTaskStatus());
        result.put("practitionerName", task.getPractitionerName());
        result.put("actualStartTime", task.getActualStartTime());
        result.put("actualEndTime", task.getActualEndTime());
        result.put("punchInTime", task.getPunchInTime());
        result.put("punchOutTime", task.getPunchOutTime());
        result.put("punchLocation", task.getPunchLocation());
        
        // TODO: 从相关表查询完成凭证信息
        result.put("certificateUrl", "https://example.com/certificate.jpg");
        result.put("certificateRemark", "任务完成凭证");
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchReassignTasks(Map<String, Object> reqVO) {
        @SuppressWarnings("unchecked")
        List<String> taskNoList = (List<String>) reqVO.get("taskNoList");
        String practitionerOneid = (String) reqVO.get("practitionerOneid");
        String practitionerName = (String) reqVO.get("practitionerName");
        String practitionerPhone = (String) reqVO.get("practitionerPhone");
        
        if (taskNoList == null || taskNoList.isEmpty()) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "任务列表不能为空");
        }
        
        // 批量更新服务人员信息
        int updateResult = domesticTaskMapper.updatePractitionerByTaskNoList(taskNoList, practitionerOneid, practitionerName, practitionerPhone);
        
        Map<String, Object> result = new HashMap<>();
        result.put("successCount", updateResult);
        result.put("failCount", taskNoList.size() - updateResult);
        result.put("totalCount", taskNoList.size());
        
        log.info("批量重指派任务完成，成功: {}, 失败: {}, 总计: {}", updateResult, taskNoList.size() - updateResult, taskNoList.size());
        
        return result;
    }

    // ========== 收款信息管理接口实现 ==========

    @Override
    public Map<String, Object> getPaymentInfo(String orderId) {
        log.info("查询订单收款信息，订单ID: {}", orderId);
        
        // 查询订单支付记录
        List<PublicbizOrderPaymentDO> paymentList = orderPaymentMapper.selectByOrderId(Long.valueOf(orderId));
        
        // 统计支付总金额
        BigDecimal totalPaidAmount = orderPaymentMapper.sumPaymentAmountByOrderId(Long.valueOf(orderId));
        if (totalPaidAmount == null) {
            totalPaidAmount = BigDecimal.ZERO;
        }
        
        // 查询订单信息 - 修改查询方式，直接使用订单ID查询
        DomesticTaskOrderDO order = domesticTaskOrderMapper.selectById(Long.valueOf(orderId));
        if (order == null) {
            // 如果查不到，尝试通过orderId查询
            order = domesticTaskOrderMapper.selectByOrderId(Long.valueOf(orderId));
        }
        
        BigDecimal totalAmount = order != null ? order.getTotalAmount() : BigDecimal.ZERO;
        
        Map<String, Object> result = new HashMap<>();
        result.put("orderId", orderId);
        result.put("totalAmount", totalAmount);
        result.put("totalPaidAmount", totalPaidAmount);
        result.put("unpaidAmount", totalAmount.subtract(totalPaidAmount));
        result.put("paymentRecords", paymentList);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addPayment(Map<String, Object> reqVO) {
        String orderId = reqVO.get("orderId").toString();
        String paymentType = (String) reqVO.get("paymentType");
        BigDecimal paymentAmount = new BigDecimal(reqVO.get("paymentAmount").toString());
        String paymentRemark = (String) reqVO.get("paymentRemark");
        String transactionId = (String) reqVO.get("transactionId");
        
        log.info("新增收款信息，订单ID: {}, 支付类型: {}, 金额: {}", orderId, paymentType, paymentAmount);
        
        // 查询订单信息 - 修改查询方式，直接使用订单ID查询
        DomesticTaskOrderDO order = domesticTaskOrderMapper.selectById(Long.valueOf(orderId));
        if (order == null) {
            // 如果查不到，尝试通过orderId查询
            order = domesticTaskOrderMapper.selectByOrderId(Long.valueOf(orderId));
        }
        if (order == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "订单不存在");
        }
        
        // 检查是否已存在相同的支付记录（防止重复创建）
        LambdaQueryWrapperX<PublicbizOrderPaymentDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(PublicbizOrderPaymentDO::getOrderId, Long.valueOf(orderId));
        queryWrapper.eq(PublicbizOrderPaymentDO::getPaymentType, paymentType);
        queryWrapper.eq(PublicbizOrderPaymentDO::getPaymentAmount, paymentAmount);
        
        // 检查最近5分钟内是否有相同的支付记录
        queryWrapper.ge(PublicbizOrderPaymentDO::getCreateTime, LocalDateTime.now().minusMinutes(5));
        
        List<PublicbizOrderPaymentDO> existingPayments = orderPaymentMapper.selectList(queryWrapper);
        if (!existingPayments.isEmpty()) {
            log.warn("检测到重复的支付记录，订单ID: {}, 支付类型: {}, 金额: {}", orderId, paymentType, paymentAmount);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "支付记录已存在，请勿重复提交");
            return result;
        }
        
        // 创建支付记录
        PublicbizOrderPaymentDO paymentDO = PublicbizOrderPaymentDO.builder()
                .orderId(Long.valueOf(orderId))
                .orderNo(order.getOrderNo())
                .paymentNo(generatePaymentNo())
                .paymentType(paymentType)
                .paymentAmount(paymentAmount)
                .paymentStatus("success")
                .paymentTime(LocalDateTime.now())
                .operatorId(SecurityFrameworkUtils.getLoginUserId())
                .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                .paymentRemark(paymentRemark)
                .transactionId(transactionId)
                .build();
        
        int insertResult = orderPaymentMapper.insert(paymentDO);
        if (insertResult <= 0) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "支付记录创建失败");
        }
        
        // 记录操作日志（JSON格式）
        recordPaymentLog(order, paymentDO, "新增收款");
        
        // 更新订单支付状态
        updateOrderPaymentStatus(Long.valueOf(orderId), paymentAmount);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "收款信息添加成功");
        result.put("paymentId", paymentDO.getId());
        
        log.info("收款信息添加成功，订单ID: {}, 支付记录ID: {}", orderId, paymentDO.getId());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePayment(Map<String, Object> reqVO) {
        Long paymentId = Long.valueOf(reqVO.get("paymentId").toString());
        String paymentType = (String) reqVO.get("paymentType");
        BigDecimal paymentAmount = new BigDecimal(reqVO.get("paymentAmount").toString());
        String paymentRemark = (String) reqVO.get("paymentRemark");
        String transactionId = (String) reqVO.get("transactionId");
        
        log.info("修改收款信息，支付ID: {}", paymentId);
        
        // 查询支付记录
        PublicbizOrderPaymentDO payment = orderPaymentMapper.selectById(paymentId);
        if (payment == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "支付记录不存在");
        }
        
        // 保存原始数据用于比较
        PublicbizOrderPaymentDO originalPayment = new PublicbizOrderPaymentDO();
        BeanUtils.copyProperties(payment, originalPayment);
        
        // 更新支付记录
        payment.setPaymentType(paymentType);
        payment.setPaymentAmount(paymentAmount);
        payment.setPaymentRemark(paymentRemark);
        payment.setTransactionId(transactionId);
        payment.setUpdateTime(LocalDateTime.now());
        payment.setUpdater(SecurityFrameworkUtils.getLoginUserId().toString());
        
        int updateResult = orderPaymentMapper.updateById(payment);
        if (updateResult <= 0) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "支付记录更新失败");
        }
        
        // 查询订单信息用于日志记录
        DomesticTaskOrderDO order = domesticTaskOrderMapper.selectByOrderId(payment.getOrderId());
        
        // 记录操作日志（JSON格式）
        recordPaymentUpdateLog(order, originalPayment, payment);
        
        // 更新订单支付状态
        updateOrderPaymentStatus(payment.getOrderId(), paymentAmount);
        
        log.info("收款信息修改成功，支付ID: {}", paymentId);
        return true;
    }

    // ========== 收支记录管理接口实现 ==========

    @Override
    public Map<String, Object> getIncomeExpenseList(String orderId) {
        log.info("查询订单收支记录列表，订单ID: {}", orderId);
        
        // 查询支付记录作为收支记录
        List<PublicbizOrderPaymentDO> paymentList = orderPaymentMapper.selectList(
            new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                .eq(PublicbizOrderPaymentDO::getOrderId, Long.valueOf(orderId))
                .orderByDesc(PublicbizOrderPaymentDO::getCreateTime)
        );
        
        // 转换为收支记录格式
        List<Map<String, Object>> records = new ArrayList<>();
        for (PublicbizOrderPaymentDO record : paymentList) {
            Map<String, Object> recordMap = new HashMap<>();
            recordMap.put("id", record.getId());
            recordMap.put("recordNo", record.getPaymentNo());
            recordMap.put("recordType", convertPaymentTypeToRecordType(record.getPaymentType()));
            recordMap.put("recordDirection", "income"); // 支付记录都是收入
            recordMap.put("amount", record.getPaymentAmount());
            recordMap.put("description", record.getPaymentRemark());
            recordMap.put("operator", record.getOperatorName());
            recordMap.put("createTime", record.getCreateTime());
            recordMap.put("remark", record.getPaymentRemark());
            recordMap.put("recordDate", record.getPaymentTime() != null ? record.getPaymentTime().toLocalDate() : null);
            recordMap.put("recordTime", record.getPaymentTime());
            records.add(recordMap);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", records);
        result.put("totalCount", records.size());
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addIncomeExpense(Map<String, Object> reqVO) {
        String orderId = reqVO.get("orderId").toString();
        String recordType = (String) reqVO.get("recordType"); // 使用新的收支记录类型
        BigDecimal amount = new BigDecimal(reqVO.get("amount").toString());
        String description = (String) reqVO.get("description");
        String remark = (String) reqVO.get("remark");
        
        log.info("新增收支记录，订单ID: {}, 类型: {}, 金额: {}", orderId, recordType, amount);
        
        // 查询订单信息
        DomesticTaskOrderDO order = domesticTaskOrderMapper.selectById(Long.valueOf(orderId));
        if (order == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "订单不存在");
        }
        
        // 根据记录类型判断是收入还是支出，并调整金额符号
        IncomeExpenseTypeEnum typeEnum = IncomeExpenseTypeEnum.getByCode(recordType);
        if (typeEnum != null && "expense".equals(typeEnum.getDirection())) {
            // 支出类型，金额为负数
            amount = amount.negate();
            log.info("支出类型，调整金额为负数: {}", amount);
        }
        
        // 创建支付记录作为收支记录
        PublicbizOrderPaymentDO paymentDO = PublicbizOrderPaymentDO.builder()
                .orderId(Long.valueOf(orderId))
                .orderNo(order.getOrderNo())
                .paymentNo(generatePaymentNo())
                .paymentType(convertRecordTypeToPaymentType(recordType))
                .paymentAmount(amount)
                .paymentStatus("success") // 收支记录默认为成功状态
                .paymentTime(LocalDateTime.now())
                .operatorId(SecurityFrameworkUtils.getLoginUserId())
                .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                .paymentRemark(description != null ? description : remark)
                .build();
        
        int insertResult = orderPaymentMapper.insert(paymentDO);
        if (insertResult <= 0) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "收支记录创建失败");
        }
        
        // 记录操作日志
        recordPaymentLog(order, paymentDO, "新增收支记录");
        
        // 更新订单已支付金额
        updateOrderPaidAmount(Long.valueOf(orderId));
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "收支记录添加成功");
        result.put("recordId", paymentDO.getId());
        
        log.info("收支记录添加成功，记录ID: {}", paymentDO.getId());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateIncomeExpense(Map<String, Object> reqVO) {
        Long recordId = Long.valueOf(reqVO.get("recordId").toString());
        String recordType = (String) reqVO.get("recordType");
        BigDecimal amount = reqVO.get("amount") != null ? new BigDecimal(reqVO.get("amount").toString()) : null;
        String description = (String) reqVO.get("description");
        String remark = (String) reqVO.get("remark");
        
        log.info("修改收支记录，记录ID: {}", recordId);
        
        // 查询支付记录
        PublicbizOrderPaymentDO record = orderPaymentMapper.selectById(recordId);
        if (record == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "收支记录不存在");
        }
        
        // 保存原始数据用于比较
        PublicbizOrderPaymentDO originalRecord = new PublicbizOrderPaymentDO();
        BeanUtils.copyProperties(record, originalRecord);
        
        // 更新支付记录
        if (amount != null) {
            // 根据记录类型判断是收入还是支出，并调整金额符号
            IncomeExpenseTypeEnum typeEnum = IncomeExpenseTypeEnum.getByCode(recordType);
            if (typeEnum != null && "expense".equals(typeEnum.getDirection())) {
                // 支出类型，金额为负数
                amount = amount.negate();
                log.info("支出类型，调整金额为负数: {}", amount);
            }
            record.setPaymentAmount(amount);
        }
        if (description != null || remark != null) {
            record.setPaymentRemark(description != null ? description : remark);
        }
        record.setUpdateTime(LocalDateTime.now());
        record.setUpdater(SecurityFrameworkUtils.getLoginUserId().toString());
        
        int updateResult = orderPaymentMapper.updateById(record);
        if (updateResult <= 0) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "收支记录更新失败");
        }
        
        // 查询订单信息用于日志记录
        DomesticTaskOrderDO order = domesticTaskOrderMapper.selectById(record.getOrderId());
        
        // 记录操作日志
        recordPaymentUpdateLog(order, originalRecord, record);
        
        // 更新订单已支付金额
        updateOrderPaidAmount(record.getOrderId());
        
        log.info("收支记录修改成功，记录ID: {}", recordId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteIncomeExpense(Map<String, Object> reqVO) {
        Long recordId = Long.valueOf(reqVO.get("recordId").toString());
        
        log.info("删除收支记录，记录ID: {}", recordId);
        
        // 查询支付记录
        PublicbizOrderPaymentDO record = orderPaymentMapper.selectById(recordId);
        if (record == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "收支记录不存在");
        }
        
        // 查询订单信息用于日志记录
        DomesticTaskOrderDO order = domesticTaskOrderMapper.selectById(record.getOrderId());
        
        // 删除支付记录
        int deleteResult = orderPaymentMapper.deleteById(recordId);
        if (deleteResult > 0) {
            // 记录操作日志
            recordPaymentLog(order, record, "删除收支记录");
            
            // 更新订单已支付金额
            updateOrderPaidAmount(record.getOrderId());
            
            log.info("收支记录删除成功，记录ID: {}", recordId);
            return true;
        }
        return false;
    }

    // ========== 服务评价管理接口实现 ==========

    @Override
    public Map<String, Object> getEvaluationInfo(String orderId) {
        log.info("查询订单服务评价信息，订单ID: {}", orderId);
        
        // 查询评价信息
        OrderEvaluationDO evaluation = orderEvaluationMapper.selectByOrderId(Long.valueOf(orderId));
        
        Map<String, Object> result = new HashMap<>();
        if (evaluation != null) {
            // 映射新字段到兼容字段
            result.put("evaluationId", evaluation.getId());
            result.put("orderId", evaluation.getOrderId());
            result.put("customerName", evaluation.getReviewerName()); // 评价人姓名作为客户姓名
            result.put("practitionerName", evaluation.getAuntId()); // 阿姨OneID作为服务人员姓名
            result.put("serviceRating", evaluation.getRating()); // 使用rating作为服务评分
            result.put("attitudeRating", evaluation.getRating()); // 使用rating作为态度评分
            result.put("professionalRating", evaluation.getRating()); // 使用rating作为专业评分
            result.put("overallRating", evaluation.getRating()); // 使用rating作为综合评分
            result.put("evaluationContent", evaluation.getReviewContent()); // 评价内容
            result.put("evaluationTags", evaluation.getReviewTags()); // 评价标签
            result.put("evaluationImages", evaluation.getReviewImages()); // 评价图片
            result.put("isAnonymous", evaluation.getIsAnonymous());
            result.put("evaluationTime", evaluation.getCreateTime()); // 使用创建时间作为评价时间
            result.put("replyContent", evaluation.getReplyContent());
            result.put("replyTime", evaluation.getReplyTime());
            result.put("replyUserName", evaluation.getReviewerName()); // 使用评价人姓名作为回复人姓名
            result.put("evaluationStatus", evaluation.getStatus() != null && evaluation.getStatus() == 1 ? "completed" : "pending");
            
            // 添加新字段
            result.put("reviewType", evaluation.getReviewType());
            result.put("isRecommend", evaluation.getIsRecommend());
            result.put("likeCount", evaluation.getLikeCount());
            result.put("agencyId", evaluation.getAgencyId());
            result.put("servicePackageId", evaluation.getServicePackageId());
        } else {
            result.put("evaluationStatus", "pending"); // 待评价
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addEvaluation(Map<String, Object> reqVO) {
        log.info("新增服务评价，请求参数: {}", reqVO);
        
        // 获取订单ID
        String orderId = reqVO.get("orderId") != null ? reqVO.get("orderId").toString() : null;
        if (StrUtil.isBlank(orderId)) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "订单ID不能为空");
        }
        
        // 兼容不同的参数格式
        BigDecimal overallRating = null;
        BigDecimal serviceRating = null;
        BigDecimal attitudeRating = null;
        BigDecimal professionalRating = null;
        
        // 优先使用 overallRating
        if (reqVO.get("overallRating") != null) {
            overallRating = new BigDecimal(reqVO.get("overallRating").toString());
        } else if (reqVO.get("serviceRating") != null && reqVO.get("attitudeRating") != null && reqVO.get("professionalRating") != null) {
            // 如果有三个评分，则计算综合评分
            serviceRating = new BigDecimal(reqVO.get("serviceRating").toString());
            attitudeRating = new BigDecimal(reqVO.get("attitudeRating").toString());
            professionalRating = new BigDecimal(reqVO.get("professionalRating").toString());
            overallRating = serviceRating.add(attitudeRating).add(professionalRating)
                    .divide(new BigDecimal("3"), 1, BigDecimal.ROUND_HALF_UP);
        } else {
            // 默认评分
            overallRating = new BigDecimal("5.0");
        }
        
        // 获取评价内容，兼容不同的字段名
        String evaluationContent = null;
        if (reqVO.get("evaluationContent") != null) {
            evaluationContent = reqVO.get("evaluationContent").toString();
        } else if (reqVO.get("comment") != null) {
            evaluationContent = reqVO.get("comment").toString();
        }
        
        // 获取评价标签，兼容不同的字段名
        String evaluationTags = null;
        if (reqVO.get("evaluationTags") != null) {
            evaluationTags = reqVO.get("evaluationTags").toString();
        } else if (reqVO.get("tags") != null) {
            // 如果是数组，转换为字符串
            Object tagsObj = reqVO.get("tags");
                    if (tagsObj instanceof List) {
            @SuppressWarnings("unchecked")
            List<String> tagsList = (List<String>) tagsObj;
            evaluationTags = String.join(",", tagsList);
        } else {
            evaluationTags = tagsObj.toString();
        }
        }
        
        // 获取评价图片
        String evaluationImages = reqVO.get("evaluationImages") != null ? reqVO.get("evaluationImages").toString() : null;
        
        // 获取是否匿名
        Integer isAnonymous = null;
        if (reqVO.get("isAnonymous") != null) {
            isAnonymous = Integer.valueOf(reqVO.get("isAnonymous").toString());
        }
        
        // 获取评价时间
        LocalDateTime evaluationTime = null;
        if (reqVO.get("evaluationTime") != null) {
            try {
                String timeStr = reqVO.get("evaluationTime").toString();
                evaluationTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                log.warn("解析评价时间失败，使用当前时间: {}", reqVO.get("evaluationTime"));
                evaluationTime = LocalDateTime.now();
            }
        } else {
            evaluationTime = LocalDateTime.now();
        }
        
        log.info("新增服务评价，订单ID: {}, 综合评分: {}", orderId, overallRating);
        
        // 查询订单信息 - 先尝试通过详情表ID查询
        DomesticTaskOrderDO order = domesticTaskOrderMapper.selectById(Long.valueOf(orderId));
        if (order == null) {
            // 如果通过详情表ID查不到，尝试通过orderId查询
            order = domesticTaskOrderMapper.selectByOrderId(Long.valueOf(orderId));
        }
        if (order == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "订单不存在");
        }
        
        // 根据practitionerOneid查询服务人员的id
        String auntId = null;
        if (order.getPractitionerOneid() != null) {
            try {
                // 根据aunt_oneid查询服务人员信息
                PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(order.getPractitionerOneid());
                if (practitioner != null) {
                    auntId = practitioner.getId().toString();
                    log.info("查询到服务人员信息，aunt_oneid: {}, aunt_id: {}, 姓名: {}", 
                            order.getPractitionerOneid(), auntId, practitioner.getName());
                } else {
                    log.warn("未找到服务人员信息，aunt_oneid: {}", order.getPractitionerOneid());
                    // 如果找不到服务人员，使用订单ID作为aunt_id
                    auntId = order.getId().toString();
                }
            } catch (Exception e) {
                log.error("查询服务人员信息异常，aunt_oneid: {}", order.getPractitionerOneid(), e);
                // 异常情况下使用订单ID作为aunt_id
                auntId = order.getId().toString();
            }
        } else {
            // 如果没有practitionerOneid，使用订单ID作为aunt_id
            auntId = order.getId().toString();
        }
        
        // 创建评价记录
        OrderEvaluationDO evaluationDO = OrderEvaluationDO.builder()
                .orderId(Long.valueOf(orderId))
                .auntId(auntId != null ? auntId : orderId) // 使用查询到的服务人员ID
                .reviewerId(SecurityFrameworkUtils.getLoginUserId()) // 从当前登录用户获取
                .reviewerName(order.getCustomerName()) // 使用客户姓名作为评价人姓名
                .reviewerAvatar(null) // TODO: 从用户信息获取
                // .rating(overallRating) // 移除rating字段，因为它是生成列
                .attitudeRating(overallRating) // 使用综合评分作为态度评分
                .professionalRating(overallRating) // 使用综合评分作为专业评分
                .responsibilityRating(overallRating) // 使用综合评分作为责任心评分
                .reviewTags(evaluationTags)
                .reviewContent(evaluationContent)
                .reviewImages(evaluationImages)
                .reviewType("service") // 默认为服务评价
                .isAnonymous(isAnonymous != null ? isAnonymous : 0)
                .isRecommend(0) // 默认不推荐
                .likeCount(0) // 默认点赞数为0
                .replyContent(null)
                .replyTime(null)
                .status(1) // 默认显示
                .agencyId(order.getAgencyId()) // 使用订单中的机构ID
                .servicePackageId(order.getServicePackageId()) // 使用订单中的套餐ID
                .build();
        
        // 设置创建时间和更新时间
        evaluationDO.setCreateTime(evaluationTime);
        evaluationDO.setUpdateTime(LocalDateTime.now());
        
        int insertResult = orderEvaluationMapper.insert(evaluationDO);
        
        if (insertResult > 0) {
            log.info("服务评价新增成功，评价ID: {}", evaluationDO.getId());
            return true;
        }
        
        return false;
    }

    // ========== 新增接口实现 ==========

    @Override
    public Map<String, Object> getPackageList() {
        log.info("获取服务套餐列表");
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询所有有效的服务套餐
            List<ServicePackageDO> packageList = servicePackageMapper.selectList(
                new LambdaQueryWrapperX<ServicePackageDO>()
                    .eq(ServicePackageDO::getDeleted, false)
                    .eq(ServicePackageDO::getStatus, "active")
                    .eq(ServicePackageDO::getAuditStatus, "approved")
                    .orderByDesc(ServicePackageDO::getCreateTime)
            );
            
            // 转换为前端需要的格式
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (ServicePackageDO pkg : packageList) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", pkg.getId());
                item.put("name", pkg.getName());
                item.put("price", pkg.getPrice());
                item.put("originalPrice", pkg.getOriginalPrice());
                item.put("category", pkg.getCategory());
                item.put("categoryId", pkg.getCategoryId());
                item.put("packageType", pkg.getPackageType());
                item.put("duration", pkg.getServiceDuration());
                item.put("unit", pkg.getUnit());
                item.put("description", pkg.getServiceDescription());
                item.put("thumbnail", pkg.getThumbnail());
                item.put("agencyId", pkg.getAgencyId());
                item.put("agencyName", pkg.getAgencyName());
                dataList.add(item);
            }
            
            result.put("success", true);
            result.put("data", dataList);
            result.put("message", "获取服务套餐列表成功");
            result.put("total", dataList.size());
            
        } catch (Exception e) {
            log.error("获取服务套餐列表失败", e);
            result.put("success", false);
            result.put("message", "获取服务套餐列表失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getAgencyList() {
        log.info("获取服务机构列表");
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询所有有效的服务机构
            List<AgencyDO> agencyList = agencyMapper.selectList(
                new LambdaQueryWrapperX<AgencyDO>()
                    .eq(AgencyDO::getDeleted, false)
                    .eq(AgencyDO::getStatus, "active")
                    .eq(AgencyDO::getReviewStatus, "approved")
                    .eq(AgencyDO::getCooperationStatus, "cooperating")
                    .orderByDesc(AgencyDO::getCreateTime)
            );
            
            // 转换为前端需要的格式
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (AgencyDO agency : agencyList) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", agency.getId());
                item.put("agencyName", agency.getAgencyName());
                item.put("agencyNo", agency.getAgencyNo());
                item.put("contactPerson", agency.getContactPerson());
                item.put("contactPhone", agency.getContactPhone());
                item.put("address", agency.getAgencyAddress());
                item.put("district", agency.getDistrict());
                item.put("rating", null); // AgencyDO中没有rating字段
                item.put("totalOrders", null); // AgencyDO中没有totalOrders字段
                item.put("satisfactionRate", null); // AgencyDO中没有satisfactionRate字段
                dataList.add(item);
            }
            
            result.put("success", true);
            result.put("data", dataList);
            result.put("message", "获取服务机构列表成功");
            result.put("total", dataList.size());
            
        } catch (Exception e) {
            log.error("获取服务机构列表失败", e);
            result.put("success", false);
            result.put("message", "获取服务机构列表失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getPractitionerList(Long agencyId) {
        log.info("获取服务人员列表，机构ID: {}", agencyId);
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 构建查询条件
            LambdaQueryWrapperX<PractitionerDO> queryWrapper = new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getDeleted, false)
                .eq(PractitionerDO::getStatus, "active")
                .eq(PractitionerDO::getPlatformStatus, "cooperating")
                .orderByDesc(PractitionerDO::getRating)
                .orderByDesc(PractitionerDO::getCreateTime);
            
            // 如果指定了机构ID，则按机构筛选
            if (agencyId != null) {
                queryWrapper.eq(PractitionerDO::getAgencyId, agencyId);
            }
            
            // 查询服务人员
            List<PractitionerDO> practitionerList = practitionerMapper.selectList(queryWrapper);
            
            // 转换为前端需要的格式
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (PractitionerDO practitioner : practitionerList) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", practitioner.getId());
                item.put("name", practitioner.getName());
                item.put("phone", practitioner.getPhone());
                item.put("oneid", practitioner.getAuntOneid());
                item.put("serviceType", practitioner.getServiceType());
                item.put("rating", practitioner.getRating());
                item.put("totalOrders", practitioner.getTotalOrders());
                item.put("satisfactionRate", practitioner.getCustomerSatisfaction());
                item.put("currentStatus", practitioner.getCurrentStatus());
                item.put("agencyId", practitioner.getAgencyId());
                item.put("agencyName", practitioner.getAgencyName());
                item.put("avatar", practitioner.getAvatar());
                item.put("experience", practitioner.getExperienceYears());
                item.put("certificates", null); // PractitionerDO中没有certificates字段
                dataList.add(item);
            }
            
            result.put("success", true);
            result.put("data", dataList);
            result.put("message", "获取服务人员列表成功");
            result.put("total", dataList.size());
            
        } catch (Exception e) {
            log.error("获取服务人员列表失败", e);
            result.put("success", false);
            result.put("message", "获取服务人员列表失败：" + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateEvaluation(Map<String, Object> reqVO) {
        String orderId = (String) reqVO.get("orderId");
        BigDecimal overallRating = reqVO.get("overallRating") != null ? 
            new BigDecimal(reqVO.get("overallRating").toString()) : null;
        String evaluationContent = (String) reqVO.get("evaluationContent");
        String evaluationTags = reqVO.get("evaluationTags") != null ? 
            reqVO.get("evaluationTags").toString() : null;
        String evaluationImages = (String) reqVO.get("evaluationImages");
        Integer isAnonymous = (Integer) reqVO.get("isAnonymous");
        String replyContent = (String) reqVO.get("replyContent");
        
        log.info("更新服务评价，订单ID: {}, 综合评分: {}", orderId, overallRating);
        
        // 根据订单ID查询评价信息
        OrderEvaluationDO evaluation = orderEvaluationMapper.selectByOrderId(Long.valueOf(orderId));
        if (evaluation == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "该订单的评价不存在");
        }
        
        // 更新评价信息
        if (overallRating != null) {
            // 使用综合评分作为三个维度评分
            evaluation.setAttitudeRating(overallRating);
            evaluation.setProfessionalRating(overallRating);
            evaluation.setResponsibilityRating(overallRating);
            // 注意：rating字段是生成列，不需要手动设置，由数据库自动计算
        }
        
        if (evaluationContent != null) {
            evaluation.setReviewContent(evaluationContent);
        }
        if (evaluationTags != null) {
            evaluation.setReviewTags(evaluationTags);
        }
        if (evaluationImages != null) {
            evaluation.setReviewImages(evaluationImages);
        }
        if (isAnonymous != null) {
            evaluation.setIsAnonymous(isAnonymous);
        }
        if (replyContent != null) {
            evaluation.setReplyContent(replyContent);
            evaluation.setReplyTime(LocalDateTime.now());
        }
        
        try {
            // 使用自定义的更新方法，排除生成列rating
            int updateResult = orderEvaluationMapper.updateEvaluationExcludeRating(evaluation);
            
            if (updateResult > 0) {
                log.info("服务评价更新成功，评价ID: {}", evaluation.getId());
                return true;
            }
            
            return false;
        } catch (Exception e) {
            log.error("更新服务评价失败，订单ID: {}, 错误信息: {}", orderId, e.getMessage(), e);
            throw new ServiceException(500, "更新服务评价失败: " + e.getMessage());
        }
    }

    // ========== 私有方法 ==========

    /**
     * 生成支付单号
     */
    private String generatePaymentNo() {
        return "PAY" + DateUtil.format(new Date(), "yyyyMMdd") + String.format("%04d", new Random().nextInt(1000000));
    }

    /**
     * 生成收支记录编号
     */
    private String generateIncomeExpenseNo() {
        return "IE" + DateUtil.format(new Date(), "yyyyMMdd") + String.format("%04d", new Random().nextInt(1000000));
    }

    /**
     * 将收支记录类型转换为支付类型
     */
    private String convertRecordTypeToPaymentType(String recordType) {
        // 根据IncomeExpenseTypeEnum判断是收入还是支出
        IncomeExpenseTypeEnum typeEnum = IncomeExpenseTypeEnum.getByCode(recordType);
        if (typeEnum != null) {
            if ("income".equals(typeEnum.getDirection())) {
                // 收入类型
                switch (recordType) {
                    case "order_income":
                        return "cash"; // 订单收入
                    case "extra_income":
                        return "other"; // 额外收入
                    case "refund_income":
                        return "refund"; // 退款收入
                    case "commission_income":
                        return "commission"; // 佣金收入
                    default:
                        return "other";
                }
            } else if ("expense".equals(typeEnum.getDirection())) {
                // 支出类型
                switch (recordType) {
                    case "service_expense":
                        return "service"; // 服务支出
                    case "compensation_expense":
                        return "compensation"; // 赔偿支出
                    case "refund_expense":
                        return "refund"; // 退款支出
                    case "commission_expense":
                        return "commission"; // 佣金支出
                    case "operation_expense":
                        return "operation"; // 运营支出
                    default:
                        return "other";
                }
            }
        }
        return "other";
    }

    /**
     * 将支付类型转换为收支记录类型
     */
    private String convertPaymentTypeToRecordType(String paymentType) {
        switch (paymentType) {
            case "cash":
                return "order_income"; // 现金支付
            case "wechat":
                return "order_income"; // 微信支付
            case "alipay":
                return "order_income"; // 支付宝
            case "bank_transfer":
                return "order_income"; // 银行转账
            case "pos":
                return "order_income"; // POS机刷卡
            case "other":
                return "extra_income"; // 其他
            default:
                return "order_income";
        }
    }

    // ========== 售后记录管理接口实现 ==========

    @Override
    public Map<String, Object> getAfterSalesList(String orderId) {
        log.info("查询订单售后记录列表，订单ID: {}", orderId);
        
        // 查询该订单的所有工单记录（作为售后记录）
        List<WorkOrderDO> workOrders = workOrderMapper.selectByOrderNo(orderId);
        
        // 转换为售后记录格式
        List<Map<String, Object>> records = new ArrayList<>();
        for (WorkOrderDO workOrder : workOrders) {
            Map<String, Object> record = new HashMap<>();
            record.put("id", workOrder.getWorkOrderNo());
            record.put("workOrderType", getWorkOrderTypeName(workOrder.getWorkOrderType()));
            record.put("problemDescription", workOrder.getWorkOrderContent());
            record.put("workOrderStatus", getWorkOrderStatusName(workOrder.getWorkOrderStatus()));
            record.put("processingResult", workOrder.getRemark());
            record.put("createTime", workOrder.getCreateTime() != null ? 
                       workOrder.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            
            records.add(record);
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("records", records);
        
        log.info("查询订单售后记录列表完成，记录数: {}", records.size());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> addAfterSalesRecord(Map<String, Object> reqVO) {
        String orderId = reqVO.get("orderId").toString();
        String workOrderType = (String) reqVO.get("workOrderType");
        String problemDescription = (String) reqVO.get("problemDescription");
        String workOrderStatus = (String) reqVO.get("workOrderStatus");
        String processingResult = (String) reqVO.get("processingResult");
        String createTime = (String) reqVO.get("createTime");
        
        log.info("新增售后记录，订单ID: {}, 工单类型: {}", orderId, workOrderType);
        
        // 生成工单编号
        String workOrderNo = generateWorkOrderNo();
        
        // 创建工单记录
        WorkOrderDO workOrder = new WorkOrderDO();
        workOrder.setWorkOrderNo(workOrderNo);
        workOrder.setOrderNo(orderId);
        workOrder.setWorkOrderTitle("售后记录 - " + workOrderType);
        workOrder.setWorkOrderContent(problemDescription);
        workOrder.setWorkOrderType(convertWorkOrderType(workOrderType));
        workOrder.setWorkOrderStatus(convertWorkOrderStatus(workOrderStatus));
        workOrder.setPriority("medium");
        workOrder.setRemark(processingResult);
        
        // 设置创建时间
        if (StrUtil.isNotBlank(createTime)) {
            try {
                LocalDateTime createDateTime = LocalDateTime.parse(createTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                workOrder.setCreateTime(createDateTime);
            } catch (Exception e) {
                log.warn("解析创建时间失败，使用当前时间: {}", createTime);
                workOrder.setCreateTime(LocalDateTime.now());
            }
        } else {
            workOrder.setCreateTime(LocalDateTime.now());
        }
        
        // 保存工单记录
        workOrderMapper.insert(workOrder);
        
        // 记录操作日志
        recordAfterSalesLog(orderId, workOrderNo, "新增售后记录", 
                           "新增售后记录：" + workOrderType + " - " + problemDescription);
        
        Map<String, Object> result = new HashMap<>();
        result.put("afterSalesId", workOrderNo);
        
        log.info("新增售后记录成功，工单编号: {}", workOrderNo);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAfterSalesRecord(Map<String, Object> reqVO) {
        String afterSalesId = reqVO.get("afterSalesId").toString();
        String workOrderType = (String) reqVO.get("workOrderType");
        String problemDescription = (String) reqVO.get("problemDescription");
        String workOrderStatus = (String) reqVO.get("workOrderStatus");
        String processingResult = (String) reqVO.get("processingResult");
        
        log.info("修改售后记录，售后记录ID: {}", afterSalesId);
        
        // 查询现有工单记录
        WorkOrderDO workOrder = workOrderMapper.selectByWorkOrderNo(afterSalesId);
        if (workOrder == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "售后记录不存在");
        }
        
        // 保存原始数据用于比较
        WorkOrderDO originalWorkOrder = new WorkOrderDO();
        BeanUtils.copyProperties(workOrder, originalWorkOrder);
        
        // 更新工单记录
        boolean hasChanges = false;
        
        if (StrUtil.isNotBlank(workOrderType)) {
            workOrder.setWorkOrderType(convertWorkOrderType(workOrderType));
            workOrder.setWorkOrderTitle("售后记录 - " + workOrderType);
            hasChanges = true;
        }
        
        if (StrUtil.isNotBlank(problemDescription)) {
            workOrder.setWorkOrderContent(problemDescription);
            hasChanges = true;
        }
        
        if (StrUtil.isNotBlank(workOrderStatus)) {
            workOrder.setWorkOrderStatus(convertWorkOrderStatus(workOrderStatus));
            hasChanges = true;
        }
        
        if (StrUtil.isNotBlank(processingResult)) {
            workOrder.setRemark(processingResult);
            hasChanges = true;
        }
        
        if (hasChanges) {
            workOrder.setUpdateTime(LocalDateTime.now());
            workOrderMapper.updateById(workOrder);
            
            // 记录操作日志
            recordAfterSalesLog(workOrder.getOrderNo(), afterSalesId, "修改售后记录", 
                               "修改售后记录：" + workOrderType + " - " + problemDescription);
        }
        
        log.info("修改售后记录成功");
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteAfterSalesRecord(Map<String, Object> reqVO) {
        String afterSalesId = reqVO.get("afterSalesId").toString();
        
        log.info("删除售后记录，售后记录ID: {}", afterSalesId);
        
        // 查询现有工单记录
        WorkOrderDO workOrder = workOrderMapper.selectByWorkOrderNo(afterSalesId);
        if (workOrder == null) {
            throw ServiceExceptionUtil.exception(BAD_REQUEST, "售后记录不存在");
        }
        
        // 软删除工单记录
        workOrderMapper.deleteById(workOrder.getId());
        
        // 记录操作日志
        recordAfterSalesLog(workOrder.getOrderNo(), afterSalesId, "删除售后记录", 
                           "删除售后记录：" + workOrder.getWorkOrderTitle());
        
        log.info("删除售后记录成功");
        return true;
    }

    /**
     * 生成工单编号
     */
    private String generateWorkOrderNo() {
        return "WO" + DateUtil.format(new Date(), "yyyyMMdd") + String.format("%04d", new Random().nextInt(1000000));
    }

    /**
     * 获取工单类型名称
     */
    private String getWorkOrderTypeName(String workOrderType) {
        switch (workOrderType) {
            case "complaint":
                return "投诉";
            case "substitution_request":
                return "换人申请";
            case "take_leave":
                return "请假";
            case "leave_adjustment":
                return "调休";
            case "separation_application":
                return "离职申请";
            default:
                return workOrderType;
        }
    }

    /**
     * 获取工单状态名称
     */
    private String getWorkOrderStatusName(String workOrderStatus) {
        switch (workOrderStatus) {
            case "pending":
                return "待处理";
            case "processing":
                return "处理中";
            case "resolved":
                return "已解决";
            case "closed":
                return "已关闭";
            case "approved":
                return "已批准";
            case "rejected":
                return "已驳回";
            default:
                return workOrderStatus;
        }
    }

    /**
     * 转换工单类型
     */
    private String convertWorkOrderType(String workOrderType) {
        switch (workOrderType) {
            case "投诉":
                return "complaint";
            case "换人申请":
                return "substitution_request";
            case "请假":
                return "take_leave";
            case "调休":
                return "leave_adjustment";
            case "离职申请":
                return "separation_application";
            default:
                return workOrderType;
        }
    }

    /**
     * 转换工单状态
     */
    private String convertWorkOrderStatus(String workOrderStatus) {
        switch (workOrderStatus) {
            case "待处理":
                return "pending";
            case "处理中":
                return "processing";
            case "已完成":
                return "resolved";
            case "已取消":
                return "closed";
            default:
                return workOrderStatus;
        }
    }

    /**
     * 记录售后操作日志
     */
    private void recordAfterSalesLog(String orderNo, String workOrderNo, String logTitle, String logContent) {
        try {
            PublicbizOrderLogDO orderLog = new PublicbizOrderLogDO();
            orderLog.setOrderNo(orderNo);
            orderLog.setLogType("售后记录管理");
            orderLog.setLogTitle(logTitle);
            orderLog.setLogContent(logContent);
            orderLog.setOldStatus("");
            orderLog.setNewStatus("");
            orderLog.setOperatorId(SecurityFrameworkUtils.getLoginUserId());
            orderLog.setOperatorName(SecurityFrameworkUtils.getLoginUserNickname());
            orderLog.setOperatorRole("系统管理员");
            
            publicbizOrderLogMapper.insert(orderLog);
            log.info("记录售后操作日志成功，工单编号: {}", workOrderNo);
        } catch (Exception e) {
            log.error("记录售后操作日志失败", e);
        }
    }

    /**
     * 更新订单已支付金额
     */
    private void updateOrderPaidAmount(Long orderId) {
        // 统计支付总金额
        BigDecimal totalPaidAmount = orderPaymentMapper.sumPaymentAmountByOrderId(orderId);
        if (totalPaidAmount == null) {
            totalPaidAmount = BigDecimal.ZERO;
        }
        
        // 更新订单主表的已支付金额
        PublicbizOrderDO mainOrder = publicbizOrderMapper.selectById(orderId);
        if (mainOrder != null) {
            mainOrder.setPaidAmount(totalPaidAmount);
            mainOrder.setUpdateTime(LocalDateTime.now());
            publicbizOrderMapper.updateById(mainOrder);
        }
        
        log.info("订单已支付金额更新完成，订单ID: {}, 已支付金额: {}", orderId, totalPaidAmount);
    }

    /**
     * 创建支付流水记录
     */
    private void createPaymentRecord(PublicbizOrderDO orderDO, DomesticTaskOrderSaveReqDTO reqDTO) {
        try {
            log.info("开始创建支付流水记录，订单号: {}", orderDO.getOrderNo());
            
            // 构建支付记录请求DTO
            OrderCollectionReqDTO paymentReqDTO = new OrderCollectionReqDTO();
            paymentReqDTO.setOrderId(orderDO.getId());
            paymentReqDTO.setOrderNo(orderDO.getOrderNo());
            paymentReqDTO.setCollectionAmount(reqDTO.getReceivedAmount() != null ? reqDTO.getReceivedAmount() : reqDTO.getActualAmount());
            paymentReqDTO.setCollectionMethod(reqDTO.getPaymentMethod() != null ? reqDTO.getPaymentMethod() : "cash");
            paymentReqDTO.setPaymentStatus("paid");
            paymentReqDTO.setCollectionDate(reqDTO.getPaymentTime() != null ? reqDTO.getPaymentTime().toLocalDate() : LocalDate.now());
            paymentReqDTO.setOperatorName(SecurityFrameworkUtils.getLoginUserNickname());
            paymentReqDTO.setCollectionRemark(reqDTO.getPaymentRemark() != null ? reqDTO.getPaymentRemark() : "订单创建时自动生成支付记录");
            
            // 调用支付服务创建支付记录
            Long paymentId = orderPaymentService.createPaymentRecord(paymentReqDTO);
            
            log.info("支付流水记录创建成功，支付记录ID: {}", paymentId);
            
        } catch (Exception e) {
            log.error("创建支付流水记录失败，订单号: {}", orderDO.getOrderNo(), e);
            // 不抛出异常，避免影响订单创建流程
        }
    }

    /**
     * 手动创建操作日志记录
     */
    private void createOperateLog(Long bizId, String orderNo, String subType, String action, Object extraData) {
        try {
            log.info("开始创建操作日志，业务ID: {}, 订单号: {}", bizId, orderNo);
            
            // 创建订单操作日志记录
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(orderNo)
                    .logType(subType)
                    .logTitle(subType)
                    .logContent(action)
                    .oldStatus("")
                    .newStatus("")
                    .operatorId(SecurityFrameworkUtils.getLoginUserId())
                    .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                    .operatorRole("管理员")
                    .relatedPartyType("家政服务订单")
                    .relatedPartyName("客户")
                    .build();
            
            // 手动设置deleted字段
            orderLog.setDeleted(false);
            
            // 直接插入到publicbiz_order_log表
            int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
            if (logInsertResult <= 0) {
                log.error("创建操作日志失败，订单号：{}", orderNo);
                throw new RuntimeException("创建操作日志失败");
            }
            
            log.info("操作日志创建成功，业务ID: {}", bizId);
            
        } catch (Exception e) {
            log.error("创建操作日志失败，业务ID: {}", bizId, e);
            // 不抛出异常，避免影响主业务流程
        }
    }

  /**
     * 记录订单更新日志（JSON格式）
     */
    private void recordOrderUpdateLog(DomesticTaskOrderUpdateReqDTO reqDTO, DomesticTaskOrderDO originalOrder, 
                                    DomesticTaskOrderDO updatedOrder) {
        try {
            log.info("开始记录订单更新日志，订单号：{}", updatedOrder.getOrderNo());
            
            // 构建变更内容JSON
            Map<String, Object> changes = new HashMap<>();
            Map<String, Object> changeDetails = new HashMap<>();
            
            // 检查主要字段的变更 - 只有当字段真正发生变化时才记录
            if (reqDTO.getCustomerName() != null && !Objects.equals(originalOrder.getCustomerName(), reqDTO.getCustomerName())) {
                Map<String, String> customerNameChange = new HashMap<>();
                customerNameChange.put("old", originalOrder.getCustomerName() != null ? originalOrder.getCustomerName() : "");
                customerNameChange.put("new", reqDTO.getCustomerName());
                changeDetails.put("customerName", customerNameChange);
            }
            
            if (reqDTO.getCustomerPhone() != null && !Objects.equals(originalOrder.getCustomerPhone(), reqDTO.getCustomerPhone())) {
                Map<String, String> customerPhoneChange = new HashMap<>();
                customerPhoneChange.put("old", originalOrder.getCustomerPhone() != null ? originalOrder.getCustomerPhone() : "");
                customerPhoneChange.put("new", reqDTO.getCustomerPhone());
                changeDetails.put("customerPhone", customerPhoneChange);
            }
            
            if (reqDTO.getServiceStartDate() != null && !Objects.equals(originalOrder.getServiceStartDate(), reqDTO.getServiceStartDate())) {
                Map<String, String> serviceStartDateChange = new HashMap<>();
                serviceStartDateChange.put("old", originalOrder.getServiceStartDate() != null ? originalOrder.getServiceStartDate().toString() : "");
                serviceStartDateChange.put("new", reqDTO.getServiceStartDate().toString());
                changeDetails.put("serviceStartDate", serviceStartDateChange);
            }
            
            if (reqDTO.getServiceAddress() != null && !Objects.equals(originalOrder.getServiceAddress(), reqDTO.getServiceAddress())) {
                Map<String, String> serviceAddressChange = new HashMap<>();
                serviceAddressChange.put("old", originalOrder.getServiceAddress() != null ? originalOrder.getServiceAddress() : "");
                serviceAddressChange.put("new", reqDTO.getServiceAddress());
                changeDetails.put("serviceAddress", serviceAddressChange);
            }
            
            if (reqDTO.getPractitionerOneid() != null && !Objects.equals(originalOrder.getPractitionerOneid(), reqDTO.getPractitionerOneid())) {
                Map<String, String> practitionerOneidChange = new HashMap<>();
                practitionerOneidChange.put("old", originalOrder.getPractitionerOneid() != null ? originalOrder.getPractitionerOneid() : "");
                practitionerOneidChange.put("new", reqDTO.getPractitionerOneid());
                changeDetails.put("practitionerOneid", practitionerOneidChange);
            }
            
            if (reqDTO.getAgencyId() != null && !Objects.equals(originalOrder.getAgencyId(), reqDTO.getAgencyId())) {
                Map<String, String> agencyIdChange = new HashMap<>();
                agencyIdChange.put("old", originalOrder.getAgencyId() != null ? originalOrder.getAgencyId().toString() : "");
                agencyIdChange.put("new", reqDTO.getAgencyId().toString());
                changeDetails.put("agencyId", agencyIdChange);
            }
            
            if (reqDTO.getUnitPrice() != null && !Objects.equals(originalOrder.getUnitPrice(), reqDTO.getUnitPrice())) {
                Map<String, String> unitPriceChange = new HashMap<>();
                unitPriceChange.put("old", originalOrder.getUnitPrice() != null ? originalOrder.getUnitPrice().toString() : "");
                unitPriceChange.put("new", reqDTO.getUnitPrice().toString());
                changeDetails.put("unitPrice", unitPriceChange);
            }
            
            if (reqDTO.getTotalAmount() != null && !Objects.equals(originalOrder.getTotalAmount(), reqDTO.getTotalAmount())) {
                Map<String, String> totalAmountChange = new HashMap<>();
                totalAmountChange.put("old", originalOrder.getTotalAmount() != null ? originalOrder.getTotalAmount().toString() : "");
                totalAmountChange.put("new", reqDTO.getTotalAmount().toString());
                changeDetails.put("totalAmount", totalAmountChange);
            }
            
            if (reqDTO.getActualAmount() != null && !Objects.equals(originalOrder.getActualAmount(), reqDTO.getActualAmount())) {
                Map<String, String> actualAmountChange = new HashMap<>();
                actualAmountChange.put("old", originalOrder.getActualAmount() != null ? originalOrder.getActualAmount().toString() : "");
                actualAmountChange.put("new", reqDTO.getActualAmount().toString());
                changeDetails.put("actualAmount", actualAmountChange);
            }
            
            // 只有当有变更时才记录日志
            if (!changeDetails.isEmpty()) {
                changes.put("orderId", updatedOrder.getId());
                changes.put("orderNo", updatedOrder.getOrderNo());
                changes.put("changes", changeDetails);
                changes.put("updateTime", LocalDateTime.now().toString());
                changes.put("operatorId", SecurityFrameworkUtils.getLoginUserId());
                changes.put("operatorName", SecurityFrameworkUtils.getLoginUserNickname());
                
                // 转换为JSON字符串
                String jsonContent = JsonUtils.toJsonString(changes);
                
                // 创建操作日志记录
                PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                        .orderNo(updatedOrder.getOrderNo())
                        .logType("订单编辑")
                        .logTitle("订单信息更新")
                        .logContent(jsonContent)
                        .oldStatus("")
                        .newStatus("")
                        .operatorId(SecurityFrameworkUtils.getLoginUserId())
                        .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                        .operatorRole("管理员")
                        .relatedPartyType("家政服务订单")
                        .relatedPartyName(reqDTO.getCustomerName() != null ? reqDTO.getCustomerName() : updatedOrder.getCustomerName())
                        .build();
                
                // 直接插入到publicbiz_order_log表
                int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
                if (logInsertResult <= 0) {
                    log.error("创建订单更新日志失败，订单号：{}", updatedOrder.getOrderNo());
                    throw new RuntimeException("创建订单更新日志失败");
                }
                
                log.info("订单更新日志记录成功，订单号：{}", updatedOrder.getOrderNo());
            } else {
                log.info("订单信息无变更，不记录更新日志，订单号：{}", updatedOrder.getOrderNo());
            }
            
        } catch (Exception e) {
            log.error("记录订单更新日志异常，订单号：{}", updatedOrder.getOrderNo(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 如果需要，更新支付记录
     */
    private void updatePaymentRecordIfNeeded(Long orderId, DomesticTaskOrderUpdateReqDTO reqDTO) {
        try {
            log.info("开始更新支付记录，订单ID: {}", orderId);
            
            // 参数验证
            if (orderId == null || reqDTO == null || reqDTO.getReceivedAmount() == null) {
                log.warn("更新支付记录参数不完整，订单ID: {}, 收款金额: {}", orderId, reqDTO != null ? reqDTO.getReceivedAmount() : null);
                return;
            }
            
            // 查询现有的支付记录
            LambdaQueryWrapperX<PublicbizOrderPaymentDO> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.eq(PublicbizOrderPaymentDO::getOrderId, orderId);
            List<PublicbizOrderPaymentDO> existingPayments = orderPaymentMapper.selectList(queryWrapper);
            
            if (!existingPayments.isEmpty()) {
                // 更新第一条支付记录
                PublicbizOrderPaymentDO payment = existingPayments.get(0);
                payment.setPaymentType(reqDTO.getPaymentMethod());
                payment.setPaymentAmount(reqDTO.getReceivedAmount());
                payment.setPaymentTime(reqDTO.getPaymentTime() != null ? 
                    reqDTO.getPaymentTime().toLocalDate().atStartOfDay() : LocalDateTime.now());
                payment.setPaymentRemark(reqDTO.getPaymentRemark());
                payment.setUpdateTime(LocalDateTime.now());
                payment.setUpdater(SecurityFrameworkUtils.getLoginUserId().toString());
                
                int updateResult = orderPaymentMapper.updateById(payment);
                if (updateResult > 0) {
                    log.info("支付记录更新成功，订单ID: {}", orderId);
                } else {
                    log.warn("支付记录更新失败，订单ID: {}", orderId);
                }
            } else {
                // 创建新的支付记录
                // 先查询订单信息获取订单号
                DomesticTaskOrderDO order = domesticTaskOrderMapper.selectById(orderId);
                String orderNo = order != null ? order.getOrderNo() : "";
                
                PublicbizOrderPaymentDO newPayment = PublicbizOrderPaymentDO.builder()
                        .orderId(orderId)
                        .orderNo(orderNo)
                        .paymentNo(generatePaymentNo())
                        .paymentType(reqDTO.getPaymentMethod())
                        .paymentAmount(reqDTO.getReceivedAmount())
                        .paymentStatus("success")
                        .paymentTime(reqDTO.getPaymentTime() != null ? 
                            reqDTO.getPaymentTime().toLocalDate().atStartOfDay() : LocalDateTime.now())
                        .operatorId(SecurityFrameworkUtils.getLoginUserId())
                        .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                        .paymentRemark(reqDTO.getPaymentRemark())
                        .build();
                
                int insertResult = orderPaymentMapper.insert(newPayment);
                if (insertResult > 0) {
                    log.info("支付记录创建成功，订单ID: {}", orderId);
                } else {
                    log.warn("支付记录创建失败，订单ID: {}", orderId);
                }
            }
            
        } catch (Exception e) {
            log.error("更新支付记录异常，订单ID: {}", orderId, e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 记录订单创建日志（JSON格式）
     */
    private void recordOrderCreateLog(DomesticTaskOrderSaveReqDTO reqDTO, PublicbizOrderDO orderDO, 
                                    DomesticTaskOrderDO domesticOrderDO) {
        try {
            log.info("开始记录订单创建日志，订单号：{}", orderDO.getOrderNo());
            
            // 构建创建内容JSON
            Map<String, Object> createInfo = new HashMap<>();
            createInfo.put("orderId", orderDO.getId());
            createInfo.put("orderNo", orderDO.getOrderNo());
            createInfo.put("customerName", reqDTO.getCustomerName());
            createInfo.put("customerPhone", reqDTO.getCustomerPhone());
            createInfo.put("serviceType", reqDTO.getServiceType());
            createInfo.put("serviceStartDate", reqDTO.getServiceStartDate() != null ? reqDTO.getServiceStartDate().toString() : "");
            createInfo.put("serviceAddress", reqDTO.getServiceAddress());
            createInfo.put("unitPrice", reqDTO.getUnitPrice() != null ? reqDTO.getUnitPrice().toString() : "");
            createInfo.put("totalAmount", reqDTO.getTotalAmount() != null ? reqDTO.getTotalAmount().toString() : "");
            createInfo.put("actualAmount", reqDTO.getActualAmount() != null ? reqDTO.getActualAmount().toString() : "");
            createInfo.put("paymentStatus", reqDTO.getPaymentStatus());
            createInfo.put("paymentMethod", reqDTO.getPaymentMethod());
            createInfo.put("receivedAmount", reqDTO.getReceivedAmount() != null ? reqDTO.getReceivedAmount().toString() : "");
            createInfo.put("paymentTime", reqDTO.getPaymentTime() != null ? reqDTO.getPaymentTime().toString() : "");
            createInfo.put("paymentRemark", reqDTO.getPaymentRemark());
            createInfo.put("agencyId", reqDTO.getAgencyId() != null ? reqDTO.getAgencyId().toString() : "");
            createInfo.put("agencyName", reqDTO.getAgencyName());
            createInfo.put("practitionerOneid", reqDTO.getPractitionerOneid());
            createInfo.put("practitionerName", reqDTO.getPractitionerName());
            createInfo.put("practitionerPhone", reqDTO.getPractitionerPhone());
            createInfo.put("createTime", LocalDateTime.now().toString());
            createInfo.put("operatorId", SecurityFrameworkUtils.getLoginUserId());
            createInfo.put("operatorName", SecurityFrameworkUtils.getLoginUserNickname());
            
            // 转换为JSON字符串
            String jsonContent = JsonUtils.toJsonString(createInfo);
            
            // 创建操作日志记录
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(orderDO.getOrderNo())
                    .logType("订单创建")
                    .logTitle("创建家政服务订单")
                    .logContent(jsonContent)
                    .oldStatus("")
                    .newStatus("pending")
                    .operatorId(SecurityFrameworkUtils.getLoginUserId())
                    .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                    .operatorRole("管理员")
                    .relatedPartyType("家政服务订单")
                    .relatedPartyName(reqDTO.getCustomerName())
                    .build();
            
            // 直接插入到publicbiz_order_log表
            int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
            if (logInsertResult <= 0) {
                log.error("创建订单创建日志失败，订单号：{}", orderDO.getOrderNo());
                throw new RuntimeException("创建订单创建日志失败");
            }
            
            log.info("订单创建日志记录成功，订单号：{}", orderDO.getOrderNo());
            
        } catch (Exception e) {
            log.error("记录订单创建日志异常，订单号：{}", orderDO.getOrderNo(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 记录支付操作日志（JSON格式）
     */
    private void recordPaymentLog(DomesticTaskOrderDO order, PublicbizOrderPaymentDO paymentDO, String operation) {
        try {
            log.info("开始记录支付操作日志，订单号：{}，操作：{}", order.getOrderNo(), operation);
            
            // 构建支付信息JSON
            Map<String, Object> paymentInfo = new HashMap<>();
            paymentInfo.put("orderId", order.getId());
            paymentInfo.put("orderNo", order.getOrderNo());
            paymentInfo.put("paymentId", paymentDO.getId());
            paymentInfo.put("paymentNo", paymentDO.getPaymentNo());
            paymentInfo.put("paymentType", paymentDO.getPaymentType());
            paymentInfo.put("paymentAmount", paymentDO.getPaymentAmount() != null ? paymentDO.getPaymentAmount().toString() : "");
            paymentInfo.put("paymentStatus", paymentDO.getPaymentStatus());
            paymentInfo.put("paymentTime", paymentDO.getPaymentTime() != null ? paymentDO.getPaymentTime().toString() : "");
            paymentInfo.put("paymentRemark", paymentDO.getPaymentRemark());
            paymentInfo.put("transactionId", paymentDO.getTransactionId());
            paymentInfo.put("operation", operation);
            paymentInfo.put("operatorId", SecurityFrameworkUtils.getLoginUserId());
            paymentInfo.put("operatorName", SecurityFrameworkUtils.getLoginUserNickname());
            paymentInfo.put("createTime", LocalDateTime.now().toString());
            
            // 转换为JSON字符串
            String jsonContent = JsonUtils.toJsonString(paymentInfo);
            
            // 创建操作日志记录
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(order.getOrderNo())
                    .logType(operation)
                    .logTitle(operation + "信息")
                    .logContent(jsonContent)
                    .oldStatus("")
                    .newStatus("")
                    .operatorId(SecurityFrameworkUtils.getLoginUserId())
                    .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                    .operatorRole("管理员")
                    .relatedPartyType("家政服务订单")
                    .relatedPartyName(order.getCustomerName())
                    .build();
            
            // 直接插入到publicbiz_order_log表
            int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
            if (logInsertResult <= 0) {
                log.error("创建支付操作日志失败，订单号：{}", order.getOrderNo());
                throw new RuntimeException("创建支付操作日志失败");
            }
            
            log.info("支付操作日志记录成功，订单号：{}", order.getOrderNo());
            
        } catch (Exception e) {
            log.error("记录支付操作日志异常，订单号：{}", order.getOrderNo(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 记录支付更新日志（JSON格式）
     */
    private void recordPaymentUpdateLog(DomesticTaskOrderDO order, PublicbizOrderPaymentDO originalPayment, 
                                      PublicbizOrderPaymentDO updatedPayment) {
        try {
            log.info("开始记录支付更新日志，订单号：{}", order.getOrderNo());
            
            // 构建变更内容JSON
            Map<String, Object> changes = new HashMap<>();
            Map<String, Object> changeDetails = new HashMap<>();
            
            // 检查主要字段的变更
            if (!Objects.equals(originalPayment.getPaymentType(), updatedPayment.getPaymentType())) {
                Map<String, String> paymentTypeChange = new HashMap<>();
                paymentTypeChange.put("old", originalPayment.getPaymentType() != null ? originalPayment.getPaymentType() : "");
                paymentTypeChange.put("new", updatedPayment.getPaymentType() != null ? updatedPayment.getPaymentType() : "");
                changeDetails.put("paymentType", paymentTypeChange);
            }
            
            if (!Objects.equals(originalPayment.getPaymentAmount(), updatedPayment.getPaymentAmount())) {
                Map<String, String> paymentAmountChange = new HashMap<>();
                paymentAmountChange.put("old", originalPayment.getPaymentAmount() != null ? originalPayment.getPaymentAmount().toString() : "");
                paymentAmountChange.put("new", updatedPayment.getPaymentAmount() != null ? updatedPayment.getPaymentAmount().toString() : "");
                changeDetails.put("paymentAmount", paymentAmountChange);
            }
            
            if (!Objects.equals(originalPayment.getPaymentRemark(), updatedPayment.getPaymentRemark())) {
                Map<String, String> paymentRemarkChange = new HashMap<>();
                paymentRemarkChange.put("old", originalPayment.getPaymentRemark() != null ? originalPayment.getPaymentRemark() : "");
                paymentRemarkChange.put("new", updatedPayment.getPaymentRemark() != null ? updatedPayment.getPaymentRemark() : "");
                changeDetails.put("paymentRemark", paymentRemarkChange);
            }
            
            if (!Objects.equals(originalPayment.getTransactionId(), updatedPayment.getTransactionId())) {
                Map<String, String> transactionIdChange = new HashMap<>();
                transactionIdChange.put("old", originalPayment.getTransactionId() != null ? originalPayment.getTransactionId() : "");
                transactionIdChange.put("new", updatedPayment.getTransactionId() != null ? updatedPayment.getTransactionId() : "");
                changeDetails.put("transactionId", transactionIdChange);
            }
            
            changes.put("orderId", order.getId());
            changes.put("orderNo", order.getOrderNo());
            changes.put("paymentId", updatedPayment.getId());
            changes.put("paymentNo", updatedPayment.getPaymentNo());
            changes.put("changes", changeDetails);
            changes.put("updateTime", LocalDateTime.now().toString());
            changes.put("operatorId", SecurityFrameworkUtils.getLoginUserId());
            changes.put("operatorName", SecurityFrameworkUtils.getLoginUserNickname());
            
            // 转换为JSON字符串
            String jsonContent = JsonUtils.toJsonString(changes);
            
            // 创建操作日志记录
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(order.getOrderNo())
                    .logType("修改收款")
                    .logTitle("修改收款信息")
                    .logContent(jsonContent)
                    .oldStatus("")
                    .newStatus("")
                    .operatorId(SecurityFrameworkUtils.getLoginUserId())
                    .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                    .operatorRole("管理员")
                    .relatedPartyType("家政服务订单")
                    .relatedPartyName(order.getCustomerName())
                    .build();
            
            // 直接插入到publicbiz_order_log表
            int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
            if (logInsertResult <= 0) {
                log.error("创建支付更新日志失败，订单号：{}", order.getOrderNo());
                throw new RuntimeException("创建支付更新日志失败");
            }
            
            log.info("支付更新日志记录成功，订单号：{}", order.getOrderNo());
            
        } catch (Exception e) {
            log.error("记录支付更新日志异常，订单号：{}", order.getOrderNo(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 记录收支记录操作日志（JSON格式）
     */
    private void recordIncomeExpenseLog(DomesticTaskOrderDO order, PublicbizIncomeExpenseDO record, String operation) {
        try {
            log.info("开始记录收支记录操作日志，订单号：{}，操作：{}", order.getOrderNo(), operation);
            
            // 构建收支记录信息JSON
            Map<String, Object> recordInfo = new HashMap<>();
            recordInfo.put("orderId", order.getId());
            recordInfo.put("orderNo", order.getOrderNo());
            recordInfo.put("recordId", record.getId());
            recordInfo.put("recordNo", record.getRecordNo());
            recordInfo.put("recordType", record.getRecordType());
            recordInfo.put("recordDirection", record.getRecordDirection());
            recordInfo.put("amount", record.getAmount() != null ? record.getAmount().toString() : "");
            recordInfo.put("description", record.getDescription());
            recordInfo.put("remark", record.getRemark());
            recordInfo.put("operation", operation);
            recordInfo.put("operatorId", SecurityFrameworkUtils.getLoginUserId());
            recordInfo.put("operatorName", SecurityFrameworkUtils.getLoginUserNickname());
            recordInfo.put("createTime", LocalDateTime.now().toString());
            
            // 转换为JSON字符串
            String jsonContent = JsonUtils.toJsonString(recordInfo);
            
            // 创建操作日志记录
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(order.getOrderNo())
                    .logType(operation)
                    .logTitle(operation + "信息")
                    .logContent(jsonContent)
                    .oldStatus("")
                    .newStatus("")
                    .operatorId(SecurityFrameworkUtils.getLoginUserId())
                    .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                    .operatorRole("管理员")
                    .relatedPartyType("家政服务订单")
                    .relatedPartyName(order.getCustomerName())
                    .build();
            
            // 直接插入到publicbiz_order_log表
            int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
            if (logInsertResult <= 0) {
                log.error("创建收支记录操作日志失败，订单号：{}", order.getOrderNo());
                throw new RuntimeException("创建收支记录操作日志失败");
            }
            
            log.info("收支记录操作日志记录成功，订单号：{}", order.getOrderNo());
            
        } catch (Exception e) {
            log.error("记录收支记录操作日志异常，订单号：{}", order.getOrderNo(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 记录收支记录更新日志（JSON格式）
     */
    private void recordIncomeExpenseUpdateLog(DomesticTaskOrderDO order, PublicbizIncomeExpenseDO originalRecord, 
                                            PublicbizIncomeExpenseDO updatedRecord) {
        try {
            log.info("开始记录收支记录更新日志，订单号：{}", order.getOrderNo());
            
            // 构建变更内容JSON
            Map<String, Object> changes = new HashMap<>();
            Map<String, Object> changeDetails = new HashMap<>();
            
            // 检查主要字段的变更
            if (!Objects.equals(originalRecord.getAmount(), updatedRecord.getAmount())) {
                Map<String, String> amountChange = new HashMap<>();
                amountChange.put("old", originalRecord.getAmount() != null ? originalRecord.getAmount().toString() : "");
                amountChange.put("new", updatedRecord.getAmount() != null ? updatedRecord.getAmount().toString() : "");
                changeDetails.put("amount", amountChange);
            }
            
            if (!Objects.equals(originalRecord.getDescription(), updatedRecord.getDescription())) {
                Map<String, String> descriptionChange = new HashMap<>();
                descriptionChange.put("old", originalRecord.getDescription() != null ? originalRecord.getDescription() : "");
                descriptionChange.put("new", updatedRecord.getDescription() != null ? updatedRecord.getDescription() : "");
                changeDetails.put("description", descriptionChange);
            }
            
            if (!Objects.equals(originalRecord.getRemark(), updatedRecord.getRemark())) {
                Map<String, String> remarkChange = new HashMap<>();
                remarkChange.put("old", originalRecord.getRemark() != null ? originalRecord.getRemark() : "");
                remarkChange.put("new", updatedRecord.getRemark() != null ? updatedRecord.getRemark() : "");
                changeDetails.put("remark", remarkChange);
            }
            
            changes.put("orderId", order.getId());
            changes.put("orderNo", order.getOrderNo());
            changes.put("recordId", updatedRecord.getId());
            changes.put("recordNo", updatedRecord.getRecordNo());
            changes.put("changes", changeDetails);
            changes.put("updateTime", LocalDateTime.now().toString());
            changes.put("operatorId", SecurityFrameworkUtils.getLoginUserId());
            changes.put("operatorName", SecurityFrameworkUtils.getLoginUserNickname());
            
            // 转换为JSON字符串
            String jsonContent = JsonUtils.toJsonString(changes);
            
            // 创建操作日志记录
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(order.getOrderNo())
                    .logType("修改收支记录")
                    .logTitle("修改收支记录信息")
                    .logContent(jsonContent)
                    .oldStatus("")
                    .newStatus("")
                    .operatorId(SecurityFrameworkUtils.getLoginUserId())
                    .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                    .operatorRole("管理员")
                    .relatedPartyType("家政服务订单")
                    .relatedPartyName(order.getCustomerName())
                    .build();
            
            // 直接插入到publicbiz_order_log表
            int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
            if (logInsertResult <= 0) {
                log.error("创建收支记录更新日志失败，订单号：{}", order.getOrderNo());
                throw new RuntimeException("创建收支记录更新日志失败");
            }
            
            log.info("收支记录更新日志记录成功，订单号：{}", order.getOrderNo());
            
        } catch (Exception e) {
            log.error("记录收支记录更新日志异常，订单号：{}", order.getOrderNo(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 更新支付记录（针对收支记录）
     */
    private void updatePaymentRecordForIncomeExpense(DomesticTaskOrderDO order, PublicbizIncomeExpenseDO record) {
        try {
            log.info("开始更新支付记录（针对收支记录），订单号：{}，记录类型：{}", order.getOrderNo(), record.getRecordType());
            
            // 查询现有的支付记录
            LambdaQueryWrapperX<PublicbizOrderPaymentDO> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.eq(PublicbizOrderPaymentDO::getOrderId, order.getOrderId());
            List<PublicbizOrderPaymentDO> existingPayments = orderPaymentMapper.selectList(queryWrapper);
            
            if (!existingPayments.isEmpty()) {
                // 更新第一条支付记录
                PublicbizOrderPaymentDO payment = existingPayments.get(0);
                
                // 根据收支记录类型更新支付记录
                if ("compensation_expense".equals(record.getRecordType())) {
                    // 赔偿支出，更新支付备注
                    String newRemark = payment.getPaymentRemark() != null ? 
                        payment.getPaymentRemark() + "；赔偿支出：" + record.getDescription() :
                        "赔偿支出：" + record.getDescription();
                    payment.setPaymentRemark(newRemark);
                } else if ("extra_income".equals(record.getRecordType())) {
                    // 额外收入，更新支付备注
                    String newRemark = payment.getPaymentRemark() != null ? 
                        payment.getPaymentRemark() + "；额外收入：" + record.getDescription() :
                        "额外收入：" + record.getDescription();
                    payment.setPaymentRemark(newRemark);
                }
                
                payment.setUpdateTime(LocalDateTime.now());
                payment.setUpdater(SecurityFrameworkUtils.getLoginUserId().toString());
                
                int updateResult = orderPaymentMapper.updateById(payment);
                if (updateResult > 0) {
                    log.info("支付记录更新成功（针对收支记录），订单号：{}", order.getOrderNo());
                } else {
                    log.warn("支付记录更新失败（针对收支记录），订单号：{}", order.getOrderNo());
                }
            } else {
                log.warn("未找到支付记录，无法更新（针对收支记录），订单号：{}", order.getOrderNo());
            }
            
        } catch (Exception e) {
            log.error("更新支付记录异常（针对收支记录），订单号：{}", order.getOrderNo(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 更新订单支付状态
     */
    private void updateOrderPaymentStatus(Long orderId, BigDecimal paymentAmount) {
        try {
            log.info("开始更新订单支付状态，订单ID: {}", orderId);
            
            // 查询订单主表
            PublicbizOrderDO order = publicbizOrderMapper.selectById(orderId);
            if (order == null) {
                log.warn("订单主表记录不存在，订单ID: {}", orderId);
                return;
            }
            
            // 查询该订单的所有支付记录
            LambdaQueryWrapperX<PublicbizOrderPaymentDO> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.eq(PublicbizOrderPaymentDO::getOrderId, orderId);
            queryWrapper.eq(PublicbizOrderPaymentDO::getPaymentStatus, "success");
            List<PublicbizOrderPaymentDO> payments = orderPaymentMapper.selectList(queryWrapper);
            
            // 计算总支付金额
            BigDecimal totalPaidAmount = payments.stream()
                    .map(PublicbizOrderPaymentDO::getPaymentAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 更新订单主表的已支付金额
            order.setPaidAmount(totalPaidAmount);
            order.setUpdateTime(LocalDateTime.now());
            order.setUpdater(SecurityFrameworkUtils.getLoginUserId().toString());
            
            // 根据支付金额判断支付状态
            if (totalPaidAmount.compareTo(BigDecimal.ZERO) > 0) {
                if (totalPaidAmount.compareTo(order.getTotalAmount()) >= 0) {
                    order.setPaymentStatus("paid");
                } else {
                    order.setPaymentStatus("partial_paid");
                }
            } else {
                order.setPaymentStatus("pending");
            }
            
            int updateResult = publicbizOrderMapper.updateById(order);
            if (updateResult > 0) {
                log.info("订单支付状态更新成功，订单ID: {}, 已支付金额: {}, 支付状态: {}", 
                    orderId, totalPaidAmount, order.getPaymentStatus());
            } else {
                log.warn("订单支付状态更新失败，订单ID: {}", orderId);
            }
            
        } catch (Exception e) {
            log.error("更新订单支付状态异常，订单ID: {}", orderId, e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    @Override
    public PageResult<Map<String, Object>> getOrderLogList(String orderNo, Integer page, Integer size, String logType) {
        log.info("开始查询订单操作日志，订单号：{}，页码：{}，每页大小：{}，日志类型：{}", orderNo, page, size, logType);
        
        try {
            // 构建查询条件
            LambdaQueryWrapperX<PublicbizOrderLogDO> queryWrapper = new LambdaQueryWrapperX<>();
            queryWrapper.eq(PublicbizOrderLogDO::getOrderNo, orderNo);
            
            if (StrUtil.isNotBlank(logType)) {
                queryWrapper.eq(PublicbizOrderLogDO::getLogType, logType);
            }
            
            queryWrapper.orderByDesc(PublicbizOrderLogDO::getCreateTime);
            
            // 计算分页参数
            long offset = (page - 1L) * size;
            
            // 查询总数
            long total = publicbizOrderLogMapper.selectCount(queryWrapper);
            
            // 查询数据
            List<PublicbizOrderLogDO> logList = publicbizOrderLogMapper.selectList(queryWrapper);
            
            // 手动分页
            List<PublicbizOrderLogDO> pageRecords = new ArrayList<>();
            int startIndex = (int) offset;
            int endIndex = Math.min(startIndex + size, logList.size());
            
            if (startIndex < logList.size()) {
                pageRecords = logList.subList(startIndex, endIndex);
            }
            
            // 转换为Map格式
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (PublicbizOrderLogDO log : pageRecords) {
                Map<String, Object> logMap = new HashMap<>();
                logMap.put("id", log.getId());
                logMap.put("logType", log.getLogType());
                logMap.put("logTitle", log.getLogTitle());
                logMap.put("logContent", log.getLogContent());
                logMap.put("oldStatus", log.getOldStatus());
                logMap.put("newStatus", log.getNewStatus());
                logMap.put("operatorName", log.getOperatorName());
                logMap.put("operatorRole", log.getOperatorRole());
                logMap.put("createTime", log.getCreateTime());
                logMap.put("relatedPartyType", log.getRelatedPartyType());
                logMap.put("relatedPartyName", log.getRelatedPartyName());
                resultList.add(logMap);
            }
            
            log.info("订单操作日志查询完成，订单号：{}，总记录数：{}，当前页记录数：{}", orderNo, total, resultList.size());
            
            return new PageResult<>(resultList, total);
            
        } catch (Exception e) {
            log.error("查询订单操作日志异常，订单号：{}", orderNo, e);
            throw new RuntimeException("查询订单操作日志失败", e);
        }
    }

    /**
     * 记录人员变动日志
     */
    private void recordPersonnelChangeLog(DomesticTaskOrderDO existingOrder, DomesticTaskOrderDO originalOrder, DomesticTaskOrderUpdateReqDTO reqDTO) {
        try {
            log.info("开始记录人员变动日志，订单号：{}", existingOrder.getOrderNo());
            
            // 获取原人员和新人员信息
            String oldPractitionerId = originalOrder.getPractitionerOneid();
            String oldPractitionerName = originalOrder.getPractitionerName();
            String newPractitionerId = reqDTO.getPractitionerOneid();
            String newPractitionerName = reqDTO.getPractitionerName();
            
            // 构建人员变动信息JSON
            Map<String, Object> changeInfo = new HashMap<>();
            changeInfo.put("orderId", existingOrder.getId());
            changeInfo.put("orderNo", existingOrder.getOrderNo());
            changeInfo.put("changeType", "人员变更");
            changeInfo.put("oldPractitionerId", oldPractitionerId);
            changeInfo.put("oldPractitionerName", oldPractitionerName);
            changeInfo.put("newPractitionerId", newPractitionerId);
            changeInfo.put("newPractitionerName", newPractitionerName);
            changeInfo.put("changeTime", LocalDateTime.now().toString());
            changeInfo.put("changeReason", "订单编辑时服务人员变更");
            changeInfo.put("operatorId", SecurityFrameworkUtils.getLoginUserId());
            changeInfo.put("operatorName", SecurityFrameworkUtils.getLoginUserNickname());
            
            // 转换为JSON字符串
            String jsonContent = JsonUtils.toJsonString(changeInfo);
            
            // 创建操作日志记录
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(existingOrder.getOrderNo())
                    .logType("人员变动")
                    .logTitle("服务人员变更")
                    .logContent(jsonContent)
                    .oldStatus("")
                    .newStatus("")
                    .operatorId(SecurityFrameworkUtils.getLoginUserId())
                    .operatorName(SecurityFrameworkUtils.getLoginUserNickname())
                    .operatorRole("管理员")
                    .relatedPartyType("家政服务订单")
                    .relatedPartyName(existingOrder.getCustomerName())
                    .build();
            
            // 直接插入到publicbiz_order_log表
            int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
            if (logInsertResult <= 0) {
                log.error("创建人员变动日志失败，订单号：{}", existingOrder.getOrderNo());
                throw new RuntimeException("创建人员变动日志失败");
            }
            
            log.info("人员变动日志记录成功，订单号：{}", existingOrder.getOrderNo());
            
        } catch (Exception e) {
            log.error("记录人员变动日志异常，订单号：{}", existingOrder.getOrderNo(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }
}
