package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "请假审批响应VO")
@Data
public class LeaveApprovalRespVO {

    @Schema(description = "申请ID")
    private Long requestId;

    @Schema(description = "新状态")
    private String newStatus;

    @Schema(description = "审批时间")
    private LocalDateTime approveTime;
}
