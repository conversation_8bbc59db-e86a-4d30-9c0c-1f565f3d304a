package cn.bztmaster.cnt.module.publicbiz.controller.app.agency;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.agency.AgencyLeaveApprovalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 机构请假审批")
@RestController
@RequestMapping("/publicbiz/agency/{agencyId}")
@Validated
public class AgencyLeaveApprovalController {

    @Resource
    private AgencyLeaveApprovalService agencyLeaveApprovalService;

    @GetMapping("/leave-requests")
    @Operation(summary = "获取机构请假申请列表")
    @PermitAll
    public CommonResult<PageResult<LeaveApprovalListRespVO>> getLeaveRequests(
            @Parameter(description = "机构ID", required = true) @PathVariable("agencyId") @NotNull Long agencyId,
            @Parameter(description = "状态：pending|approved|rejected") @RequestParam(value = "status", defaultValue = "pending") String status,
            @Parameter(description = "页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(value = "size", defaultValue = "20") Integer size) {
        PageResult<LeaveApprovalListRespVO> pageResult = agencyLeaveApprovalService.getLeaveRequests(agencyId, status, page, size);
        return success(pageResult);
    }

    @GetMapping("/leave-requests/{requestId}/detail")
    @Operation(summary = "获取请假申请详情（包含受影响订单）")
    @PermitAll
    public CommonResult<LeaveApprovalDetailRespVO> getLeaveRequestDetail(
            @Parameter(description = "机构ID", required = true) @PathVariable("agencyId") @NotNull Long agencyId,
            @Parameter(description = "申请ID", required = true) @PathVariable("requestId") @NotNull Long requestId) {
        LeaveApprovalDetailRespVO detail = agencyLeaveApprovalService.getLeaveRequestDetail(agencyId, requestId);
        return success(detail);
    }

    @PostMapping("/leave-requests/{requestId}/approve")
    @Operation(summary = "审批请假申请")
    @PermitAll
    public CommonResult<LeaveApprovalRespVO> approveLeaveRequest(
            @Parameter(description = "机构ID", required = true) @PathVariable("agencyId") @NotNull Long agencyId,
            @Parameter(description = "申请ID", required = true) @PathVariable("requestId") @NotNull Long requestId,
            @Valid @RequestBody LeaveApprovalReqVO reqVO) {
        LeaveApprovalRespVO resp = agencyLeaveApprovalService.approveLeaveRequest(agencyId, requestId, reqVO);
        return success(resp);
    }

    @GetMapping("/leave-requests/{requestId}/affected-orders")
    @Operation(summary = "获取受影响订单详情")
    @PermitAll
    public CommonResult<LeaveApprovalDetailRespVO.AffectedOrderVO[]> getAffectedOrders(
            @Parameter(description = "机构ID", required = true) @PathVariable("agencyId") @NotNull Long agencyId,
            @Parameter(description = "申请ID", required = true) @PathVariable("requestId") @NotNull Long requestId) {
        LeaveApprovalDetailRespVO.AffectedOrderVO[] orders = agencyLeaveApprovalService.getAffectedOrders(agencyId, requestId);
        return success(orders);
    }
}
