package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 家政服务订单保存 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务订单保存 Response VO")
@Data
public class DomesticTaskOrderSaveRespVO {

    @Schema(description = "订单ID", example = "1001")
    private Long orderId;

    @Schema(description = "订单编号", example = "DS202406005")
    private String orderNumber;
}
