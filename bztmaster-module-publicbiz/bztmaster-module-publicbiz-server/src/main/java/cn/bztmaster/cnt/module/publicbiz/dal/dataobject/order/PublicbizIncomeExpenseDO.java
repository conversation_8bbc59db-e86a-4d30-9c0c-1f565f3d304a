package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 收支记录 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_income_expense")
@KeySequence("publicbiz_income_expense_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PublicbizIncomeExpenseDO extends BaseDO {

    /**
     * 主键，自增
     */
    @TableId
    private Long id;

    /**
     * 关联订单ID
     */
    private Long orderId;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 收支记录编号
     */
    private String recordNo;

    /**
     * 记录类型：order_income-订单收入/extra_income-额外收入/compensation_expense-赔偿支出等
     */
    private String recordType;

    /**
     * 收支方向：income-收入/expense-支出
     */
    private String recordDirection;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 描述
     */
    private String description;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 记录日期
     */
    private LocalDate recordDate;

    /**
     * 记录时间
     */
    private LocalDateTime recordTime;

    /**
     * 关联订单ID（如赔偿关联的订单）
     */
    private Long relatedOrderId;

    /**
     * 关联订单号
     */
    private String relatedOrderNo;

    /**
     * 关联支付记录ID
     */
    private Long relatedPaymentId;

    /**
     * 关联支付记录号
     */
    private String relatedPaymentNo;
}
