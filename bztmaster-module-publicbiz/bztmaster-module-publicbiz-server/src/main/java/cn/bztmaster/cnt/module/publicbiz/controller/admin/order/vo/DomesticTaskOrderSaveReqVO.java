package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 家政服务订单保存 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务订单保存 Request VO")
@Data
public class DomesticTaskOrderSaveReqVO {

    @Schema(description = "关联商机ID", example = "opportunity_001")
    private String businessOpportunity;

    @Schema(description = "关联线索ID", example = "lead_001")
    private String lead;

    @Schema(description = "客户姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王女士")
    @NotBlank(message = "客户姓名不能为空")
    private String customerName;

    @Schema(description = "客户电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********")
    @NotBlank(message = "客户电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "客户电话格式不正确")
    private String customerPhone;

    @Schema(description = "服务地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京市朝阳区建国路88号")
    @NotBlank(message = "服务地址不能为空")
    private String serviceAddress;

    @Schema(description = "服务套餐ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "package_a")
    @NotBlank(message = "服务套餐不能为空")
    private String servicePackage;

    @Schema(description = "服务套餐ID", example = "1")
    private Long servicePackageId;

    @Schema(description = "服务套餐名称", example = "月嫂服务套餐")
    private String servicePackageName;

    @Schema(description = "服务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "maternity")
    @NotBlank(message = "服务类型不能为空")
    private String serviceType;

    @Schema(description = "服务分类ID", example = "1")
    private Long serviceCategoryId;

    @Schema(description = "服务分类名称", example = "月嫂服务")
    private String serviceCategoryName;

    @Schema(description = "服务开始日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-07-01")
    @NotNull(message = "服务开始日期不能为空")
    private LocalDate serviceStartDate;

    @Schema(description = "服务结束日期", example = "2024-07-31")
    private LocalDate serviceEndDate;

    @Schema(description = "服务时长", example = "30天")
    private String serviceDuration;

    @Schema(description = "服务频次", example = "每日")
    private String serviceFrequency;

    @Schema(description = "单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "12800.00")
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    @Schema(description = "订单总金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "12800.00")
    @NotNull(message = "订单总金额不能为空")
    private BigDecimal totalAmount;

    @Schema(description = "优惠金额", example = "0.00")
    private BigDecimal discountAmount;

    @Schema(description = "实付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "12800.00")
    @NotNull(message = "实付金额不能为空")
    private BigDecimal actualAmount;

    @Schema(description = "服务人员OneID", example = "practitioner_001")
    private String practitionerOneid;

    @Schema(description = "服务机构ID", example = "1001")
    private Long agencyId;

    @Schema(description = "备注", example = "客户要求月嫂有3年以上经验")
    private String remark;

    // 支付相关字段
    @Schema(description = "支付状态", example = "paid")
    private String paymentStatus;

    @Schema(description = "支付方式", example = "wechat")
    private String paymentMethod;

    @Schema(description = "收款金额", example = "12800.00")
    private BigDecimal receivedAmount;

    @Schema(description = "支付时间", example = "2024-06-01 10:00:00")
    private LocalDateTime paymentTime;

    @Schema(description = "支付备注", example = "客户通过微信支付")
    private String paymentRemark;

    // 服务机构信息
    @Schema(description = "服务机构编码", example = "1001")
    private String agencyCode;

    @Schema(description = "服务机构名称", example = "测试机构A")
    private String agencyName;

    // 服务人员信息
    @Schema(description = "服务人员姓名", example = "李阿姨")
    private String practitionerName;

    @Schema(description = "服务人员电话", example = "19808009090")
    private String practitionerPhone;
}
