package cn.bztmaster.cnt.module.publicbiz.service.employment.impl;

import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.WorkOrderConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderAttachmentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.WorkOrderTypeStatsResult;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderAttachmentMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employment.WorkOrderService;
import cn.bztmaster.cnt.module.publicbiz.enums.WorkOrderLogTypeEnum;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ArrayList;
import cn.hutool.core.util.StrUtil;

/**
 * 任务工单管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class WorkOrderServiceImpl implements WorkOrderService {

    @Resource
    private WorkOrderMapper workOrderMapper;

    @Resource
    private WorkOrderAttachmentMapper workOrderAttachmentMapper;

    @Resource
    private WorkOrderLogMapper workOrderLogMapper;

    @Resource
    private DomesticTaskMapper domesticTaskMapper;
    
    @Resource
    private PractitionerMapper practitionerMapper;
    
    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    @Override
    public PageResult<WorkOrderRespVO> pageWorkOrders(WorkOrderPageReqVO reqVO) {
        // 验证请求参数
        if (reqVO == null) {
            throw new RuntimeException("请求参数不能为空");
        }
        
        // 构建查询条件
        LambdaQueryWrapperX<WorkOrderDO> queryWrapper = new LambdaQueryWrapperX<WorkOrderDO>()
                .likeIfPresent(WorkOrderDO::getAgencyName, reqVO.getAgencyName())
                .eqIfPresent(WorkOrderDO::getWorkOrderStatus, reqVO.getWorkOrderStatus())
                .eqIfPresent(WorkOrderDO::getPriority, reqVO.getPriority())
                .orderByDesc(WorkOrderDO::getCreateTime);

        // 处理工单类型查询条件（支持逗号拼接的字符串）
        if (StrUtil.isNotBlank(reqVO.getWorkOrderType())) {
            String workOrderType = reqVO.getWorkOrderType();
            if (workOrderType.contains(",")) {
                // 如果是逗号拼接的字符串，使用 in 查询
                List<String> typeList = StrUtil.split(workOrderType, ",");
                queryWrapper.in(WorkOrderDO::getWorkOrderType, typeList);
            } else {
                // 如果是单个值，使用 eq 查询
                queryWrapper.eq(WorkOrderDO::getWorkOrderType, workOrderType);
            }
        }

        // 执行分页查询
        PageResult<WorkOrderDO> pageResult = workOrderMapper.selectPage(reqVO, queryWrapper);

        // 转换为响应VO
        return WorkOrderConvert.INSTANCE.convertDOPageToVO(pageResult);
    }

    @Override
    public WorkOrderRespVO getWorkOrderDetail(String workOrderNo) {
        // 验证工单编号
        if (workOrderNo == null || workOrderNo.trim().isEmpty()) {
            throw new RuntimeException("工单编号不能为空");
        }
        
        // 根据工单编号查询工单
        WorkOrderDO workOrder = workOrderMapper.selectByWorkOrderNo(workOrderNo);
        if (workOrder == null) {
            return null;
        }

        // 转换为响应VO
        return WorkOrderConvert.INSTANCE.convertToVO(workOrder);
    }

    @Override
    public WorkOrderAcceptRespVO acceptWorkOrder(WorkOrderAcceptReqVO reqVO) {
        // 验证请求参数
        if (reqVO == null) {
            throw new RuntimeException("请求参数不能为空");
        }
        if (reqVO.getWorkOrderId() == null) {
            throw new RuntimeException("工单ID不能为空");
        }
        // 自动获取当前登录用户信息
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserName = SecurityFrameworkUtils.getLoginUserNickname();
        
        if (currentUserId == null) {
            throw new RuntimeException("无法获取当前登录用户信息");
        }
        
        // 根据工单ID查询工单
        WorkOrderDO workOrder = workOrderMapper.selectById(reqVO.getWorkOrderId());
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }

        // 检查工单状态
        if (workOrder.getWorkOrderStatus() == null || !"pending".equals(workOrder.getWorkOrderStatus())) {
            throw new RuntimeException("工单状态不允许接单，当前状态：" + workOrder.getWorkOrderStatus());
        }

        // 更新工单状态为处理中
        workOrder.setWorkOrderStatus("processing");
        workOrder.setUpdateTime(LocalDateTime.now());
        workOrderMapper.updateById(workOrder);

        // 记录操作日志
        WorkOrderLogDO log = new WorkOrderLogDO();
        log.setWorkOrderNo(workOrder.getWorkOrderNo());
        log.setLogType("accept");
        log.setLogContent("接单处理工单");
        log.setOperatorId(currentUserId);
        log.setCreateTime(LocalDateTime.now());
        // 设置日志标题
        log.setLogTitle("工单接单");
        workOrderLogMapper.insert(log);

        // 构建响应
        WorkOrderAcceptRespVO respVO = new WorkOrderAcceptRespVO();
        respVO.setWorkOrderNo(workOrder.getWorkOrderNo());
        respVO.setAcceptTime(LocalDateTime.now());
        respVO.setAssigneeId(currentUserId);
        respVO.setAssigneeName(currentUserName);
        respVO.setSuccess(true);

        return respVO;
    }

    @Override
    public WorkOrderTransferRespVO transferWorkOrder(WorkOrderTransferReqVO reqVO) {
        // 验证请求参数
        if (reqVO == null) {
            throw new RuntimeException("请求参数不能为空");
        }
        if (reqVO.getWorkOrderNo() == null || reqVO.getWorkOrderNo().trim().isEmpty()) {
            throw new RuntimeException("工单编号不能为空");
        }
        if (reqVO.getNewAssigneeId() == null) {
            throw new RuntimeException("新处理人ID不能为空");
        }
        if (reqVO.getNewAssigneeName() == null || reqVO.getNewAssigneeName().trim().isEmpty()) {
            throw new RuntimeException("新处理人姓名不能为空");
        }
        // 自动获取当前登录用户信息
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserName = SecurityFrameworkUtils.getLoginUserNickname();
        
        if (currentUserId == null) {
            throw new RuntimeException("无法获取当前登录用户信息");
        }
        
        // 根据工单编号查询工单
        WorkOrderDO workOrder = workOrderMapper.selectByWorkOrderNo(reqVO.getWorkOrderNo());
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }

        // 检查工单状态
        if (workOrder.getWorkOrderStatus() == null || "resolved".equals(workOrder.getWorkOrderStatus()) || "closed".equals(workOrder.getWorkOrderStatus())) {
            throw new RuntimeException("工单状态不允许提交处理结果，当前状态：" + workOrder.getWorkOrderStatus());
        }

        // 更新工单负责人
        workOrder.setAssigneeId(reqVO.getNewAssigneeId());
        workOrder.setAssigneeName(reqVO.getNewAssigneeName());
        // 更新转派原因和转派备注
        if (reqVO.getTransferReason() != null) {
            workOrder.setReassignmentReason(reqVO.getTransferReason());
        }
        if (reqVO.getTransferRemark() != null) {
            workOrder.setReassignmentRemark(reqVO.getTransferRemark());
        }
        // 更新优先级（如果提供）
        if (reqVO.getPriority() != null) {
            workOrder.setPriority(reqVO.getPriority());
        }
        workOrder.setUpdateTime(LocalDateTime.now());
        workOrderMapper.updateById(workOrder);

        // 记录操作日志
        WorkOrderLogDO log = new WorkOrderLogDO();
        log.setWorkOrderNo(reqVO.getWorkOrderNo());
        log.setLogType("transfer");
        // 构建详细的日志内容，包含转派原因和备注
        StringBuilder logContent = new StringBuilder("转派工单给：" + reqVO.getNewAssigneeName());
        if (reqVO.getTransferReason() != null) {
            logContent.append("，转派原因：").append(reqVO.getTransferReason());
        }
        if (reqVO.getTransferRemark() != null) {
            logContent.append("，转派备注：").append(reqVO.getTransferRemark());
        }
        if (reqVO.getPriority() != null) {
            logContent.append("，调整优先级为：").append(reqVO.getPriority());
        }
        log.setLogContent(logContent.toString());
        log.setOperatorId(currentUserId);
        log.setCreateTime(LocalDateTime.now());
        // 设置日志标题
        log.setLogTitle("工单转派");
        workOrderLogMapper.insert(log);

        // 构建响应
        WorkOrderTransferRespVO respVO = new WorkOrderTransferRespVO();
        respVO.setWorkOrderNo(reqVO.getWorkOrderNo());
        respVO.setTransferTime(LocalDateTime.now());
        respVO.setNewAssigneeId(reqVO.getNewAssigneeId());
        respVO.setNewAssigneeName(reqVO.getNewAssigneeName());
        respVO.setSuccess(true);

        return respVO;
    }

    @Override
    public List<WorkOrderLogRespVO> getWorkOrderLogs(String workOrderNo) {
        // 验证工单编号
        if (workOrderNo == null || workOrderNo.trim().isEmpty()) {
            throw new RuntimeException("工单编号不能为空");
        }
        
        // 构建查询条件
        LambdaQueryWrapperX<WorkOrderLogDO> queryWrapper = new LambdaQueryWrapperX<WorkOrderLogDO>()
                .eq(WorkOrderLogDO::getWorkOrderNo, workOrderNo)
                .orderByDesc(WorkOrderLogDO::getCreateTime);

        // 执行查询，获取所有日志记录
        List<WorkOrderLogDO> logList = workOrderLogMapper.selectList(queryWrapper);

        // 转换为响应VO
        List<WorkOrderLogRespVO> result = WorkOrderConvert.INSTANCE.convertLogDOListToVO(logList);
        
        // 处理日志类型转换和日期格式转换
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (WorkOrderLogRespVO log : result) {
            // 将日志类型转换为中文描述
            if (log.getLogType() != null) {
                log.setLogType(WorkOrderLogTypeEnum.getDescriptionByCode(log.getLogType()));
            }
            
            // 将创建时间转换为指定格式的字符串
            if (log.getCreateTime() != null) {
                log.setCreateTimeStr(log.getCreateTime().format(formatter));
            }
        }
        
        return result;
    }

    @Override
    public WorkOrderAttachmentRespVO uploadAttachment(WorkOrderAttachmentUploadReqVO reqVO) {
        // 创建附件记录
        WorkOrderAttachmentDO attachment = new WorkOrderAttachmentDO();
        attachment.setWorkOrderNo(reqVO.getWorkOrderNo());
        attachment.setFileName(reqVO.getFileName());
        attachment.setFileUrl(reqVO.getFileUrl());
        attachment.setFileCategory(reqVO.getFileCategory());
        // 设置文件类型（从文件名推断）
        if (reqVO.getFileName() != null && reqVO.getFileName().contains(".")) {
            String fileType = reqVO.getFileName().substring(reqVO.getFileName().lastIndexOf(".") + 1);
            attachment.setFileType(fileType.toLowerCase());
        }
        // 设置上传目的
        attachment.setUploadPurpose(reqVO.getUploadPurpose() != null ? reqVO.getUploadPurpose() : "工单附件");
        // 验证文件URL
        if (reqVO.getFileUrl() == null || reqVO.getFileUrl().trim().isEmpty()) {
            throw new RuntimeException("文件URL不能为空");
        }
        // 验证文件名
        if (reqVO.getFileName() == null || reqVO.getFileName().trim().isEmpty()) {
            throw new RuntimeException("文件名不能为空");
        }
        // 验证文件分类
        if (reqVO.getFileCategory() == null || reqVO.getFileCategory().trim().isEmpty()) {
            throw new RuntimeException("文件分类不能为空");
        }
        // 验证工单编号
        if (reqVO.getWorkOrderNo() == null || reqVO.getWorkOrderNo().trim().isEmpty()) {
            throw new RuntimeException("工单编号不能为空");
        }
        // 验证工单是否存在
        WorkOrderDO workOrder = workOrderMapper.selectByWorkOrderNo(reqVO.getWorkOrderNo());
        if (workOrder == null) {
            throw new RuntimeException("工单不存在，工单编号：" + reqVO.getWorkOrderNo());
        }

        // 保存附件
        workOrderAttachmentMapper.insert(attachment);
        // 确保ID被正确设置
        if (attachment.getId() == null) {
            throw new RuntimeException("附件保存失败，无法获取附件ID");
        }

        // 记录操作日志
        WorkOrderLogDO workOrderLog = new WorkOrderLogDO();
        workOrderLog.setWorkOrderNo(reqVO.getWorkOrderNo());
        workOrderLog.setLogType("upload_attachment");
        workOrderLog.setLogContent("上传附件：" + reqVO.getFileName());
        workOrderLog.setOperatorId(reqVO.getUploaderId());
        workOrderLog.setCreateTime(LocalDateTime.now());
        // 设置日志标题
        workOrderLog.setLogTitle("附件上传");
        workOrderLogMapper.insert(workOrderLog);
        // 确保日志ID被正确设置
        if (workOrderLog.getId() == null) {
            log.warn("日志保存失败，无法获取日志ID");
        }

        // 构建响应
        WorkOrderAttachmentRespVO respVO = new WorkOrderAttachmentRespVO();
        respVO.setId(attachment.getId());
        respVO.setWorkOrderNo(attachment.getWorkOrderNo());
        respVO.setFileName(attachment.getFileName());
        respVO.setFileUrl(attachment.getFileUrl());
        respVO.setFileCategory(attachment.getFileCategory());
        respVO.setFileType(attachment.getFileType());
        respVO.setUploadPurpose(attachment.getUploadPurpose());

        return respVO;
    }

    @Override
    public List<WorkOrderAttachmentRespVO> getAttachments(String workOrderNo) {
        // 验证工单编号
        if (workOrderNo == null || workOrderNo.trim().isEmpty()) {
            throw new RuntimeException("工单编号不能为空");
        }
        
        // 构建查询条件
        LambdaQueryWrapperX<WorkOrderAttachmentDO> queryWrapper = new LambdaQueryWrapperX<WorkOrderAttachmentDO>()
                .eq(WorkOrderAttachmentDO::getWorkOrderNo, workOrderNo)
                .orderByDesc(WorkOrderAttachmentDO::getCreateTime);

        // 执行查询，获取所有附件记录
        List<WorkOrderAttachmentDO> attachmentList = workOrderAttachmentMapper.selectList(queryWrapper);

        // 转换为响应VO
        return WorkOrderConvert.INSTANCE.convertAttachmentDOListToVO(attachmentList);
    }

    @Override
    public List<WorkOrderPractitionerRespVO> getWorkOrderPractitioners() {
        // 构建查询条件：平台状态为合作中，状态是正常，当前状态是待岗，deleted为0
        LambdaQueryWrapperX<PractitionerDO> queryWrapper = new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getPlatformStatus, "cooperating")  // 平台状态：合作中
                .eq(PractitionerDO::getStatus, "active")               // 状态：正常
                .eq(PractitionerDO::getCurrentStatus, "待岗");          // 当前状态：待岗
        
        // 执行查询，获取所有符合条件的阿姨记录
        List<PractitionerDO> practitionerList = practitionerMapper.selectList(queryWrapper);
        
        // 手动转换为响应VO
        List<WorkOrderPractitionerRespVO> result = new ArrayList<>();
        for (PractitionerDO practitioner : practitionerList) {
            WorkOrderPractitionerRespVO respVO = new WorkOrderPractitionerRespVO();
            respVO.setId(practitioner.getId());
            respVO.setAuntOneid(practitioner.getAuntOneid());
            respVO.setName(practitioner.getName());
            respVO.setRating(practitioner.getRating());
            respVO.setServiceType(practitioner.getServiceType());
            result.add(respVO);
        }
        
        return result;
    }

    @Override
    public WorkOrderTaskDetailRespVO getTaskDetail(String orderNo) {
        // 验证请求参数
        if (orderNo == null || orderNo.trim().isEmpty()) {
            throw new RuntimeException("订单号不能为空");
        }
        
        try {
            // 根据订单号查询订单详情
            LambdaQueryWrapperX<DomesticOrderDO> queryWrapper = new LambdaQueryWrapperX<DomesticOrderDO>()
                    .eq(DomesticOrderDO::getOrderNo, orderNo);
            
            DomesticOrderDO domesticOrder = domesticOrderMapper.selectOne(queryWrapper);

            // 查询该订单下的任务统计信息
            LambdaQueryWrapperX<DomesticTaskDO> taskQueryWrapper = new LambdaQueryWrapperX<DomesticTaskDO>()
                    .eq(DomesticTaskDO::getOrderNo, orderNo);
            
            List<DomesticTaskDO> allTasks = domesticTaskMapper.selectList(taskQueryWrapper);
            
            // 统计各种状态的任务数量
            int totalTaskCount = allTasks.size();
            int completedTaskCount = (int) allTasks.stream()
                    .filter(task -> "completed".equals(task.getTaskStatus()))
                    .count();
            int inProgressTaskCount = (int) allTasks.stream()
                    .filter(task -> "in_progress".equals(task.getTaskStatus()))
                    .count();
            int pendingTaskCount = (int) allTasks.stream()
                    .filter(task -> "pending".equals(task.getTaskStatus()) || "assigned".equals(task.getTaskStatus()))
                    .count();
            int cancelledTaskCount = (int) allTasks.stream()
                    .filter(task -> "cancelled".equals(task.getTaskStatus()))
                    .count();
            
            // 计算任务进度
            double taskProgress = totalTaskCount > 0 ? (double) completedTaskCount / totalTaskCount * 100 : 0.0;
            
            // 构建响应对象
            WorkOrderTaskDetailRespVO respVO = new WorkOrderTaskDetailRespVO();
            if (domesticOrder == null) {
                respVO.setOrderNo(orderNo);
                respVO.setServiceCategoryName("");
            }else{
                respVO.setOrderNo(domesticOrder.getOrderNo());
                respVO.setServiceCategoryName(domesticOrder.getServiceCategoryName());
            }
            respVO.setTaskCount(totalTaskCount);
            respVO.setCompletedTaskCount(completedTaskCount);
            respVO.setPendingTaskCount(pendingTaskCount);
            respVO.setInProgressTaskCount(inProgressTaskCount);
            respVO.setCancelledTaskCount(cancelledTaskCount);
            respVO.setTaskProgress(taskProgress);
            
            return respVO;
        } catch (Exception e) {
            log.error("获取任务详情失败，订单号：{}", orderNo, e);
            // 返回空的响应对象，避免影响接口调用
            WorkOrderTaskDetailRespVO respVO = new WorkOrderTaskDetailRespVO();
            respVO.setOrderNo(orderNo);
            respVO.setTaskCount(0);
            respVO.setCompletedTaskCount(0);
            respVO.setPendingTaskCount(0);
            respVO.setInProgressTaskCount(0);
            respVO.setCancelledTaskCount(0);
            respVO.setTaskProgress(0.0);
            return respVO;
        }
    }

    @Override
    public List<WorkOrderTaskRespVO> getTaskList(String orderNo, String taskStatus, 
                                                 String practitionerName, String practitionerOneid) {
        // 验证请求参数
        if (orderNo == null || orderNo.trim().isEmpty()) {
            throw new RuntimeException("订单号不能为空");
        }
        
        try {
            // 构建查询条件
            LambdaQueryWrapperX<DomesticTaskDO> queryWrapper = new LambdaQueryWrapperX<DomesticTaskDO>()
                    .eq(DomesticTaskDO::getOrderNo, orderNo)  // 订单号
                    .eqIfPresent(DomesticTaskDO::getTaskStatus, taskStatus)  // 任务状态（可选）
                    .likeIfPresent(DomesticTaskDO::getPractitionerName, practitionerName)  // 服务人员姓名（可选，模糊查询）
                    .eqIfPresent(DomesticTaskDO::getPractitionerOneid, practitionerOneid);  // 服务人员OneID（可选）

            
            // 执行查询，获取所有符合条件的任务记录
            List<DomesticTaskDO> taskList = domesticTaskMapper.selectList(queryWrapper);
            
            // 手动转换为响应VO
            List<WorkOrderTaskRespVO> result = new ArrayList<>();
            for (DomesticTaskDO task : taskList) {
                WorkOrderTaskRespVO respVO = new WorkOrderTaskRespVO();
                respVO.setId(task.getId());
                respVO.setTaskNo(task.getTaskNo());
                respVO.setTaskName(task.getTaskName());
                respVO.setTaskDescription(task.getTaskDescription());
                respVO.setTaskStatus(task.getTaskStatus());
                respVO.setPlannedStartTime(task.getPlannedStartTime());
                respVO.setPlannedEndTime(task.getPlannedEndTime());
                respVO.setActualStartTime(task.getActualStartTime());
                respVO.setActualEndTime(task.getActualEndTime());
                respVO.setPractitionerOneid(task.getPractitionerOneid());
                respVO.setPractitionerName(task.getPractitionerName());
                result.add(respVO);
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取任务列表失败，订单号：{}", orderNo, e);
            // 返回空列表而不是抛出异常，避免影响接口调用
            return new ArrayList<>();
        }
    }

    @Override
    public WorkOrderReassignTaskRespVO reassignTask(WorkOrderReassignTaskReqVO reqVO) {
        // 验证请求参数
        if (reqVO == null) {
            throw new RuntimeException("请求参数不能为空");
        }
        if (reqVO.getTaskNoList() == null || reqVO.getTaskNoList().isEmpty()) {
            throw new RuntimeException("任务编号列表不能为空");
        }
        if (reqVO.getNewPractitionerOneid() == null || reqVO.getNewPractitionerOneid().trim().isEmpty()) {
            throw new RuntimeException("新执行人员OneID不能为空");
        }
        if (reqVO.getNewPractitionerName() == null || reqVO.getNewPractitionerName().trim().isEmpty()) {
            throw new RuntimeException("新执行人员姓名不能为空");
        }
        // 自动获取当前登录用户信息
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserName = SecurityFrameworkUtils.getLoginUserNickname();
        
        if (currentUserId == null) {
            throw new RuntimeException("无法获取当前登录用户信息");
        }
        
        // 验证任务是否存在
        List<DomesticTaskDO> existingTasks = domesticTaskMapper.selectByTaskNoList(reqVO.getTaskNoList());
        if (existingTasks.size() != reqVO.getTaskNoList().size()) {
            throw new RuntimeException("部分任务不存在，请检查任务编号");
        }
        
        // 验证任务状态是否允许重新指派
        for (DomesticTaskDO task : existingTasks) {
            if (!"pending".equals(task.getTaskStatus()) && !"assigned".equals(task.getTaskStatus())) {
                throw new RuntimeException("任务 " + task.getTaskNo() + " 状态不允许重新指派，当前状态：" + task.getTaskStatus());
            }
        }
        
        // 根据新执行人员OneID查找阿姨手机号
        LambdaQueryWrapperX<PractitionerDO> practitionerQueryWrapper = new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getAuntOneid, reqVO.getNewPractitionerOneid());
        PractitionerDO practitioner = practitionerMapper.selectOne(practitionerQueryWrapper);
        
        if (practitioner == null) {
            throw new RuntimeException("未找到新执行人员信息，OneID：" + reqVO.getNewPractitionerOneid());
        }
        
        // 获取阿姨手机号
        String practitionerPhone = practitioner.getPhone();
        if (practitionerPhone == null || practitionerPhone.trim().isEmpty()) {
            log.warn("新执行人员手机号为空，OneID：{}", reqVO.getNewPractitionerOneid());
            practitionerPhone = ""; // 设置为空字符串避免空指针
        }
        
        // 更新任务执行人
        int updatedCount = domesticTaskMapper.updatePractitionerByTaskNoList(
            reqVO.getTaskNoList(),
            reqVO.getNewPractitionerOneid(),
            reqVO.getNewPractitionerName(),
            practitionerPhone
        );
        
        if (updatedCount != reqVO.getTaskNoList().size()) {
            throw new RuntimeException("更新任务执行人失败，预期更新 " + reqVO.getTaskNoList().size() + " 条，实际更新 " + updatedCount + " 条");
        }
        
        // 记录操作日志（为每个任务记录一条日志）
        for (String taskNo : reqVO.getTaskNoList()) {
            WorkOrderLogDO log = new WorkOrderLogDO();
            log.setWorkOrderNo(taskNo); // 这里使用任务编号作为工单编号
            log.setLogType("task_reassignment");
            log.setLogTitle("任务重新指派");
            log.setLogContent("重新指派任务给：" + reqVO.getNewPractitionerName());
            log.setOperatorId(currentUserId);
            log.setCreateTime(LocalDateTime.now());
            workOrderLogMapper.insert(log);
        }
        
        // 构建响应
        WorkOrderReassignTaskRespVO respVO = new WorkOrderReassignTaskRespVO();
        respVO.setSuccess(true);
        respVO.setReassignedCount(updatedCount);
        respVO.setFailedTasks(new ArrayList<>()); // 没有失败的任务
        
        return respVO;
    }

    @Override
    public WorkOrderSubmitResolutionRespVO submitResolution(WorkOrderSubmitResolutionReqVO reqVO) {
        // 验证请求参数
        if (reqVO == null) {
            throw new RuntimeException("请求参数不能为空");
        }
        if (reqVO.getWorkOrderNo() == null || reqVO.getWorkOrderNo().trim().isEmpty()) {
            throw new RuntimeException("工单编号不能为空");
        }
        if (reqVO.getLogContent() == null || reqVO.getLogContent().trim().isEmpty()) {
            throw new RuntimeException("处理结果内容不能为空");
        }
        
        // 自动获取当前登录用户信息
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserName = SecurityFrameworkUtils.getLoginUserNickname();
        
        if (currentUserId == null) {
            throw new RuntimeException("无法获取当前登录用户信息");
        }
        
        // 根据工单编号查询工单
        WorkOrderDO workOrder = workOrderMapper.selectByWorkOrderNo(reqVO.getWorkOrderNo());
        if (workOrder == null) {
            throw new RuntimeException("工单不存在");
        }

        // 更新工单状态为已解决
        workOrder.setWorkOrderStatus("resolved");
        workOrder.setStatus(1);
        workOrder.setUpdateTime(LocalDateTime.now());
        workOrderMapper.updateById(workOrder);
          // 保存附件信息
        if (reqVO.getAttachments() != null && !reqVO.getAttachments().isEmpty()) {
            for (WorkOrderSubmitResolutionReqVO.Attachment attachment : reqVO.getAttachments()) {
                // 创建附件记录
                WorkOrderAttachmentDO workOrderAttachment = new WorkOrderAttachmentDO();
                workOrderAttachment.setWorkOrderNo(reqVO.getWorkOrderNo());
                workOrderAttachment.setFileName(attachment.getFileName());
                workOrderAttachment.setFileUrl(attachment.getFileUrl());
                workOrderAttachment.setFileType(attachment.getFileType());
                workOrderAttachment.setFileCategory(attachment.getFileCategory());
                workOrderAttachment.setUploadPurpose(attachment.getUploadPurpose());
                workOrderAttachment.setCreateTime(LocalDateTime.now());
                
                // 保存附件记录
                workOrderAttachmentMapper.insert(workOrderAttachment);
            }
        }


        // 记录操作日志
        WorkOrderLogDO log = new WorkOrderLogDO();
        log.setWorkOrderNo(reqVO.getWorkOrderNo());
        log.setLogType("submit_resolution");
        log.setLogContent("提交处理结果：" + reqVO.getLogContent());
        log.setOperatorId(currentUserId);
        log.setCreateTime(LocalDateTime.now());
        // 设置日志标题
        log.setLogTitle("提交处理结果");
        workOrderLogMapper.insert(log);

      
        // 构建响应
        WorkOrderSubmitResolutionRespVO respVO = new WorkOrderSubmitResolutionRespVO();
        respVO.setWorkOrderNo(reqVO.getWorkOrderNo());
        respVO.setSubmitTime(LocalDateTime.now());
        respVO.setSuccess(true);

        return respVO;
    }

    @Override
    public WorkOrderAssignAuntRespVO assignAunt(WorkOrderAssignAuntReqVO reqVO) {
        // 验证请求参数
        if (reqVO == null) {
            throw new RuntimeException("请求参数不能为空");
        }
        if (reqVO.getWorkOrderNo() == null || reqVO.getWorkOrderNo().trim().isEmpty()) {
            throw new RuntimeException("工单编号不能为空");
        }
        if (reqVO.getAuntOneid() == null || reqVO.getAuntOneid().trim().isEmpty()) {
            throw new RuntimeException("阿姨OneID不能为空");
        }
        if (reqVO.getAuntName() == null || reqVO.getAuntName().trim().isEmpty()) {
            throw new RuntimeException("阿姨姓名不能为空");
        }
        if (reqVO.getAssignmentReason() == null || reqVO.getAssignmentReason().trim().isEmpty()) {
            throw new RuntimeException("指派原因不能为空");
        }
        // 自动获取当前登录用户信息
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserName = SecurityFrameworkUtils.getLoginUserNickname();
        
        if (currentUserId == null) {
            throw new RuntimeException("无法获取当前登录用户信息");
        }
        
        // 验证工单是否存在
        WorkOrderDO workOrder = workOrderMapper.selectByWorkOrderNo(reqVO.getWorkOrderNo());
        if (workOrder == null) {
            throw new RuntimeException("工单不存在，工单编号：" + reqVO.getWorkOrderNo());
        }
        
        // 验证工单状态是否允许指派阿姨
        if (workOrder.getWorkOrderStatus() == null || 
            (!"pending".equals(workOrder.getWorkOrderStatus()) && 
             !"processing".equals(workOrder.getWorkOrderStatus()))) {
            throw new RuntimeException("工单状态不允许指派阿姨，当前状态：" + workOrder.getWorkOrderStatus());
        }
        
        // 根据订单号和重新指派时间查找待执行的任务
        List<String> pendingTaskCodes = new ArrayList<>();
        if (reqVO.getOrderNo() != null && reqVO.getReassignmentTime() != null) {
           
                // 将重新指派时间字符串转换为LocalDateTime，支持多种日期格式
                LocalDateTime reassignmentDateTime;
                String reassignmentTime = reqVO.getReassignmentTime();
                if (reassignmentTime.length() == 10) {
                    // 格式：2024-04-12
                    reassignmentDateTime = LocalDateTime.parse(reassignmentTime + " 00:00:00", java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } else {
                    // 格式：2024-04-12 10:30:00
                    reassignmentDateTime = LocalDateTime.parse(reassignmentTime, java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                // 构建查询条件：查找该订单下开始计划时间大于等于指派时间的所有待执行任务
                LambdaQueryWrapperX<DomesticTaskDO> taskQueryWrapper = new LambdaQueryWrapperX<>();
                taskQueryWrapper.eq(DomesticTaskDO::getOrderNo, reqVO.getOrderNo())
                        .ge(DomesticTaskDO::getPlannedStartTime, reassignmentDateTime)
                        .in(DomesticTaskDO::getTaskStatus, "pending", "assigned");
                
                List<DomesticTaskDO> pendingTasks = domesticTaskMapper.selectList(taskQueryWrapper);
                
                // 提取任务编码
                for (DomesticTaskDO task : pendingTasks) {
                    if (task.getTaskNo() != null && !task.getTaskNo().trim().isEmpty()) {
                        pendingTaskCodes.add(task.getTaskNo());
                    }
                }
        }
         // 如果找到待执行任务，自动调用reassignTask方法重新指派任务
        if (!pendingTaskCodes.isEmpty()) {
                // 构建重新指派任务的请求对象
                WorkOrderReassignTaskReqVO reassignReqVO = new WorkOrderReassignTaskReqVO();
                reassignReqVO.setTaskNoList(pendingTaskCodes);
                reassignReqVO.setNewPractitionerOneid(reqVO.getAuntOneid());
                reassignReqVO.setNewPractitionerName(reqVO.getAuntName());
                
                // 调用reassignTask方法重新指派任务
                WorkOrderReassignTaskRespVO reassignResult = reassignTask(reassignReqVO);
        }
        
        // 更新工单的阿姨信息
        workOrder.setNewAuntOneid(reqVO.getAuntOneid());
        workOrder.setNewAuntName(reqVO.getAuntName());
        // 将字符串转换为LocalDate
        if (reqVO.getReassignmentTime() != null) {
            try {
                LocalDate reassignmentDate = LocalDate.parse(reqVO.getReassignmentTime().substring(0, 10));
                workOrder.setReassignmentStartDate(reassignmentDate);
            } catch (Exception e) {
                log.warn("无法解析重新指派时间：{}", reqVO.getReassignmentTime());
            }
        }
        workOrder.setReassignmentDescription(reqVO.getAssignmentReason());
        workOrder.setUpdateTime(LocalDateTime.now());
        workOrderMapper.updateById(workOrder);
        
       
        
        // 记录操作日志
        WorkOrderLogDO log = new WorkOrderLogDO();
        log.setWorkOrderNo(reqVO.getWorkOrderNo());
        log.setLogType("assign_aunt");
        log.setLogTitle("指派阿姨");
        log.setLogContent("指派阿姨：" + reqVO.getAuntName() + "，原因：" + reqVO.getAssignmentReason());
        log.setOperatorId(currentUserId);
        log.setCreateTime(LocalDateTime.now());
        workOrderLogMapper.insert(log);
        
        // 构建响应
        WorkOrderAssignAuntRespVO respVO = new WorkOrderAssignAuntRespVO();
        respVO.setSuccess(true);
        return respVO;
    }

    @Override
    public WorkOrderTypeStatsRespVO getTypeStats() {
        // 获取当前用户的机构ID
        Long currentUserAgencyId = SecurityFrameworkUtils.getLoginUser() != null ?
            SecurityFrameworkUtils.getLoginUser().getTenantId() : null;
        
        if (currentUserAgencyId == null) {
            log.warn("无法获取当前用户机构ID，返回空统计结果");
            return buildEmptyStats();
        }
        // 统计总工单数量
        Long totalCount = workOrderMapper.selectTotalCountByAgencyId(currentUserAgencyId);

        // 构建统计结果
        WorkOrderTypeStatsRespVO respVO = new WorkOrderTypeStatsRespVO();
        respVO.setAll(totalCount != null ? totalCount.intValue() : 0);
        
        // 初始化各类型工单数量为0 complaint-投诉/substitution_request-换人申请/take_leave-请假/顶岗
        Long complaintCount = workOrderMapper.selectCountByAgencyIdAndType(currentUserAgencyId,"complaint");//投诉
        Long substitutionRequestCount = workOrderMapper.selectCountByAgencyIdAndType(currentUserAgencyId,"substitution_request");
        
        // 统计请假相关工单数量（包括take_leave和leave_adjustment两种类型）
        List<String> leaveTypes = new ArrayList<>();
        leaveTypes.add("take_leave");
        leaveTypes.add("leave_adjustment");
        Long takeLeaveCount = workOrderMapper.selectCountByAgencyIdAndTypes(currentUserAgencyId, leaveTypes);

        // 设置各类型工单数量
        respVO.setComplaint(complaintCount != null ? complaintCount.intValue() : 0);
        respVO.setSubstitutionRequest(substitutionRequestCount != null ? substitutionRequestCount.intValue() : 0);
        respVO.setTakeLeave(takeLeaveCount != null ? takeLeaveCount.intValue() : 0);

        return respVO;
    }
    
    /**
     * 构建空的统计结果
     *
     * @return 空的统计结果
     */
    private WorkOrderTypeStatsRespVO buildEmptyStats() {
        WorkOrderTypeStatsRespVO respVO = new WorkOrderTypeStatsRespVO();
        respVO.setAll(0);
        respVO.setComplaint(0);
        respVO.setSubstitutionRequest(0);
        respVO.setTakeLeave(0);
        return respVO;
    }
}
