package cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 家政服务任务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DomesticTaskMapper extends BaseMapperX<DomesticTaskDO> {

    /**
     * 根据阿姨OneID和日期范围查询排班任务
     *
     * @param auntOneId 阿姨OneID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 排班任务列表
     */
    List<DomesticTaskDO> selectByAuntOneIdAndDateRange(@Param("auntOneId") String auntOneId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    /**
     * 根据阿姨OneID查询指定日期的排班任务
     *
     * @param auntOneId    阿姨OneID
     * @param scheduleDate 排班日期
     * @return 排班任务列表
     */
    List<DomesticTaskDO> selectByAuntOneIdAndScheduleDate(@Param("auntOneId") String auntOneId,
            @Param("scheduleDate") LocalDate scheduleDate);

    /**
     * 根据阿姨OneID查询进行中的任务
     *
     * @param auntOneId 阿姨OneID
     * @return 进行中的任务列表
     */
    List<DomesticTaskDO> selectInProgressByAuntOneId(@Param("auntOneId") String auntOneId);

    /**
     * 查询阿姨今日排班数
     *
     * @param auntOneId    阿姨OneID
     * @param scheduleDate 排班日期
     * @return 今日排班任务数量
     */
    Integer selectTodayScheduleCount(@Param("auntOneId") String auntOneId,
            @Param("scheduleDate") LocalDate scheduleDate);

    /**
     * 查询阿姨本月服务时长
     *
     * @param auntOneId 阿姨OneID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 本月服务时长（小时）
     */
    Integer selectMonthlyServiceHours(@Param("auntOneId") String auntOneId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 根据任务编号列表查询任务
     *
     * @param taskNoList 任务编号列表
     * @return 任务列表
     */
    List<DomesticTaskDO> selectByTaskNoList(@Param("taskNoList") List<String> taskNoList);

    /**
     * 根据任务编号查询任务
     *
     * @param taskNo 任务编号
     * @return 任务对象
     */
    DomesticTaskDO selectByTaskNo(@Param("taskNo") String taskNo);

    /**
     * 根据任务编号列表更新服务人员信息
     *
     * @param taskNoList        任务编号列表
     * @param practitionerOneid 服务人员OneID
     * @param practitionerName  服务人员姓名
     * @param practitionerPhone 服务人员电话
     * @return 更新记录数
     */
    int updatePractitionerByTaskNoList(@Param("taskNoList") List<String> taskNoList,
            @Param("practitionerOneid") String practitionerOneid,
            @Param("practitionerName") String practitionerName,
            @Param("practitionerPhone") String practitionerPhone);

    /**
     * 根据订单ID分页查询任务列表
     *
     * @param orderId    订单ID
     * @param taskStatus 任务状态
     * @param executor   执行人
     * @param offset     偏移量
     * @param limit      限制数量
     * @return 任务列表
     */
    List<DomesticTaskDO> selectByOrderIdWithConditions(@Param("orderId") Long orderId,
            @Param("taskStatus") String taskStatus,
            @Param("executor") String executor,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit);

    /**
     * 根据订单ID和条件统计任务数量
     *
     * @param orderId    订单ID
     * @param taskStatus 任务状态
     * @param executor   执行人
     * @return 任务数量
     */
    Long countByOrderIdWithConditions(@Param("orderId") Long orderId,
            @Param("taskStatus") String taskStatus,
            @Param("executor") String executor);

    /**
     * 根据任务ID更新任务状态
     *
     * @param taskId      任务ID
     * @param taskStatus  任务状态
     * @param actualStartTime 实际开始时间
     * @param actualEndTime   实际结束时间
     * @return 更新记录数
     */
    int updateTaskStatus(@Param("taskId") Long taskId,
            @Param("taskStatus") String taskStatus,
            @Param("actualStartTime") LocalDateTime actualStartTime,
            @Param("actualEndTime") LocalDateTime actualEndTime);

    /**
     * 根据任务ID更新任务信息
     *
     * @param taskId          任务ID
     * @param taskName        任务名称
     * @param taskDescription 任务描述
     * @param plannedStartTime 计划开始时间
     * @param plannedEndTime   计划结束时间
     * @return 更新记录数
     */
    int updateTaskInfo(@Param("taskId") Long taskId,
            @Param("taskName") String taskName,
            @Param("taskDescription") String taskDescription,
            @Param("plannedStartTime") LocalDateTime plannedStartTime,
            @Param("plannedEndTime") LocalDateTime plannedEndTime);

    /**
     * 根据任务ID更新打卡信息
     *
     * @param taskId        任务ID
     * @param punchInTime   打卡开始时间
     * @param punchOutTime  打卡结束时间
     * @param punchLocation 打卡位置
     * @param punchLatitude 打卡纬度
     * @param punchLongitude 打卡经度
     * @return 更新记录数
     */
    int updatePunchInfo(@Param("taskId") Long taskId,
            @Param("punchInTime") LocalDateTime punchInTime,
            @Param("punchOutTime") LocalDateTime punchOutTime,
            @Param("punchLocation") String punchLocation,
            @Param("punchLatitude") BigDecimal punchLatitude,
            @Param("punchLongitude") BigDecimal punchLongitude);

    /**
     * 根据订单ID查询任务列表，按task_sequence升序排序
     *
     * @param orderId 订单ID
     * @return 任务列表
     */
    List<DomesticTaskDO> selectByOrderIdOrderByTaskSequence(@Param("orderId") Long orderId);

    /**
     * 根据阿姨OneID和月份统计每日任务数量
     *
     * @param auntOneId 阿姨OneID
     * @param startDate 月份开始日期
     * @param endDate   月份结束日期
     * @return 每日任务统计结果
     */
    List<Map<String, Object>> selectDailyTaskStatsByAuntOneId(@Param("auntOneId") String auntOneId,
                                                              @Param("startDate") LocalDate startDate,
                                                              @Param("endDate") LocalDate endDate);

    /**
     * 根据订单ID查询正在进行中的任务
     *
     * @param orderId 订单ID
     * @return 正在进行中的任务
     */
    DomesticTaskDO selectInProgressTaskByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单ID列表查询进行中的任务
     *
     * @param orderIds 订单ID列表
     * @return 进行中的任务列表
     */
    List<DomesticTaskDO> selectInProgressTasksByOrderIds(@Param("orderIds") List<Long> orderIds);

}