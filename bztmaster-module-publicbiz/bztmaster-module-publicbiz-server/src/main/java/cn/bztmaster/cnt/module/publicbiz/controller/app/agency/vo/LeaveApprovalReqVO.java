package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "请假审批请求VO")
@Data
public class LeaveApprovalReqVO {

    @Schema(description = "审批动作：approve-同意, reject-拒绝", required = true)
    @NotBlank(message = "审批动作不能为空")
    private String action;

    @Schema(description = "审批备注")
    private String remark;

    @Schema(description = "审批人ID", required = true)
    @NotNull(message = "审批人ID不能为空")
    private Long operatorId;

    @Schema(description = "审批人姓名", required = true)
    @NotBlank(message = "审批人姓名不能为空")
    private String operatorName;
}
