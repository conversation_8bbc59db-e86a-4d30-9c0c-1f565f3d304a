package cn.bztmaster.cnt.module.publicbiz.service.order.impl;

import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.UniversityPracticeOrderRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.UniversityPracticeOrderSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionRespDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageRespVO;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderContractUploadReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderContractUploadRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPaperContractUploadReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPaperContractUploadRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderExportReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderExcelVO;
import cn.bztmaster.cnt.module.publicbiz.convert.order.UniversityPracticeOrderConvert;
import cn.bztmaster.cnt.module.publicbiz.util.PaymentNoGenerator;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderLogService;
import java.util.HashMap;
import java.util.ArrayList;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.date.DateUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizPracticeOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizPartnerContractDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizPracticeOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderPaymentMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizPartnerContractMapper;
import cn.bztmaster.cnt.module.publicbiz.framework.common.exception.ErrorCodeConstants;
import cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants;
import cn.bztmaster.cnt.module.publicbiz.service.order.UniversityPracticeOrderService;
import cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils;
import cn.bztmaster.cnt.framework.web.core.util.WebFrameworkUtils;
import cn.hutool.core.collection.CollUtil;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.mzt.logapi.context.LogRecordContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.util.StringUtils;
import java.io.IOException;

import javax.annotation.Resource;


/**
 * 高校实践订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class UniversityPracticeOrderServiceImpl implements UniversityPracticeOrderService {

    @Resource
    private PublicbizOrderMapper orderMapper;

    @Resource
    private PublicbizPracticeOrderMapper practiceOrderMapper;

    @Resource
    private PublicbizOrderLogMapper orderLogMapper;

    @Resource
    private PublicbizOrderPaymentMapper publicbizOrderPaymentMapper;

    @Resource
    private PublicbizPartnerContractMapper partnerContractMapper;

    @Resource
    private OrderLogService orderLogService;



    /**
     * 创建支付记录
     *
     * @param orderId 订单ID
     * @param orderNo 订单号
     * @param createReqVO 创建请求VO
     */
    private void createPaymentRecord(Long orderId, String orderNo, UniversityPracticeOrderSaveReqVO createReqVO) {
        try {
            log.info("开始创建支付记录，订单ID：{}，订单号：{}", orderId, orderNo);
            
            // 生成支付单号
            String paymentNo = PaymentNoGenerator.generatePaymentNoWithRetry(
                no -> publicbizOrderPaymentMapper.selectByPaymentNo(no) != null, 3);
            
            // 创建支付记录
            PublicbizOrderPaymentDO paymentRecord = PublicbizOrderPaymentDO.builder()
                    .orderId(orderId)
                    .orderNo(orderNo)
                    .paymentNo(paymentNo)
                    .paymentType(createReqVO.getCollectionMethod() != null ? createReqVO.getCollectionMethod() : "other")
                    .paymentAmount(createReqVO.getCollectionAmount() != null ? createReqVO.getCollectionAmount() : createReqVO.getTotalAmount())
                    .paymentStatus("success")
                    .paymentTime(LocalDateTime.now())
                    .operatorId(WebFrameworkUtils.getLoginUserId())
                    .operatorName(createReqVO.getOperatorName())
                    .paymentRemark(createReqVO.getCollectionRemark())
                    .build();
            
            int paymentInsertResult = publicbizOrderPaymentMapper.insert(paymentRecord);
            if (paymentInsertResult <= 0) {
                log.error("创建支付记录失败，订单ID：{}", orderId);
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.PAYMENT_RECORD_CREATE_FAILED);
            }
            
            log.info("支付记录创建成功，支付单号：{}，订单ID：{}", paymentNo, orderId);
            
        } catch (Exception e) {
            log.error("创建支付记录异常，订单ID：{}，订单号：{}", orderId, orderNo, e);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PAYMENT_RECORD_CREATE_FAILED);
        }
    }

    /**
     * 处理支付记录更新
     *
     * @param updateReqVO 更新请求VO
     * @param oldOrder 原订单信息
     */
    private void handlePaymentRecordUpdate(UniversityPracticeOrderSaveReqVO updateReqVO, PublicbizOrderDO oldOrder) {
        try {
            // 如果支付状态从非paid变为paid，需要创建支付记录
            if ("paid".equals(updateReqVO.getPaymentStatus()) && !"paid".equals(oldOrder.getPaymentStatus())) {
                log.info("支付状态变为已支付，开始创建支付记录，订单ID：{}", updateReqVO.getId());
                createPaymentRecord(updateReqVO.getId(), updateReqVO.getOrderNo(), updateReqVO);
            }
            // 如果支付状态保持为paid，需要更新支付记录
            else if ("paid".equals(updateReqVO.getPaymentStatus()) && "paid".equals(oldOrder.getPaymentStatus())) {
                log.info("支付状态保持为已支付，开始更新支付记录，订单ID：{}", updateReqVO.getId());
                updatePaymentRecord(updateReqVO.getId(), updateReqVO);
            }
            // 如果支付状态从paid变为非paid，需要删除支付记录
            else if (!"paid".equals(updateReqVO.getPaymentStatus()) && "paid".equals(oldOrder.getPaymentStatus())) {
                log.info("支付状态从已支付变为非已支付，开始删除支付记录，订单ID：{}", updateReqVO.getId());
                deletePaymentRecord(updateReqVO.getId());
            }
        } catch (Exception e) {
            log.error("处理支付记录更新异常，订单ID：{}", updateReqVO.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新支付记录
     *
     * @param orderId 订单ID
     * @param updateReqVO 更新请求VO
     */
    private void updatePaymentRecord(Long orderId, UniversityPracticeOrderSaveReqVO updateReqVO) {
        try {
            // 查询现有的支付记录
            List<PublicbizOrderPaymentDO> existingPayments = publicbizOrderPaymentMapper.selectByOrderIdAndStatus(orderId, "success");
            if (existingPayments.isEmpty()) {
                log.warn("未找到成功的支付记录，订单ID：{}", orderId);
                return;
            }
            
            // 更新第一条成功的支付记录
            PublicbizOrderPaymentDO paymentRecord = existingPayments.get(0);
            paymentRecord.setPaymentType(updateReqVO.getCollectionMethod() != null ? updateReqVO.getCollectionMethod() : "other");
            paymentRecord.setPaymentAmount(updateReqVO.getCollectionAmount() != null ? updateReqVO.getCollectionAmount() : updateReqVO.getTotalAmount());
            paymentRecord.setPaymentTime(LocalDateTime.now());
            paymentRecord.setOperatorName(updateReqVO.getOperatorName());
            paymentRecord.setPaymentRemark(updateReqVO.getCollectionRemark());
            
            int updateResult = publicbizOrderPaymentMapper.updateById(paymentRecord);
            if (updateResult <= 0) {
                log.error("更新支付记录失败，支付记录ID：{}", paymentRecord.getId());
            } else {
                log.info("支付记录更新成功，支付记录ID：{}", paymentRecord.getId());
            }
            
        } catch (Exception e) {
            log.error("更新支付记录异常，订单ID：{}", orderId, e);
        }
    }

    /**
     * 删除支付记录
     *
     * @param orderId 订单ID
     */
    private void deletePaymentRecord(Long orderId) {
        try {
            int deleteResult = publicbizOrderPaymentMapper.deleteByOrderId(orderId);
            log.info("删除支付记录完成，订单ID：{}，删除记录数：{}", orderId, deleteResult);
        } catch (Exception e) {
            log.error("删除支付记录异常，订单ID：{}", orderId, e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.UNIVERSITY_PRACTICE_ORDER_TYPE, subType = "创建高校实践订单", bizNo = "{{#createReqVO.orderNo}}", success = "创建了高校实践订单【{{#createReqVO.orderNo}}】")
    public Long createOrder(UniversityPracticeOrderSaveReqVO createReqVO) {
        log.info("开始创建高校实践订单，项目名称：{}，高校：{}，企业：{}", 
                createReqVO.getProjectName(), createReqVO.getUniversityName(), createReqVO.getEnterpriseName());

        // 1. 参数验证
        validateCreateOrderParams(createReqVO);

        // 2. 生成订单号
        String orderNo = generateOrderNoWithRetry();

        // 3. 创建订单主表记录
        PublicbizOrderDO order = new PublicbizOrderDO();
        order.setOrderNo(orderNo);
        order.setOrderType("practice");
        order.setBusinessLine("高校实践");
        // 设置线索和商机字段
        order.setOpportunityId(createReqVO.getOpportunityId());
        order.setLeadId(createReqVO.getLeadId());
        order.setProjectName(createReqVO.getProjectName());
        order.setProjectDescription(createReqVO.getProjectDescription());
        order.setStartDate(createReqVO.getStartDate());
        order.setEndDate(createReqVO.getEndDate());
        order.setTotalAmount(createReqVO.getTotalAmount());
        order.setPaidAmount(BigDecimal.ZERO); // 初始化为0
        order.setRefundAmount(BigDecimal.ZERO);
        order.setPaymentStatus(createReqVO.getPaymentStatus() != null ? createReqVO.getPaymentStatus() : "pending");
        // 设置默认订单状态为"审批中"
        order.setOrderStatus("pending_approval");
        order.setManagerId(createReqVO.getManagerId());
        order.setManagerName(createReqVO.getManagerName());
        order.setManagerPhone(createReqVO.getManagerPhone());
        order.setContractType(createReqVO.getContractType() != null ? createReqVO.getContractType() : "electronic");
        order.setContractFileUrl(createReqVO.getContractFileUrl());
        order.setRemark(createReqVO.getRemark());
        order.setSettlementStatus("pending");
        
        // 记录合同文件URL设置日志
        log.info("设置合同文件URL：{}", createReqVO.getContractFileUrl());

        // 4. 如果支付状态为已支付，设置收款信息（使用现有字段）
        if ("paid".equals(createReqVO.getPaymentStatus())) {
            // 使用paidAmount字段存储收款金额
            order.setPaidAmount(createReqVO.getCollectionAmount() != null ? createReqVO.getCollectionAmount() : createReqVO.getTotalAmount());
            order.setSettlementStatus("processing");
            order.setSettlementTime(LocalDateTime.now());
            // 使用settlementMethod字段存储收款方式
            order.setSettlementMethod(createReqVO.getCollectionMethod());
            // 使用selectorName字段存储操作人姓名
            order.setSelectorName(createReqVO.getOperatorName());
            // 使用remark字段存储收款备注（如果原remark为空，则使用收款备注）
            if (StrUtil.isBlank(order.getRemark()) && StrUtil.isNotBlank(createReqVO.getCollectionRemark())) {
                order.setRemark(createReqVO.getCollectionRemark());
            }
        }

        // 记录插入前的订单对象信息（避免序列化整个对象，只记录关键字段）
        log.info("准备插入订单主表，订单号：{}，项目名称：{}，合同文件URL：{}", 
                order.getOrderNo(), order.getProjectName(), order.getContractFileUrl());
        
        int insertResult = orderMapper.insert(order);
        if (insertResult <= 0) {
            log.error("创建订单主表记录失败");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_CREATE_FAILED);
        }
        
        log.info("订单主表插入成功，订单ID：{}", order.getId());

        // 5. 创建高校实践订单详情记录
        PublicbizPracticeOrderDO practiceOrder = new PublicbizPracticeOrderDO();
        practiceOrder.setOrderId(order.getId());
        practiceOrder.setOrderNo(orderNo);
        practiceOrder.setUniversityName(createReqVO.getUniversityName());
        practiceOrder.setUniversityContact(createReqVO.getUniversityContact());
        practiceOrder.setUniversityPhone(createReqVO.getUniversityPhone());
        practiceOrder.setUniversityEmail(createReqVO.getUniversityEmail());
        practiceOrder.setEnterpriseName(createReqVO.getEnterpriseName());
        practiceOrder.setEnterpriseContact(createReqVO.getEnterpriseContact());
        practiceOrder.setEnterprisePhone(createReqVO.getEnterprisePhone());
        practiceOrder.setEnterpriseEmail(createReqVO.getEnterpriseEmail());
        practiceOrder.setProjectName(createReqVO.getProjectName());
        practiceOrder.setProjectDescription(createReqVO.getProjectDescription());
        practiceOrder.setStudentCount(createReqVO.getStudentCount());
        practiceOrder.setPracticeDuration(createReqVO.getPracticeDuration());
        practiceOrder.setPracticeLocation(createReqVO.getPracticeLocation());
        practiceOrder.setServiceFee(createReqVO.getServiceFee());
        practiceOrder.setManagementFee(createReqVO.getManagementFee());
        practiceOrder.setOtherFee(createReqVO.getOtherFee());

        int detailInsertResult = practiceOrderMapper.insert(practiceOrder);
        if (detailInsertResult <= 0) {
            log.error("创建高校实践订单详情记录失败，订单ID：{}", order.getId());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_DETAIL_CREATE_FAILED);
        }

        // 6. 如果支付状态为已支付，创建支付记录
        if ("paid".equals(createReqVO.getPaymentStatus())) {
            createPaymentRecord(order.getId(), orderNo, createReqVO);
        }

        // 6. 记录操作日志
        Map<String, Object> orderDetails = new HashMap<>();
        orderDetails.put("projectName", createReqVO.getProjectName());
        orderDetails.put("universityName", createReqVO.getUniversityName());
        orderDetails.put("enterpriseName", createReqVO.getEnterpriseName());
        orderDetails.put("totalAmount", createReqVO.getTotalAmount());
        
        orderLogService.recordOrderCreate(orderNo, "practice", orderDetails, null, null, null);


        return order.getId();
    }

    /**
     * 验证创建订单参数
     */
    private void validateCreateOrderParams(UniversityPracticeOrderSaveReqVO createReqVO) {
        // 1. 基础参数验证
        if (StrUtil.isBlank(createReqVO.getProjectName())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PROJECT_NAME_EMPTY);
        }
        if (StrUtil.isBlank(createReqVO.getUniversityName())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.UNIVERSITY_NAME_EMPTY);
        }
        if (StrUtil.isBlank(createReqVO.getEnterpriseName())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ENTERPRISE_NAME_EMPTY);
        }
        if (createReqVO.getStartDate() == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.START_DATE_EMPTY);
        }
        if (createReqVO.getEndDate() == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.END_DATE_EMPTY);
        }
        if (createReqVO.getStartDate().isAfter(createReqVO.getEndDate())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.DATE_RANGE_INVALID);
        }
        if (createReqVO.getTotalAmount() == null || createReqVO.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.TOTAL_AMOUNT_INVALID);
        }
        if (createReqVO.getManagerId() == null || createReqVO.getManagerId() <= 0) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MANAGER_ID_INVALID);
        }
        if (StrUtil.isBlank(createReqVO.getManagerName())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.MANAGER_NAME_EMPTY);
        }

        // 2. 收款信息验证
        if ("paid".equals(createReqVO.getPaymentStatus())) {
            if (createReqVO.getCollectionAmount() == null || createReqVO.getCollectionAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.COLLECTION_AMOUNT_INVALID);
            }
            if (createReqVO.getCollectionAmount().compareTo(createReqVO.getTotalAmount()) > 0) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.COLLECTION_AMOUNT_EXCEEDS_TOTAL);
            }
            if (StrUtil.isBlank(createReqVO.getCollectionMethod())) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.COLLECTION_METHOD_EMPTY);
            }
            if (createReqVO.getCollectionDate() == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.COLLECTION_DATE_EMPTY);
            }
            if (StrUtil.isBlank(createReqVO.getOperatorName())) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.OPERATOR_NAME_EMPTY);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.UNIVERSITY_PRACTICE_ORDER_TYPE, 
                subType = LogRecordConstants.UNIVERSITY_PRACTICE_ORDER_UPDATE_SUB_TYPE, 
                bizNo = "{{#updateReqVO.id}}", 
                success = LogRecordConstants.UNIVERSITY_PRACTICE_ORDER_UPDATE_SUCCESS)
    public void updateOrder(UniversityPracticeOrderSaveReqVO updateReqVO) {
        log.info("开始更新高校实践订单，ID：{}，订单号：{}", updateReqVO.getId(), updateReqVO.getOrderNo());
        
        // 校验存在
        validateOrderExists(updateReqVO.getId(), updateReqVO.getOrderNo());

        // 获取更新前的订单信息 - 用于操作日志的字段对比
        PublicbizOrderDO oldOrder = orderMapper.selectById(updateReqVO.getId());
        log.info("获取到原订单信息，订单ID：{}，订单号：{}", oldOrder.getId(), oldOrder.getOrderNo());

        // 更新订单主表
        PublicbizOrderDO updateObj = UniversityPracticeOrderConvert.INSTANCE.convert(updateReqVO);
        updateObj.setId(updateReqVO.getId()); // 确保ID字段正确设置
        
        // 手动设置contractFileUrl字段，确保不被覆盖
        if (StrUtil.isNotBlank(updateReqVO.getContractFileUrl())) {
            updateObj.setContractFileUrl(updateReqVO.getContractFileUrl());
            log.info("手动设置合同文件URL：{}", updateReqVO.getContractFileUrl());
        }
        
        log.info("准备更新订单主表，订单ID：{}，订单号：{}", updateObj.getId(), updateObj.getOrderNo());
        log.info("合同文件URL字段值：{}", updateObj.getContractFileUrl());
        orderMapper.updateById(updateObj);
        log.info("订单主表更新完成");

        // 更新高校实践订单详情表
        PublicbizPracticeOrderDO existingPracticeOrder = practiceOrderMapper.selectByOrderId(updateReqVO.getId());
        log.info("查询到的高校实践订单详情，订单ID：{}", existingPracticeOrder != null ? existingPracticeOrder.getId() : "null");
        if (existingPracticeOrder != null) {
            PublicbizPracticeOrderDO practiceOrder = UniversityPracticeOrderConvert.INSTANCE.convertDetail(updateReqVO, updateReqVO.getId());
            practiceOrder.setId(existingPracticeOrder.getId()); // 设置ID字段用于更新
            log.info("准备更新高校实践订单详情，订单ID：{}", practiceOrder.getId());
            practiceOrderMapper.updateById(practiceOrder);
            log.info("高校实践订单详情更新完成");
        } else {
            log.warn("未找到对应的高校实践订单详情，订单ID：{}", updateReqVO.getId());
        }

        // 处理支付记录
        handlePaymentRecordUpdate(updateReqVO, oldOrder);

        // 记录操作日志上下文 - 用于日志模板中的变量替换和字段对比
        LogRecordContext.putVariable("updateReqVO", updateReqVO);
        LogRecordContext.putVariable("oldOrder", oldOrder);
        log.info("操作日志上下文设置完成");

        // 构建字段变更列表
        List<OrderLogService.FieldChange> changes = new ArrayList<>();
        changes.add(new OrderLogService.FieldChange("项目名称", 
                oldOrder.getProjectName(), updateReqVO.getProjectName()));
        changes.add(new OrderLogService.FieldChange("项目描述", 
                oldOrder.getProjectDescription(), updateReqVO.getProjectDescription()));
        changes.add(new OrderLogService.FieldChange("开始日期", 
                oldOrder.getStartDate() != null ? oldOrder.getStartDate().toString() : null, 
                updateReqVO.getStartDate() != null ? updateReqVO.getStartDate().toString() : null));
        changes.add(new OrderLogService.FieldChange("结束日期", 
                oldOrder.getEndDate() != null ? oldOrder.getEndDate().toString() : null, 
                updateReqVO.getEndDate() != null ? updateReqVO.getEndDate().toString() : null));
        changes.add(new OrderLogService.FieldChange("订单金额", 
                oldOrder.getTotalAmount() != null ? oldOrder.getTotalAmount().toString() : null, 
                updateReqVO.getTotalAmount() != null ? updateReqVO.getTotalAmount().toString() : null));
        // 注意：UniversityPracticeOrderSaveReqVO 没有 orderStatus 字段，只有 paymentStatus
        // 如果需要记录订单状态变更，需要从其他地方获取或添加该字段
        changes.add(new OrderLogService.FieldChange("支付状态", 
                oldOrder.getPaymentStatus(), updateReqVO.getPaymentStatus()));
        changes.add(new OrderLogService.FieldChange("合同类型", 
                oldOrder.getContractType(), updateReqVO.getContractType()));
        changes.add(new OrderLogService.FieldChange("合同文件URL", 
                oldOrder.getContractFileUrl(), updateReqVO.getContractFileUrl()));
        
        // 记录编辑日志
        orderLogService.recordOrderEdit(updateReqVO.getOrderNo(), "practice", changes,
                oldOrder.getOrderStatus() != null ? oldOrder.getOrderStatus() : "未知",
                oldOrder.getOrderStatus(), // 使用原状态作为新状态，因为VO中没有状态字段
                null, null, null);
        log.info("订单操作日志插入完成");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.UNIVERSITY_PRACTICE_ORDER_TYPE, 
                subType = LogRecordConstants.UNIVERSITY_PRACTICE_ORDER_DELETE_SUB_TYPE, 
                bizNo = "{{#order.id}}", 
                success = LogRecordConstants.UNIVERSITY_PRACTICE_ORDER_DELETE_SUCCESS)
    public void deleteOrder(Long id, String orderNo) {
        // 校验存在
        validateOrderExists(id, orderNo);

        // 获取要删除的订单信息 - 用于操作日志记录
        PublicbizOrderDO order = orderMapper.selectById(id);

        // 删除订单主表
        orderMapper.deleteById(id);

        // 删除高校实践订单详情表
        practiceOrderMapper.deleteById(id);

        // 删除支付记录
        deletePaymentRecord(id);

        // 记录操作日志上下文 - 用于日志模板中的变量替换
        //LogRecordContext.putVariable("order", order);

        // 记录删除日志
        orderLogService.recordOrderDelete(order.getOrderNo(), "practice",
                order.getProjectName() != null ? order.getProjectName() : "未知项目",
                null, null, null);
    }

    private void validateOrderExists(Long id, String orderNo) {
        PublicbizOrderDO order = orderMapper.selectById(id);
        if (order == null) {
            throw ServiceExceptionUtil.exception(new cn.bztmaster.cnt.framework.common.exception.ErrorCode(1_001_001_000, "高校实践订单不存在"));
        }
        if (!order.getOrderNo().equals(orderNo)) {
            throw ServiceExceptionUtil.exception(new cn.bztmaster.cnt.framework.common.exception.ErrorCode(1_001_001_001, "订单号不匹配"));
        }
    }

    @Override
    public UniversityPracticeOrderDetailRespVO getOrder(Long id) {
        PublicbizOrderDO order = orderMapper.selectById(id);
        if (order == null) {
            return null;
        }
        PublicbizPracticeOrderDO practiceOrder = practiceOrderMapper.selectByOrderId(id);
        return UniversityPracticeOrderConvert.INSTANCE.convertDetail(order, practiceOrder);
    }

    @Override
    public UniversityPracticeOrderDetailRespVO getOrderByOrderNo(String orderNo) {
        PublicbizOrderDO order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            return null;
        }
        PublicbizPracticeOrderDO practiceOrder = practiceOrderMapper.selectByOrderNo(orderNo);
        return UniversityPracticeOrderConvert.INSTANCE.convertDetail(order, practiceOrder);
    }

    @Override
    public PageResult<UniversityPracticeOrderPageRespVO> getOrderPage(UniversityPracticeOrderPageReqVO pageReqVO) {
        // 分页查询订单主表
        PageResult<PublicbizOrderDO> pageResult = orderMapper.selectPracticeOrderPage(pageReqVO);
        if (pageResult.getList().isEmpty()) {
            return new PageResult<>(pageResult.getTotal());
        }

        // 查询高校实践订单详情
        List<Long> orderIds = CollectionUtils.convertList(pageResult.getList(), PublicbizOrderDO::getId);
        List<PublicbizPracticeOrderDO> practiceOrders = practiceOrderMapper.selectListByOrderIds(orderIds);
        Map<Long, PublicbizPracticeOrderDO> practiceOrderMap = CollectionUtils.convertMap(practiceOrders, PublicbizPracticeOrderDO::getOrderId);

        // 转换结果
        List<UniversityPracticeOrderPageRespVO> list = UniversityPracticeOrderConvert.INSTANCE.convertList(pageResult.getList(), practiceOrderMap);
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public List<UniversityPracticeOrderRespDTO> getOrderList(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }

        // 查询订单主表
        List<PublicbizOrderDO> orders = orderMapper.selectListByIds(ids);
        if (orders.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询高校实践订单详情
        List<PublicbizPracticeOrderDO> practiceOrders = practiceOrderMapper.selectListByOrderIds(ids);
        Map<Long, PublicbizPracticeOrderDO> practiceOrderMap = CollectionUtils.convertMap(practiceOrders, PublicbizPracticeOrderDO::getOrderId);

        // 查询合同信息
        List<Long> orderIds = orders.stream().map(PublicbizOrderDO::getId).collect(Collectors.toList());
        List<PublicbizPartnerContractDO> contracts = partnerContractMapper.selectListByOrderIds(orderIds);
        Map<Long, PublicbizPartnerContractDO> contractMap = CollectionUtils.convertMap(contracts, PublicbizPartnerContractDO::getPartnerId);

        // 转换结果
        return CollectionUtils.convertList(orders, order -> 
            UniversityPracticeOrderConvert.INSTANCE.convertApi(order, practiceOrderMap.get(order.getId()), contractMap.get(order.getId())));
    }

    @Override
    public UniversityPracticeOrderRespDTO getOrderByOrderNoForApi(String orderNo) {
        PublicbizOrderDO order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            return null;
        }
        
        // 获取高校实践订单详情
        PublicbizPracticeOrderDO practiceOrder = practiceOrderMapper.selectByOrderNo(orderNo);
        
        // 获取合同信息
        PublicbizPartnerContractDO contract = null;
        if (order.getId() != null) {
            LambdaQueryWrapper<PublicbizPartnerContractDO> contractWrapper = new LambdaQueryWrapper<>();
            contractWrapper.eq(PublicbizPartnerContractDO::getPartnerId, order.getId());
            contract = partnerContractMapper.selectOne(contractWrapper);
        }
        
        return UniversityPracticeOrderConvert.INSTANCE.convertApi(order, practiceOrder, contract);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOrderForApi(UniversityPracticeOrderSaveReqDTO createReqDTO) {
        // 转换为VO
        UniversityPracticeOrderSaveReqVO createReqVO = UniversityPracticeOrderConvert.INSTANCE.convert(createReqDTO);
        return createOrder(createReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderForApi(UniversityPracticeOrderSaveReqDTO updateReqDTO) {
        // 转换为VO
        UniversityPracticeOrderSaveReqVO updateReqVO = UniversityPracticeOrderConvert.INSTANCE.convert(updateReqDTO);
        updateOrder(updateReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderForApi(Long id, String orderNo) {
        deleteOrder(id, orderNo);
    }

    @Override
    public String generateOrderNo() {
        return generateOrderNoWithRetry();
    }

    /**
     * 生成基础订单号
     *
     * @return 基础订单号
     */
    private String generateBaseOrderNo() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 使用雪花算法生成唯一ID
        long snowflakeId = IdUtil.getSnowflakeNextId();
        String snowflakeSuffix = String.format("%012d", snowflakeId % 1000000000000L);
        
        String orderNo = "HP" + dateStr + snowflakeSuffix;
        return orderNo;
    }

    @Override
    public PageResult<UniversityPracticeOrderLogPageRespVO> getOrderOperationLogs(UniversityPracticeOrderLogPageReqVO pageReqVO) {
        log.info("开始查询高校实践订单操作日志，订单号：{}", pageReqVO.getOrderNo());
        
        // 构建查询条件
        LambdaQueryWrapper<PublicbizOrderLogDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PublicbizOrderLogDO::getOrderNo, pageReqVO.getOrderNo())
                   .eq(pageReqVO.getLogType() != null, PublicbizOrderLogDO::getLogType, pageReqVO.getLogType())
                   .ge(pageReqVO.getStartDate() != null, PublicbizOrderLogDO::getCreateTime, pageReqVO.getStartDate())
                   .le(pageReqVO.getEndDate() != null, PublicbizOrderLogDO::getCreateTime, pageReqVO.getEndDate())
                   .orderByDesc(PublicbizOrderLogDO::getCreateTime);
        
        // 分页查询
        Page<PublicbizOrderLogDO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<PublicbizOrderLogDO> result = orderLogMapper.selectPage(page, queryWrapper);
        
        // 转换为VO
        List<UniversityPracticeOrderLogPageRespVO> voList = result.getRecords().stream()
                .map(this::convertToLogRespVO)
                .collect(Collectors.toList());
        
        log.info("查询高校实践订单操作日志完成，共查询到{}条记录", result.getTotal());
        return new PageResult<>(voList, result.getTotal());
    }



    @Override
    public UniversityPracticeOrderContractUploadRespVO uploadContract(UniversityPracticeOrderContractUploadReqVO uploadReqVO, MultipartFile file) {
        log.info("开始上传高校实践订单合同，订单号：{}，文件名：{}", uploadReqVO.getOrderNo(), file.getOriginalFilename());
        
        // 校验订单是否存在
        PublicbizOrderDO order = orderMapper.selectById(uploadReqVO.getOrderId());
        if (order == null) {
            throw new RuntimeException("订单不存在，订单ID：" + uploadReqVO.getOrderId());
        }
        
        // 校验订单号是否匹配
        if (!order.getOrderNo().equals(uploadReqVO.getOrderNo())) {
            throw new RuntimeException("订单号不匹配");
        }
        
        // 校验文件
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("合同文件不能为空");
        }
        
        // 校验文件类型
        String fileName = file.getOriginalFilename();
        String fileExtension = StrUtil.subAfter(fileName, ".", true).toLowerCase();
        if (!"pdf".equals(fileExtension) && !"doc".equals(fileExtension) && !"docx".equals(fileExtension)) {
            throw new RuntimeException("不支持的文件格式，仅支持PDF、DOC、DOCX格式");
        }
        
        // 校验文件大小（10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            throw new RuntimeException("文件大小不能超过10MB");
        }
        
        try {
            // 生成文件URL（这里简化处理，实际应该上传到文件服务器）
            String fileUrl = "https://example.com/contracts/" + order.getOrderNo() + "." + fileExtension;
            
            // 更新订单的合同信息
            order.setContractType(uploadReqVO.getContractType());
            order.setContractFileUrl(fileUrl);
            order.setContractStatus("unsigned");
            orderMapper.updateById(order);
            
            // 记录操作日志
            orderLogService.recordCustomLog(order.getOrderNo(), "practice", "合同上传", "上传高校实践订单合同", 
                    String.format("上传了【%s】类型合同文件：%s", uploadReqVO.getOrderNo(), fileName),
                    order.getContractStatus(), "unsigned", null, null, null);
            
            // 构建响应结果
            UniversityPracticeOrderContractUploadRespVO respVO = new UniversityPracticeOrderContractUploadRespVO();
            respVO.setSuccess(true);
            respVO.setFileUrl(fileUrl);
            respVO.setFileName(fileName);
            respVO.setFileSize(file.getSize());
            respVO.setContractStatus("unsigned");
            respVO.setUploadTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            log.info("高校实践订单合同上传成功，文件URL：{}", fileUrl);
            return respVO;
            
        } catch (Exception e) {
            log.error("上传高校实践订单合同失败，订单ID：{}，错误：{}", uploadReqVO.getOrderId(), e.getMessage(), e);
            throw new RuntimeException("上传合同失败：" + e.getMessage());
        }
    }

    @Override
    public UniversityPracticeOrderPaperContractUploadRespVO uploadPaperContract(UniversityPracticeOrderPaperContractUploadReqVO uploadReqVO) {
        log.info("开始上传高校实践订单纸质合同，订单号：{}，合同编号：{}", uploadReqVO.getOrderNo(), uploadReqVO.getContractNumber());
        
        try {
            // 首先尝试通过订单ID查找订单
            PublicbizOrderDO order = orderMapper.selectById(uploadReqVO.getOrderId());
            
            // 如果通过ID没找到，尝试通过订单号查找
            if (order == null) {
                log.info("通过订单ID {} 未找到订单，尝试通过订单号 {} 查找", uploadReqVO.getOrderId(), uploadReqVO.getOrderNo());
                LambdaQueryWrapper<PublicbizOrderDO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(PublicbizOrderDO::getOrderNo, uploadReqVO.getOrderNo());
                order = orderMapper.selectOne(wrapper);
            }
            
            // 如果仍然没找到，检查是否是企业培训订单
            if (order == null) {
                log.info("在publicbiz_order表中未找到订单，检查是否为企业培训订单");
                // 这里可以添加企业培训订单的处理逻辑
                // 或者抛出更明确的错误信息
                throw new RuntimeException("订单不存在，订单号：" + uploadReqVO.getOrderNo() + "，订单ID：" + uploadReqVO.getOrderId() + 
                    "。请确认订单类型和订单号是否正确。");
            }
            
            // 校验订单号是否匹配
            if (!order.getOrderNo().equals(uploadReqVO.getOrderNo())) {
                throw new RuntimeException("订单号不匹配");
            }
            
            // 校验文件URL是否有效
            if (StrUtil.isBlank(uploadReqVO.getFileUrl())) {
                throw new RuntimeException("文件URL不能为空");
            }
            
            // 从文件URL中提取文件名和大小信息
            String fileName = StrUtil.subAfter(uploadReqVO.getFileUrl(), "/", true);
            if (StrUtil.isBlank(fileName)) {
                fileName = "contract_" + uploadReqVO.getOrderNo() + ".pdf";
            }
            
            // 模拟文件大小（实际应该从文件服务器获取）
            Long fileSize = 1024000L; // 1MB
            
            // 创建或更新合同信息记录
            PublicbizPartnerContractDO contractInfo = PublicbizPartnerContractDO.builder()
                    .partnerId(uploadReqVO.getOrderId()) // 使用订单ID作为合作伙伴ID
                    .contractName(uploadReqVO.getContractName())
                    .contractNumber(uploadReqVO.getContractNumber())
                    .startDate(uploadReqVO.getSignDate()) // 使用签署日期作为开始日期
                    .endDate(uploadReqVO.getSignDate().plusYears(1)) // 设置结束日期为签署日期后一年
                    .amount(uploadReqVO.getContractAmount())
                    .status("有效") // 设置合同状态为有效
                    .attachmentPath(uploadReqVO.getFileUrl()) // 使用附件路径字段存储文件URL
                    .signer("系统用户") // 设置签约人
                    .build();
            
            // 检查是否已存在合同记录
            LambdaQueryWrapper<PublicbizPartnerContractDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PublicbizPartnerContractDO::getPartnerId, uploadReqVO.getOrderId());
            PublicbizPartnerContractDO existingContract = partnerContractMapper.selectOne(wrapper);
            
            if (existingContract != null) {
                // 更新现有记录
                contractInfo.setContractId(existingContract.getContractId());
                partnerContractMapper.updateById(contractInfo);
            } else {
                // 创建新记录
                partnerContractMapper.insert(contractInfo);
            }
            
            // 更新订单的合同信息
            order.setContractType("paper");
            order.setContractFileUrl(uploadReqVO.getFileUrl());
            order.setContractStatus("unsigned");
            orderMapper.updateById(order);
            
            // 记录操作日志
            orderLogService.recordCustomLog(order.getOrderNo(), "practice", "纸质合同上传", "上传高校实践订单纸质合同", 
                    String.format("上传了纸质合同：%s，合同编号：%s，签署日期：%s", 
                            uploadReqVO.getContractName(), uploadReqVO.getContractNumber(), uploadReqVO.getSignDate()),
                    order.getContractStatus(), "unsigned", null, null, null);
            
            // 构建响应结果
            UniversityPracticeOrderPaperContractUploadRespVO respVO = new UniversityPracticeOrderPaperContractUploadRespVO();
            respVO.setOrderId(uploadReqVO.getOrderId());
            respVO.setOrderNo(uploadReqVO.getOrderNo());
            respVO.setSuccess(true);
            respVO.setFileUrl(uploadReqVO.getFileUrl());
            respVO.setFileName(fileName);
            respVO.setFileSize(fileSize);
            respVO.setContractNumber(uploadReqVO.getContractNumber());
            respVO.setContractName(uploadReqVO.getContractName());
            respVO.setSignDate(uploadReqVO.getSignDate());
            respVO.setContractAmount(uploadReqVO.getContractAmount());
            
            log.info("高校实践订单纸质合同上传成功，合同编号：{}，文件URL：{}", uploadReqVO.getContractNumber(), uploadReqVO.getFileUrl());
            return respVO;
            
        } catch (Exception e) {
            log.error("上传高校实践订单纸质合同失败，订单ID：{}，错误：{}", uploadReqVO.getOrderId(), e.getMessage(), e);
            throw new RuntimeException("上传纸质合同失败：" + e.getMessage());
        }
    }

    @Override
    public UniversityPracticeOrderStatisticsRespVO getOrderStatisticsOverview() {
        log.info("开始获取高校实践订单统计概览");
        
        try {
            UniversityPracticeOrderStatisticsRespVO statistics = new UniversityPracticeOrderStatisticsRespVO();
            
            // 查询总订单数
            LambdaQueryWrapper<PublicbizOrderDO> totalWrapper = new LambdaQueryWrapper<>();
            totalWrapper.eq(PublicbizOrderDO::getOrderType, "practice");
            Long totalOrders = orderMapper.selectCount(totalWrapper);
            statistics.setTotalOrders(totalOrders);
            
            // 查询本月新增订单数
            LocalDate startOfMonth = LocalDate.now().withDayOfMonth(1);
            LocalDate endOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth());
            LambdaQueryWrapper<PublicbizOrderDO> monthlyWrapper = new LambdaQueryWrapper<>();
            monthlyWrapper.eq(PublicbizOrderDO::getOrderType, "practice")
                         .ge(PublicbizOrderDO::getCreateTime, startOfMonth)
                         .le(PublicbizOrderDO::getCreateTime, endOfMonth);
            Long monthlyNewOrders = orderMapper.selectCount(monthlyWrapper);
            statistics.setMonthlyNewOrders(monthlyNewOrders);
            
            // 查询总订单金额
            BigDecimal totalAmount = orderMapper.selectTotalAmountByOrderType("practice");
            statistics.setTotalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO);
            
            // 查询本月新增订单金额
            BigDecimal monthlyNewAmount = orderMapper.selectMonthlyAmountByOrderType("practice", startOfMonth, endOfMonth);
            statistics.setMonthlyNewAmount(monthlyNewAmount != null ? monthlyNewAmount : BigDecimal.ZERO);
            
            // 查询各状态订单数量
            LambdaQueryWrapper<PublicbizOrderDO> completedWrapper = new LambdaQueryWrapper<>();
            completedWrapper.eq(PublicbizOrderDO::getOrderType, "practice")
                           .eq(PublicbizOrderDO::getOrderStatus, "completed");
            Long completedOrders = orderMapper.selectCount(completedWrapper);
            statistics.setCompletedOrders(completedOrders);
            
            LambdaQueryWrapper<PublicbizOrderDO> inProgressWrapper = new LambdaQueryWrapper<>();
            inProgressWrapper.eq(PublicbizOrderDO::getOrderType, "practice")
                            .in(PublicbizOrderDO::getOrderStatus, Arrays.asList("executing", "pending_payment"));
            Long inProgressOrders = orderMapper.selectCount(inProgressWrapper);
            statistics.setInProgressOrders(inProgressOrders);
            
            LambdaQueryWrapper<PublicbizOrderDO> pendingApprovalWrapper = new LambdaQueryWrapper<>();
            pendingApprovalWrapper.eq(PublicbizOrderDO::getOrderType, "practice")
                                 .eq(PublicbizOrderDO::getOrderStatus, "pending_approval");
            Long pendingApprovalOrders = orderMapper.selectCount(pendingApprovalWrapper);
            statistics.setPendingApprovalOrders(pendingApprovalOrders);
            
            // 构建状态统计
            List<UniversityPracticeOrderStatisticsRespVO.OrderStatusCount> statusCounts = buildStatusCounts();
            statistics.setStatusCounts(statusCounts);
            
            // 构建月度趋势
            List<UniversityPracticeOrderStatisticsRespVO.MonthlyOrderTrend> monthlyTrends = buildMonthlyTrends();
            statistics.setMonthlyTrends(monthlyTrends);
            
            statistics.setStatisticsTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            log.info("获取高校实践订单统计概览完成，总订单数：{}", totalOrders);
            return statistics;
            
        } catch (Exception e) {
            log.error("获取高校实践订单统计概览失败，错误：{}", e.getMessage(), e);
            throw new RuntimeException("获取统计概览失败：" + e.getMessage());
        }
    }

    /**
     * 构建状态统计
     */
    private List<UniversityPracticeOrderStatisticsRespVO.OrderStatusCount> buildStatusCounts() {
        List<UniversityPracticeOrderStatisticsRespVO.OrderStatusCount> statusCounts = new ArrayList<>();
        
        // 这里简化处理，实际应该从数据库查询各状态的数量
        String[] statuses = {"draft", "pending_approval", "approving", "approved", "rejected", "pending_payment", "executing", "completed", "cancelled"};
        String[] statusNames = {"草稿", "待审批", "审批中", "已批准", "已拒绝", "待支付", "执行中", "已完成", "已取消"};
        
        for (int i = 0; i < statuses.length; i++) {
            LambdaQueryWrapper<PublicbizOrderDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PublicbizOrderDO::getOrderType, "practice")
                   .eq(PublicbizOrderDO::getOrderStatus, statuses[i]);
            Long count = orderMapper.selectCount(wrapper);
            
            UniversityPracticeOrderStatisticsRespVO.OrderStatusCount statusCount = new UniversityPracticeOrderStatisticsRespVO.OrderStatusCount();
            statusCount.setStatus(statuses[i]);
            statusCount.setStatusName(statusNames[i]);
            statusCount.setCount(count);
            statusCounts.add(statusCount);
        }
        
        // 计算占比
        Long total = statusCounts.stream().mapToLong(UniversityPracticeOrderStatisticsRespVO.OrderStatusCount::getCount).sum();
        if (total > 0) {
            for (UniversityPracticeOrderStatisticsRespVO.OrderStatusCount statusCount : statusCounts) {
                double percentage = (double) statusCount.getCount() / total * 100;
                statusCount.setPercentage(Math.round(percentage * 10.0) / 10.0);
            }
        }
        
        return statusCounts;
    }

    /**
     * 构建月度趋势
     */
    private List<UniversityPracticeOrderStatisticsRespVO.MonthlyOrderTrend> buildMonthlyTrends() {
        List<UniversityPracticeOrderStatisticsRespVO.MonthlyOrderTrend> monthlyTrends = new ArrayList<>();
        
        // 查询最近6个月的数据
        for (int i = 5; i >= 0; i--) {
            LocalDate month = LocalDate.now().minusMonths(i);
            LocalDate startOfMonth = month.withDayOfMonth(1);
            LocalDate endOfMonth = month.withDayOfMonth(month.lengthOfMonth());
            
            LambdaQueryWrapper<PublicbizOrderDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PublicbizOrderDO::getOrderType, "practice")
                   .ge(PublicbizOrderDO::getCreateTime, startOfMonth)
                   .le(PublicbizOrderDO::getCreateTime, endOfMonth);
            Long orderCount = orderMapper.selectCount(wrapper);
            
            BigDecimal orderAmount = orderMapper.selectMonthlyAmountByOrderType("practice", startOfMonth, endOfMonth);
            
            UniversityPracticeOrderStatisticsRespVO.MonthlyOrderTrend trend = new UniversityPracticeOrderStatisticsRespVO.MonthlyOrderTrend();
            trend.setMonth(month.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            trend.setOrderCount(orderCount);
            trend.setOrderAmount(orderAmount != null ? orderAmount : BigDecimal.ZERO);
            monthlyTrends.add(trend);
        }
        
        return monthlyTrends;
    }

    /**
     * 生成订单号并处理可能的冲突（带重试机制）
     *
     * @return 唯一的订单号
     */
    private String generateOrderNoWithRetry() {
        int maxRetries = 5;
        int retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                String orderNo = generateBaseOrderNo();
                
                // 检查订单号是否已存在
                if (orderMapper.selectByOrderNo(orderNo) == null) {
                    log.info("成功生成唯一订单号：{}，重试次数：{}", orderNo, retryCount);
                    return orderNo;
                }
                
                log.warn("订单号 {} 已存在，重试生成，重试次数：{}", orderNo, retryCount + 1);
                retryCount++;
                
                // 短暂延迟，避免过快重试
                if (retryCount < maxRetries) {
                    try {
                        Thread.sleep(10); // 10毫秒延迟
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
                
            } catch (Exception e) {
                log.error("生成订单号时发生异常，重试次数：{}，错误：{}", retryCount, e.getMessage());
                retryCount++;
            }
        }
        
        // 如果重试次数用完，使用时间戳+随机数的方式
        String fallbackOrderNo = generateFallbackOrderNo();
        log.warn("重试次数用完，使用备用订单号生成方式：{}", fallbackOrderNo);
        return fallbackOrderNo;
    }

    /**
     * 备用订单号生成方式（基于纳秒级时间戳）
     *
     * @return 备用订单号
     */
    private String generateFallbackOrderNo() {
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 使用纳秒级时间戳
        long nanoTime = System.nanoTime();
        String nanoSuffix = String.format("%09d", nanoTime % 1000000000);
        
        // 添加3位随机数
        int randomNum = (int) (Math.random() * 1000);
        String randomSuffix = String.format("%03d", randomNum);
        
        String orderNo = "HP" + dateStr + nanoSuffix + randomSuffix;
        
        log.info("生成备用订单号：{}，日期：{}，纳秒后缀：{}，随机数：{}", orderNo, dateStr, nanoSuffix, randomSuffix);
        return orderNo;
    }

    /**
     * 转换为日志响应VO
     */
    private UniversityPracticeOrderLogPageRespVO convertToLogRespVO(PublicbizOrderLogDO logDO) {
        UniversityPracticeOrderLogPageRespVO respVO = new UniversityPracticeOrderLogPageRespVO();
        respVO.setId(logDO.getId());
        respVO.setLogType(logDO.getLogType());
        respVO.setLogTitle(logDO.getLogTitle());
        respVO.setLogContent(logDO.getLogContent());
        respVO.setOldStatus(logDO.getOldStatus());
        respVO.setNewStatus(logDO.getNewStatus());
        respVO.setOperatorId(logDO.getOperatorId());
        respVO.setOperatorName(logDO.getOperatorName());
        respVO.setOperatorRole(logDO.getRelatedPartyType());
        respVO.setRelatedPartyType(logDO.getRelatedPartyType());
        respVO.setRelatedPartyName(logDO.getRelatedPartyName());
        respVO.setCreateTime(logDO.getCreateTime());
        return respVO;
    }

    @Override
    public List<UniversityPracticeOrderExcelVO> getOrderExcelList(UniversityPracticeOrderExportReqVO exportReqVO) {
        try {
            log.info("开始获取高校实践订单Excel导出列表，项目名称：{}", exportReqVO.getProjectName());
            
            // 构建查询条件
            LambdaQueryWrapper<PublicbizOrderDO> queryWrapper = new LambdaQueryWrapper<>();
            
            // 订单号筛选
            if (StrUtil.isNotBlank(exportReqVO.getOrderNo())) {
                queryWrapper.like(PublicbizOrderDO::getOrderNo, exportReqVO.getOrderNo());
            }
            
            // 项目名称筛选
            if (StrUtil.isNotBlank(exportReqVO.getProjectName())) {
                queryWrapper.like(PublicbizOrderDO::getProjectName, exportReqVO.getProjectName());
            }
            
            // 合作高校筛选（在主订单表中无法直接筛选，需要在转换时处理）
            // if (StrUtil.isNotBlank(exportReqVO.getUniversityName())) {
            //     queryWrapper.like(UniversityPracticeOrderDO::getUniversityName, exportReqVO.getUniversityName());
            // }
            
            // 合作企业筛选（在主订单表中无法直接筛选，需要在转换时处理）
            // if (StrUtil.isNotBlank(exportReqVO.getEnterpriseName())) {
            //     queryWrapper.like(UniversityPracticeOrderDO::getEnterpriseName, exportReqVO.getEnterpriseName());
            // }
            
            // 项目负责人筛选
            if (StrUtil.isNotBlank(exportReqVO.getProjectManager())) {
                queryWrapper.like(PublicbizOrderDO::getManagerName, exportReqVO.getProjectManager());
            }
            
            // 订单状态筛选
            if (StrUtil.isNotBlank(exportReqVO.getOrderStatus())) {
                queryWrapper.eq(PublicbizOrderDO::getOrderStatus, exportReqVO.getOrderStatus());
            }
            
            // 支付状态筛选
            if (StrUtil.isNotBlank(exportReqVO.getPaymentStatus())) {
                queryWrapper.eq(PublicbizOrderDO::getPaymentStatus, exportReqVO.getPaymentStatus());
            }
            
            // 审批状态筛选（在主订单表中无法直接筛选，需要在转换时处理）
            // if (StrUtil.isNotBlank(exportReqVO.getApprovalStatus())) {
            //     queryWrapper.eq(UniversityPracticeOrderDO::getApprovalStatus, exportReqVO.getApprovalStatus());
            // }
            
            // 合同状态筛选
            if (StrUtil.isNotBlank(exportReqVO.getContractStatus())) {
                queryWrapper.eq(PublicbizOrderDO::getContractStatus, exportReqVO.getContractStatus());
            }
            
            // 日期范围筛选
            if (exportReqVO.getStartDate() != null) {
                queryWrapper.ge(PublicbizOrderDO::getStartDate, exportReqVO.getStartDate());
            }
            if (exportReqVO.getEndDate() != null) {
                queryWrapper.ge(PublicbizOrderDO::getEndDate, exportReqVO.getEndDate());
            }
            
            // 关键词搜索（只搜索主订单表中的字段）
            if (StrUtil.isNotBlank(exportReqVO.getKeyword())) {
                queryWrapper.and(wrapper -> wrapper
                        .like(PublicbizOrderDO::getProjectName, exportReqVO.getKeyword())
                        .or()
                        .like(PublicbizOrderDO::getManagerName, exportReqVO.getKeyword())
                );
            }
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc(PublicbizOrderDO::getCreateTime);
            
            // 查询数据
            List<PublicbizOrderDO> orderList = orderMapper.selectList(queryWrapper);
            
            // 转换为Excel VO
            List<UniversityPracticeOrderExcelVO> excelList = new ArrayList<>();
            for (PublicbizOrderDO order : orderList) {
                // 查询详情信息
                PublicbizPracticeOrderDO practiceOrder = practiceOrderMapper.selectOne(
                    new LambdaQueryWrapper<PublicbizPracticeOrderDO>()
                        .eq(PublicbizPracticeOrderDO::getOrderId, order.getId())
                );
                
                if (practiceOrder != null) {
                    // 应用高校名称和企业名称筛选
                    if (StrUtil.isNotBlank(exportReqVO.getUniversityName()) && 
                        !practiceOrder.getUniversityName().contains(exportReqVO.getUniversityName())) {
                        continue;
                    }
                    if (StrUtil.isNotBlank(exportReqVO.getEnterpriseName()) && 
                        !practiceOrder.getEnterpriseName().contains(exportReqVO.getEnterpriseName())) {
                        continue;
                    }
                    if (StrUtil.isNotBlank(exportReqVO.getApprovalStatus())) {
                        // 审批状态从订单状态中提取
                        String approvalStatus = extractApprovalStatus(order.getOrderStatus());
                        if (!exportReqVO.getApprovalStatus().equals(approvalStatus)) {
                            continue;
                        }
                    }
                    
                    excelList.add(convertToExcelVO(order, practiceOrder));
                }
            }
            
            log.info("高校实践订单Excel导出列表获取成功，共{}条记录", excelList.size());
            return excelList;
            
        } catch (Exception e) {
            log.error("获取高校实践订单Excel导出列表失败", e);
            throw new RuntimeException("获取Excel导出列表失败", e);
        }
    }

    /**
     * 转换为Excel VO
     */
    private UniversityPracticeOrderExcelVO convertToExcelVO(PublicbizOrderDO order, PublicbizPracticeOrderDO practiceOrder) {
        return UniversityPracticeOrderExcelVO.builder()
                .orderNo(order.getOrderNo())
                .leadId(order.getLeadId())
                .opportunityId(order.getOpportunityId())
                .projectName(order.getProjectName())
                .universityName(practiceOrder != null ? practiceOrder.getUniversityName() : null)
                .enterpriseName(practiceOrder != null ? practiceOrder.getEnterpriseName() : null)
                .projectManager(order.getManagerName())
                .startDate(order.getStartDate())
                .endDate(order.getEndDate())
                .projectDuration(practiceOrder != null ? practiceOrder.getPracticeDuration() : null)
                .studentCount(practiceOrder != null ? practiceOrder.getStudentCount() : null)
                .totalAmount(order.getTotalAmount())
                .orderStatus(getOrderStatusDisplayName(order.getOrderStatus()))
                .paymentStatus(getPaymentStatusDisplayName(order.getPaymentStatus()))
                .approvalStatus(getApprovalStatusDisplayName(order.getOrderStatus()))
                .contractStatus(getContractStatusDisplayName(order.getContractStatus()))
                .createTime(order.getCreateTime())
                .updateTime(order.getUpdateTime())
                .remark(order.getRemark())
                .build();
    }

    /**
     * 获取订单状态显示名称
     */
    private String getOrderStatusDisplayName(String status) {
        if (status == null) return "";
        switch (status) {
            case "draft": return "草稿";
            case "pending_approval": return "待审批";
            case "approved": return "已审批";
            case "in_progress": return "进行中";
            case "completed": return "已完成";
            case "cancelled": return "已取消";
            default: return status;
        }
    }

    /**
     * 获取支付状态显示名称
     */
    private String getPaymentStatusDisplayName(String status) {
        if (status == null) return "";
        switch (status) {
            case "unpaid": return "未支付";
            case "partial_paid": return "部分支付";
            case "paid": return "已支付";
            case "refunded": return "已退款";
            default: return status;
        }
    }

    /**
     * 获取审批状态显示名称
     */
    private String getApprovalStatusDisplayName(String status) {
        if (status == null) return "";
        switch (status) {
            case "pending": return "未审批";
            case "approved": return "已审批";
            case "rejected": return "已拒绝";
            default: return status;
        }
    }

    /**
     * 获取合同状态显示名称
     */
    private String getContractStatusDisplayName(String status) {
        if (status == null) return "";
        switch (status) {
            case "unsigned": return "未签署";
            case "signed": return "已签署";
            case "expired": return "已过期";
            default: return status;
        }
    }

    /**
     * 从订单状态中提取审批状态
     */
    private String extractApprovalStatus(String orderStatus) {
        if (orderStatus == null) return "";
        switch (orderStatus) {
            case "draft": return "pending";
            case "pending_approval": return "pending";
            case "approved": return "approved";
            case "rejected": return "rejected";
            case "in_progress": return "approved";
            case "completed": return "approved";
            case "cancelled": return "rejected";
            default: return orderStatus;
        }
    }

    @Override
    public Map<String, Object> getContractInfo(Long orderId, String orderNo) {
        log.info("开始获取高校实践订单合同信息，订单号：{}", orderNo);
        
        try {
            // 1. 验证订单信息
            PublicbizOrderDO order = orderMapper.selectById(orderId);
            if (order == null || !order.getOrderNo().equals(orderNo)) {
                throw new ServiceException(ErrorCodeConstants.ORDER_NOT_EXISTS);
            }
            
            // 2. 构建合同信息
            Map<String, Object> contractInfo = new HashMap<>();
            contractInfo.put("orderId", orderId);
            contractInfo.put("orderNo", orderNo);
            contractInfo.put("contractFileUrl", order.getContractFileUrl());
            contractInfo.put("contractType", order.getContractType());
            contractInfo.put("contractStatus", order.getContractStatus());
            
            // 3. 如果有合同文件，获取文件信息
            if (StringUtils.hasText(order.getContractFileUrl())) {
                String fileUrl = order.getContractFileUrl();
                contractInfo.put("fileUrl", fileUrl);
                contractInfo.put("fileName", getFileNameFromUrl(fileUrl));
                contractInfo.put("fileSize", 0L); // TODO: 可以通过HTTP HEAD请求获取实际文件大小
                contractInfo.put("isValidUrl", isValidFileUrl(fileUrl));
            }
            
            log.info("获取高校实践订单合同信息成功，订单号：{}", orderNo);
            return contractInfo;
            
        } catch (Exception e) {
            log.error("获取高校实践订单合同信息失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            throw new ServiceException(ErrorCodeConstants.ORDER_CONTRACT_INFO_FAILED);
        }
    }
    
    /**
     * 验证文件URL格式
     */
    private boolean isValidFileUrl(String fileUrl) {
        if (StringUtils.hasText(fileUrl) == false) {
            return false;
        }
        
        // 检查是否为HTTP/HTTPS URL
        if (!fileUrl.startsWith("http://") && !fileUrl.startsWith("https://")) {
            return false;
        }
        
        // 检查是否为七牛云或其他云存储URL
        if (fileUrl.contains("qiniu.bzmaster.cn") || 
            fileUrl.contains("aliyun.com") || 
            fileUrl.contains("tencent.com")) {
            return true;
        }
        
        return true;
    }
    

    
    /**
     * 从URL中提取文件名
     */
    private String getFileNameFromUrl(String fileUrl) {
        try {
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            // 移除查询参数
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }
            // 如果文件名为空，使用默认名称
            if (StringUtils.hasText(fileName) == false) {
                fileName = "contract_" + System.currentTimeMillis() + ".pdf";
            }
            return fileName;
        } catch (Exception e) {
            return "contract_" + System.currentTimeMillis() + ".pdf";
        }
    }

    // ========== 收款相关方法 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCollectionRespDTO confirmCollection(OrderCollectionReqDTO reqDTO) {
        log.info("开始确认收款，订单ID：{}，订单号：{}，收款金额：{}", 
                reqDTO.getOrderId(), reqDTO.getOrderNo(), reqDTO.getCollectionAmount());
        
        try {
            // 1. 验证订单状态
            PublicbizOrderDO order = orderMapper.selectById(reqDTO.getOrderId());
            if (order == null) {
                log.error("确认收款失败：订单不存在，订单ID：{}", reqDTO.getOrderId());
                throw new ServiceException(ErrorCodeConstants.ORDER_NOT_EXISTS);
            }
            
            log.info("订单状态检查：订单ID：{}，订单状态：{}，支付状态：{}", 
                    reqDTO.getOrderId(), order.getOrderStatus(), order.getPaymentStatus());
            
            // 2. 验证订单状态是否允许收款
            if (!"approved".equals(order.getOrderStatus())) {
                log.error("确认收款失败：订单状态不允许收款，订单ID：{}，当前状态：{}，期望状态：approved", 
                        reqDTO.getOrderId(), order.getOrderStatus());
                throw new ServiceException(ErrorCodeConstants.ORDER_STATUS_NOT_ALLOW_COLLECTION);
            }
            
            if (!"pending".equals(order.getPaymentStatus()) && !"pending_payment".equals(order.getPaymentStatus())) {
                log.error("确认收款失败：支付状态不允许收款，订单ID：{}，当前状态：{}，期望状态：pending或pending_payment", 
                        reqDTO.getOrderId(), order.getPaymentStatus());
                throw new ServiceException(ErrorCodeConstants.ORDER_STATUS_NOT_ALLOW_COLLECTION);
            }
            
            // 3. 验证收款金额
            if (reqDTO.getCollectionAmount() == null || reqDTO.getCollectionAmount().compareTo(BigDecimal.ZERO) <= 0) {
                log.error("确认收款失败：收款金额无效，订单ID：{}，收款金额：{}", 
                        reqDTO.getOrderId(), reqDTO.getCollectionAmount());
                throw new ServiceException(ErrorCodeConstants.COLLECTION_AMOUNT_INVALID);
            }
            
            if (order.getTotalAmount() != null && reqDTO.getCollectionAmount().compareTo(order.getTotalAmount()) > 0) {
                log.error("确认收款失败：收款金额超过订单总金额，订单ID：{}，收款金额：{}，总金额：{}", 
                        reqDTO.getOrderId(), reqDTO.getCollectionAmount(), order.getTotalAmount());
                throw new ServiceException(ErrorCodeConstants.COLLECTION_AMOUNT_EXCEEDS_TOTAL);
            }
            
            // 4. 创建支付记录
            PublicbizOrderPaymentDO paymentDO = new PublicbizOrderPaymentDO();
            paymentDO.setOrderId(reqDTO.getOrderId());
            paymentDO.setOrderNo(reqDTO.getOrderNo());
            paymentDO.setPaymentNo(generatePaymentNo());
            paymentDO.setPaymentType(reqDTO.getCollectionMethod());
            paymentDO.setPaymentAmount(reqDTO.getCollectionAmount());
            paymentDO.setPaymentStatus("success");
            paymentDO.setPaymentTime(LocalDateTime.now());
            paymentDO.setOperatorId(getCurrentUserId());
            paymentDO.setOperatorName(reqDTO.getOperatorName());
            paymentDO.setPaymentRemark(reqDTO.getCollectionRemark());
            paymentDO.setTransactionId(reqDTO.getTransactionId());
            
            log.info("准备创建支付记录：{}", paymentDO);
            
            // 5. 保存支付记录
            int insertResult = publicbizOrderPaymentMapper.insert(paymentDO);
            if (insertResult <= 0) {
                log.error("确认收款失败：支付记录创建失败，订单ID：{}", reqDTO.getOrderId());
                throw new ServiceException(ErrorCodeConstants.PAYMENT_RECORD_CREATE_FAILED);
            }
            
            log.info("支付记录创建成功，支付记录ID：{}", paymentDO.getId());
            
            // 6. 更新订单状态
            order.setPaymentStatus("paid");
            order.setPaidAmount(reqDTO.getCollectionAmount());
            order.setSettlementTime(LocalDateTime.now());
            order.setSettlementMethod(reqDTO.getCollectionMethod());
            order.setRemark(reqDTO.getCollectionRemark());
            
            int updateResult = orderMapper.updateById(order);
            if (updateResult <= 0) {
                log.error("确认收款失败：订单状态更新失败，订单ID：{}", reqDTO.getOrderId());
                throw new ServiceException(ErrorCodeConstants.ORDER_UPDATE_FAILED);
            }
            
            log.info("订单状态更新成功，订单ID：{}", reqDTO.getOrderId());
            
            // 7. 记录操作日志
            PublicbizOrderLogDO logDO = new PublicbizOrderLogDO();
            logDO.setOrderNo(reqDTO.getOrderNo());
            logDO.setLogType("确认收款");
            logDO.setLogTitle("确认收款");
            logDO.setLogContent("确认收款，收款金额：" + reqDTO.getCollectionAmount() + 
                    "，收款方式：" + reqDTO.getCollectionMethod() + 
                    "，收款备注：" + reqDTO.getCollectionRemark());
            logDO.setOldStatus("pending");
            logDO.setNewStatus("paid");
            logDO.setOperatorId(getCurrentUserId());
            logDO.setOperatorName(reqDTO.getOperatorName());
            logDO.setOperatorRole("财务人员");
            
            int logResult = orderLogMapper.insert(logDO);
            if (logResult <= 0) {
                log.warn("操作日志记录失败，但不影响收款流程，订单ID：{}", reqDTO.getOrderId());
            }
            
            log.info("确认收款操作，订单号：{}，操作：确认收款，状态变更：待支付 -> 已支付，操作人：{}", 
                    order.getOrderNo(), reqDTO.getOperatorName());
            
            log.info("确认收款成功，订单号：{}，支付记录ID：{}", reqDTO.getOrderNo(), paymentDO.getId());
            
            // 8. 转换为响应DTO
            return convertToCollectionRespDTO(paymentDO);
            
        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("确认收款失败，订单号：{}，错误：{}", reqDTO.getOrderNo(), e.getMessage(), e);
            throw new ServiceException(ErrorCodeConstants.ORDER_COLLECTION_FAILED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCollectionRespDTO updateCollection(OrderCollectionReqDTO reqDTO) {
        log.info("开始更新收款信息，订单号：{}，收款金额：{}", reqDTO.getOrderNo(), reqDTO.getCollectionAmount());
        
        try {
            // 1. 查询现有的支付记录
            List<PublicbizOrderPaymentDO> existingPayments = publicbizOrderPaymentMapper.selectList(
                new LambdaQueryWrapper<PublicbizOrderPaymentDO>()
                    .eq(PublicbizOrderPaymentDO::getOrderId, reqDTO.getOrderId())
                    .eq(PublicbizOrderPaymentDO::getOrderNo, reqDTO.getOrderNo())
            );
            
            if (existingPayments.isEmpty()) {
                throw new ServiceException(ErrorCodeConstants.PAYMENT_RECORD_NOT_EXISTS);
            }
            
            // 2. 更新第一条支付记录
            PublicbizOrderPaymentDO existingPayment = existingPayments.get(0);
            existingPayment.setPaymentType(reqDTO.getCollectionMethod());
            existingPayment.setPaymentAmount(reqDTO.getCollectionAmount());
            existingPayment.setPaymentTime(LocalDateTime.now());
            existingPayment.setOperatorId(getCurrentUserId());
            existingPayment.setOperatorName(reqDTO.getOperatorName());
            existingPayment.setPaymentRemark(reqDTO.getCollectionRemark());
            existingPayment.setTransactionId(reqDTO.getTransactionId());
            
            // 3. 保存更新
            publicbizOrderPaymentMapper.updateById(existingPayment);
            
            // 4. 更新订单已支付金额
            PublicbizOrderDO order = orderMapper.selectById(reqDTO.getOrderId());
            if (order != null) {
                order.setPaidAmount(reqDTO.getCollectionAmount());
                orderMapper.updateById(order);
            }
            
            // 5. 记录操作日志
            log.info("更新收款操作，订单号：{}，操作：更新收款信息，状态：{}，操作人：{}", 
                    order.getOrderNo(), order.getOrderStatus(), reqDTO.getOperatorName());
            
            log.info("更新收款信息成功，订单号：{}，支付记录ID：{}", reqDTO.getOrderNo(), existingPayment.getId());
            
            // 6. 转换为响应DTO
            return convertToCollectionRespDTO(existingPayment);
            
        } catch (Exception e) {
            log.error("更新收款信息失败，订单号：{}，错误：{}", reqDTO.getOrderNo(), e.getMessage(), e);
            throw new ServiceException(ErrorCodeConstants.ORDER_UPDATE_COLLECTION_FAILED);
        }
    }

    /**
     * 生成支付单号
     */
    private String generatePaymentNo() {
        // 生成支付单号：PAY + 年月日 + 6位序号
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String sequence = String.format("%06d", System.currentTimeMillis() % 1000000);
        return "PAY" + dateStr + sequence;
    }

    /**
     * 将支付记录DO转换为收款响应DTO
     */
    private OrderCollectionRespDTO convertToCollectionRespDTO(PublicbizOrderPaymentDO paymentDO) {
        OrderCollectionRespDTO respDTO = new OrderCollectionRespDTO();
        respDTO.setPaymentId(paymentDO.getId());
        respDTO.setOrderId(paymentDO.getOrderId());
        respDTO.setOrderNo(paymentDO.getOrderNo());
        respDTO.setPaymentNo(paymentDO.getPaymentNo());
        respDTO.setPaymentType(paymentDO.getPaymentType());
        respDTO.setPaymentAmount(paymentDO.getPaymentAmount());
        respDTO.setPaymentStatus(paymentDO.getPaymentStatus());
        respDTO.setPaymentTime(paymentDO.getPaymentTime());
        respDTO.setOperatorId(paymentDO.getOperatorId());
        respDTO.setOperatorName(paymentDO.getOperatorName());
        respDTO.setPaymentRemark(paymentDO.getPaymentRemark());
        respDTO.setTransactionId(paymentDO.getTransactionId());
        respDTO.setCreateTime(paymentDO.getCreateTime());
        return respDTO;
    }

    /**
     * 获取当前用户ID
     * TODO: 实现获取当前登录用户ID的逻辑
     */
    private Long getCurrentUserId() {
        // TODO: 从安全上下文获取当前用户ID
        return WebFrameworkUtils.getLoginUserId();
    }

    /**
     * 审批通过高校实践订单
     *
     * @param orderId 订单ID
     * @param orderNo 订单号
     * @param approverId 审批人ID
     * @param approverName 审批人姓名
     * @param comments 审批意见
     */
    public void approveOrder(Long orderId, String orderNo, Long approverId, String approverName, String comments) {
        log.info("开始审批通过高校实践订单，订单ID：{}，订单号：{}", orderId, orderNo);
        
        try {
            // 1. 验证订单是否存在
            PublicbizOrderDO order = orderMapper.selectById(orderId);
            if (order == null) {
                throw new RuntimeException("订单不存在，订单ID：" + orderId);
            }
            
            // 2. 更新订单状态为已批准
            PublicbizOrderDO updateOrder = new PublicbizOrderDO();
            updateOrder.setId(orderId);
            updateOrder.setOrderStatus("approved");
            updateOrder.setRemark(comments);
            
            int updateResult = orderMapper.updateById(updateOrder);
            if (updateResult <= 0) {
                log.error("更新订单状态失败，订单ID：{}", orderId);
                throw new RuntimeException("更新订单状态失败");
            }
            
            // 3. 记录审批通过日志
            PublicbizOrderLogDO logDO = new PublicbizOrderLogDO();
            logDO.setOrderNo(orderNo);
            logDO.setLogType("审批通过");
            logDO.setLogTitle("审批通过");
            logDO.setLogContent("审批通过，审批意见：" + comments);
            logDO.setOldStatus(order.getOrderStatus());
            logDO.setNewStatus("approved");
            logDO.setOperatorId(approverId);
            logDO.setOperatorName(approverName);
            logDO.setOperatorRole("审批人");
            
            int logResult = orderLogMapper.insert(logDO);
            if (logResult <= 0) {
                log.warn("记录审批日志失败，订单ID：{}", orderId);
            }
            
            log.info("高校实践订单审批通过成功，订单ID：{}，订单号：{}", orderId, orderNo);
            
        } catch (Exception e) {
            log.error("审批通过高校实践订单失败，订单ID：{}，错误：{}", orderId, e.getMessage(), e);
            throw new RuntimeException("审批通过失败：" + e.getMessage());
        }
    }

    /**
     * 审批拒绝高校实践订单
     *
     * @param orderId 订单ID
     * @param orderNo 订单号
     * @param approverId 审批人ID
     * @param approverName 审批人姓名
     * @param rejectReason 拒绝原因
     * @param comments 审批意见
     */
    public void rejectOrder(Long orderId, String orderNo, Long approverId, String approverName, String rejectReason, String comments) {
        log.info("开始审批拒绝高校实践订单，订单ID：{}，订单号：{}", orderId, orderNo);
        
        try {
            // 1. 验证订单是否存在
            PublicbizOrderDO order = orderMapper.selectById(orderId);
            if (order == null) {
                throw new RuntimeException("订单不存在，订单ID：" + orderId);
            }
            
            // 2. 更新订单状态为已拒绝
            PublicbizOrderDO updateOrder = new PublicbizOrderDO();
            updateOrder.setId(orderId);
            updateOrder.setOrderStatus("rejected");
            updateOrder.setRemark(comments);
            
            int updateResult = orderMapper.updateById(updateOrder);
            if (updateResult <= 0) {
                log.error("更新订单状态失败，订单ID：{}", orderId);
                throw new RuntimeException("更新订单状态失败");
            }
            
            // 3. 记录审批拒绝日志
            PublicbizOrderLogDO logDO = new PublicbizOrderLogDO();
            logDO.setOrderNo(orderNo);
            logDO.setLogType("审批拒绝");
            logDO.setLogTitle("审批拒绝");
            logDO.setLogContent("审批拒绝，拒绝原因：" + rejectReason + "，审批意见：" + comments);
            logDO.setOldStatus(order.getOrderStatus());
            logDO.setNewStatus("rejected");
            logDO.setOperatorId(approverId);
            logDO.setOperatorName(approverName);
            logDO.setOperatorRole("审批人");
            
            int logResult = orderLogMapper.insert(logDO);
            if (logResult <= 0) {
                log.warn("记录审批日志失败，订单ID：{}", orderId);
            }
            
            log.info("高校实践订单审批拒绝成功，订单ID：{}，订单号：{}", orderId, orderNo);
            
        } catch (Exception e) {
            log.error("审批拒绝高校实践订单失败，订单ID：{}，错误：{}", orderId, e.getMessage(), e);
            throw new RuntimeException("审批拒绝失败：" + e.getMessage());
        }
    }

    /**
     * 标记高校实践订单完成
     *
     * @param orderId 订单ID
     * @param orderNo 订单号
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param comments 完成说明
     */
    public void completeOrder(Long orderId, String orderNo, Long operatorId, String operatorName, String comments) {
        log.info("开始标记高校实践订单完成，订单ID：{}，订单号：{}", orderId, orderNo);
        
        try {
            // 1. 验证订单是否存在
            PublicbizOrderDO order = orderMapper.selectById(orderId);
            if (order == null) {
                throw new RuntimeException("订单不存在，订单ID：" + orderId);
            }
            
            // 2. 验证订单状态是否允许完成
            if (!"approved".equals(order.getOrderStatus())) {
                throw new RuntimeException("订单状态不允许完成，当前状态：" + order.getOrderStatus());
            }
            
            // 3. 更新订单状态为已完成
            PublicbizOrderDO updateOrder = new PublicbizOrderDO();
            updateOrder.setId(orderId);
            updateOrder.setOrderStatus("completed");
            updateOrder.setRemark(comments);
            
            int updateResult = orderMapper.updateById(updateOrder);
            if (updateResult <= 0) {
                log.error("更新订单状态失败，订单ID：{}", orderId);
                throw new RuntimeException("更新订单状态失败");
            }
            
            // 4. 记录订单完成日志
            PublicbizOrderLogDO logDO = new PublicbizOrderLogDO();
            logDO.setOrderNo(orderNo);
            logDO.setLogType("订单完成");
            logDO.setLogTitle("订单完成");
            logDO.setLogContent("订单完成，完成说明：" + comments);
            logDO.setOldStatus(order.getOrderStatus());
            logDO.setNewStatus("completed");
            logDO.setOperatorId(operatorId);
            logDO.setOperatorName(operatorName);
            logDO.setOperatorRole("操作员");
            
            int logResult = orderLogMapper.insert(logDO);
            if (logResult <= 0) {
                log.warn("记录订单完成日志失败，订单ID：{}", orderId);
            }
            
            log.info("高校实践订单完成标记成功，订单ID：{}，订单号：{}", orderId, orderNo);
            
        } catch (Exception e) {
            log.error("标记高校实践订单完成失败，订单ID：{}，错误：{}", orderId, e.getMessage(), e);
            throw new RuntimeException("标记订单完成失败：" + e.getMessage());
        }
    }

}
