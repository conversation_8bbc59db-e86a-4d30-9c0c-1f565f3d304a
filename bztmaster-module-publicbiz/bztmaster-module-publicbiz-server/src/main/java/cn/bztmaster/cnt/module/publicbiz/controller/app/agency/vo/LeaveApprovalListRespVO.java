package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "请假申请列表响应VO")
@Data
public class LeaveApprovalListRespVO {

    @Schema(description = "申请ID")
    private Long id;

    @Schema(description = "工单编号")
    private String workOrderNo;

    @Schema(description = "阿姨OneID")
    private String auntOneId;

    @Schema(description = "阿姨姓名")
    private String auntName;

    @Schema(description = "请假类型(1-请假,2-调休)")
    private Integer leaveType;

    @Schema(description = "请假类型文本")
    private String leaveTypeText;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "请假天数")
    private Double durationDays;

    @Schema(description = "请假时长(小时)")
    private Double durationHours;

    @Schema(description = "请假理由")
    private String reason;

    @Schema(description = "工单状态")
    private String workOrderStatus;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "受影响订单数量")
    private Integer affectedOrdersCount;
}
