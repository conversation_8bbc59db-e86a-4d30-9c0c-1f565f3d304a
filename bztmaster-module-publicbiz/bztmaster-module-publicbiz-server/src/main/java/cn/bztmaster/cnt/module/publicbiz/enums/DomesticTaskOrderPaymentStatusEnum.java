package cn.bztmaster.cnt.module.publicbiz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 家政服务订单支付状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DomesticTaskOrderPaymentStatusEnum {

    UNPAID("unpaid", "未支付"),
    PAID("paid", "已支付"),
    PARTIAL("partial", "部分支付");

    /**
     * 状态值
     */
    private final String value;
    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态值获取状态名称
     *
     * @param value 状态值
     * @return 状态名称
     */
    public static String getNameByValue(String value) {
        for (DomesticTaskOrderPaymentStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status.getName();
            }
        }
        return "未知状态";
    }

    /**
     * 根据状态值获取枚举
     *
     * @param value 状态值
     * @return 枚举
     */
    public static DomesticTaskOrderPaymentStatusEnum getByValue(String value) {
        for (DomesticTaskOrderPaymentStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}

