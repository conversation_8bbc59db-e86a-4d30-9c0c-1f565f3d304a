package cn.bztmaster.cnt.module.publicbiz.framework.rpc.config;

import cn.bztmaster.cnt.framework.common.biz.system.oauth2.OAuth2TokenCommonApi;
import cn.bztmaster.cnt.module.infra.api.file.FileApi;
import cn.bztmaster.cnt.module.system.api.logger.LoginLogApi;
import cn.bztmaster.cnt.module.system.api.notify.AppNotifySendApi;
import cn.bztmaster.cnt.module.system.api.sms.SmsCodeApi;
import cn.bztmaster.cnt.module.talentpool.api.talent.TalentUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * publicbiz 模块的 RPC 配置类
 *
 * <AUTHOR>
 */
@Configuration(value = "publicbizRpcConfiguration", proxyBeanMethods = false)
@EnableFeignClients(clients = {
                SmsCodeApi.class,
                OAuth2TokenCommonApi.class,
                LoginLogApi.class,
                TalentUserApi.class,
                FileApi.class,
                AppNotifySendApi.class
})
public class RpcConfiguration {
}