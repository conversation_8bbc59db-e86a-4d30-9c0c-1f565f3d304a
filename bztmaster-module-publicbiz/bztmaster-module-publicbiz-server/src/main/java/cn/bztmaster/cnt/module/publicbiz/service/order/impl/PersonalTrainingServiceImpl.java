package cn.bztmaster.cnt.module.publicbiz.service.order.impl;

import cn.bztmaster.cnt.module.publicbiz.api.order.dto.*;
import cn.bztmaster.cnt.module.publicbiz.convert.order.PersonalTrainingConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PersonalTrainingDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizPartnerContractDO;
// import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PersonalTrainingApprovalDO;
// import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PersonalTrainingContractDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PersonalTrainingMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizPartnerContractMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderPaymentMapper;
// import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PersonalTrainingApprovalMapper;
// import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PersonalTrainingContractMapper;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderLogService;
import cn.bztmaster.cnt.module.publicbiz.service.order.PersonalTrainingService;
import cn.bztmaster.cnt.module.publicbiz.util.OrderLogContentBuilder;
import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.bztmaster.cnt.module.publicbiz.enums.PersonalTrainingErrorCodeConstants.*;
import cn.bztmaster.cnt.module.publicbiz.enums.order.LearningStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.order.ExamStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.util.StatusValidationUtil;
import cn.hutool.core.util.StrUtil;
import java.time.LocalDate;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 个人培训与认证订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PersonalTrainingServiceImpl implements PersonalTrainingService {

    @Resource
    private PersonalTrainingMapper personalTrainingMapper;
    
    @Resource
    private PublicbizOrderMapper publicbizOrderMapper;
    
    @Resource
    private PublicbizOrderLogMapper publicbizOrderLogMapper;
    
    @Resource
    private PublicbizPartnerContractMapper publicbizPartnerContractMapper;
    
    @Resource
    private PublicbizOrderPaymentMapper publicbizOrderPaymentMapper;
    
    @Resource
    private OrderLogService orderLogService;

    // 新增的Mapper依赖（注释掉，因为审批表不存在）
    // @Resource
    // private PersonalTrainingApprovalMapper personalTrainingApprovalMapper;
    
    // @Resource
    // private PersonalTrainingContractMapper personalTrainingContractMapper;
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "PT" + System.currentTimeMillis();
    }

    /**
     * 生成合同编号
     */
    private String generateContractNo() {
        return "CT" + System.currentTimeMillis();
    }

    /**
     * 创建订单主表记录
     */
    private PublicbizOrderDO createMainOrder(PersonalTrainingSaveReqDTO reqDTO, String orderNo) {
        PublicbizOrderDO order = new PublicbizOrderDO();
        order.setOrderNo(orderNo);
        
        // 根据请求中的订单类型正确设置订单类型，业务线保持为"个人培训与认证"
        if ("certification".equals(reqDTO.getOrderType())) {
            // 考试认证
            order.setOrderType("certification");
        } else if ("training".equals(reqDTO.getOrderType())) {
            // 个人培训
            order.setOrderType("personal");
        } else {
            // 默认值，记录警告日志
            log.warn("未知的订单类型：{}，使用默认值：personal", reqDTO.getOrderType());
            order.setOrderType("personal");
        }
        order.setBusinessLine("个人培训与认证"); // 设置业务线
        
        // 设置商机和线索ID
        if (StrUtil.isNotBlank(reqDTO.getBusinessOpportunity())) {
            order.setOpportunityId(reqDTO.getBusinessOpportunity());
            log.info("设置关联商机ID：{}", reqDTO.getBusinessOpportunity());
        }
        if (StrUtil.isNotBlank(reqDTO.getAssociatedLead())) {
            order.setLeadId(reqDTO.getAssociatedLead());
            log.info("设置关联线索ID：{}", reqDTO.getAssociatedLead());
        }
        order.setProjectName(reqDTO.getCourseName());
        order.setProjectDescription(reqDTO.getCourseType());
        order.setTotalAmount(reqDTO.getOrderAmount());
        order.setPaidAmount(BigDecimal.ZERO);
        order.setRefundAmount(BigDecimal.ZERO);
        order.setPaymentStatus(reqDTO.getPaymentStatus());
        order.setOrderStatus(reqDTO.getOrderStatus() != null ? reqDTO.getOrderStatus() : "draft");
        
        // 设置合同相关字段
        order.setContractType(reqDTO.getContractType() != null ? reqDTO.getContractType() : "electronic");
        order.setContractFileUrl(reqDTO.getContractAttachment());
        order.setContractStatus("unsigned"); // 初始状态为未签署
        
        // 设置删除标记为false（未删除）
        order.setDeleted(false);
        
        // 其他字段由 MyBatis Plus 自动填充
        return order;
    }

    /**
     * 创建个人培训订单详情记录
     */
    private PersonalTrainingDO createDetailOrder(PersonalTrainingSaveReqDTO reqDTO, Long mainOrderId, String orderNo) {
        PersonalTrainingDO detail = new PersonalTrainingDO();
        detail.setOrderId(mainOrderId);
        detail.setOrderNo(orderNo);
        
        // 为 student_oneid 生成一个默认值，避免数据库插入失败
        detail.setStudentOneid(reqDTO.getStudentOneid() != null ? reqDTO.getStudentOneid() : "STU_" + System.currentTimeMillis());
        
        detail.setStudentName(reqDTO.getStudentName());
        detail.setStudentPhone(reqDTO.getStudentPhone());
        detail.setStudentEmail(reqDTO.getStudentEmail());
        detail.setCourseName(reqDTO.getCourseName());
        // 根据订单类型设置课程类型
        if (reqDTO.getCourseType() != null) {
            detail.setCourseType(reqDTO.getCourseType());
        } else {
            if ("certification".equals(reqDTO.getOrderType())) {
                detail.setCourseType("考试认证");
            } else {
                detail.setCourseType("个人培训");
            }
        }
        detail.setCourseDescription(reqDTO.getCourseDescription());
        detail.setCourseDuration(reqDTO.getCourseDuration());
        
        // 设置学习状态，使用验证工具类
        StatusValidationUtil.ValidationResult learningStatusResult = 
            StatusValidationUtil.validateLearningStatus(reqDTO.getLearningStatus(), 50);
        detail.setLearningStatus(learningStatusResult.getValue());
        if (!learningStatusResult.isValid()) {
            log.warn("学习状态验证失败：{}", learningStatusResult.getMessage());
        }
        
        // 设置考试状态，使用验证工具类
        StatusValidationUtil.ValidationResult examStatusResult = 
            StatusValidationUtil.validateExamStatus("not_registered", 50);
        detail.setExamStatus(examStatusResult.getValue());
        if (!examStatusResult.isValid()) {
            log.warn("考试状态验证失败：{}", examStatusResult.getMessage());
        }
        
        // 设置费用信息
        detail.setCourseFee(reqDTO.getOrderAmount() != null ? reqDTO.getOrderAmount().multiply(new BigDecimal("0.8")) : BigDecimal.ZERO);
        detail.setExamFee(reqDTO.getOrderAmount() != null ? reqDTO.getOrderAmount().multiply(new BigDecimal("0.15")) : BigDecimal.ZERO);
        detail.setCertificationFee(reqDTO.getOrderAmount() != null ? reqDTO.getOrderAmount().multiply(new BigDecimal("0.05")) : BigDecimal.ZERO);
        
        // 这些字段已移动到订单主表，不需要在这里设置
        
        // 设置租户ID
        detail.setTenantId(1L); // 设置默认租户ID
        
        // 设置删除标记为false（未删除）
        detail.setDeleted(false);
        
        // 其他字段由 MyBatis Plus 自动填充
        return detail;
    }

    /**
     * 创建合作伙伴合同记录
     */
    private PublicbizPartnerContractDO createPartnerContract(PersonalTrainingSaveReqDTO reqDTO, Long mainOrderId) {
        PublicbizPartnerContractDO contract = new PublicbizPartnerContractDO();
        
        // 设置合同基本信息
        contract.setPartnerId(mainOrderId);
        // 根据订单类型设置合同名称
        if (reqDTO.getContractName() != null) {
            contract.setContractName(reqDTO.getContractName());
        } else {
            if ("certification".equals(reqDTO.getOrderType())) {
                contract.setContractName("考试认证合同");
            } else {
                contract.setContractName("个人培训合同");
            }
        }
        contract.setContractNumber(reqDTO.getContractNumber() != null ? reqDTO.getContractNumber() : generateContractNo());
        
        // 设置合同日期
        if (StrUtil.isNotBlank(reqDTO.getSigningDate())) {
            try {
                LocalDate signingDate = LocalDate.parse(reqDTO.getSigningDate());
                contract.setStartDate(signingDate);
                // 设置结束日期为开始日期后一年
                contract.setEndDate(signingDate.plusYears(1));
            } catch (Exception e) {
                log.warn("解析签署日期失败：{}，使用当前日期", reqDTO.getSigningDate());
                LocalDate now = LocalDate.now();
                contract.setStartDate(now);
                contract.setEndDate(now.plusYears(1));
            }
        } else {
            LocalDate now = LocalDate.now();
            contract.setStartDate(now);
            contract.setEndDate(now.plusYears(1));
        }
        
        // 设置合同金额
        contract.setAmount(reqDTO.getContractAmount() != null ? reqDTO.getContractAmount() : reqDTO.getOrderAmount());
        
        // 设置合同状态
        contract.setStatus("有效");
        
        // 设置合同附件路径
        contract.setAttachmentPath(reqDTO.getContractAttachment());
        
        // 设置签约人
        contract.setSigner(reqDTO.getStudentName());
        
        // 设置其他字段
        contract.setCreateTime(LocalDateTime.now());
        contract.setUpdateTime(LocalDateTime.now());
        contract.setCreator("1"); // 设置创建者ID
        contract.setUpdater("1"); // 设置更新者ID
        contract.setTenantId(1L); // 设置租户ID
        
        // 设置删除标记为false（未删除）
        contract.setDeleted(false);
        
        return contract;
    }

    /**
     * 创建支付记录
     */
    private void createPaymentRecord(PersonalTrainingSaveReqDTO reqDTO, Long mainOrderId, String orderNo) {
        try {
            log.info("开始创建支付记录，订单ID：{}，订单号：{}", mainOrderId, orderNo);
            
            // 生成支付单号
            String paymentNo = "PAY" + System.currentTimeMillis();
            
            // 创建支付记录
            PublicbizOrderPaymentDO paymentRecord = PublicbizOrderPaymentDO.builder()
                    .orderId(mainOrderId)
                    .orderNo(orderNo)
                    .paymentNo(paymentNo)
                    .paymentType(reqDTO.getCollectionMethod() != null ? reqDTO.getCollectionMethod() : "other")
                    .paymentAmount(reqDTO.getCollectionAmount() != null ? reqDTO.getCollectionAmount() : reqDTO.getOrderAmount())
                    .paymentStatus("success")
                    .paymentTime(LocalDateTime.now())
                    .operatorId(1L)
                    .operatorName("系统")
                    .paymentRemark("订单创建时自动生成支付记录")
                    .transactionId("TXN" + System.currentTimeMillis())
                    .build();
            
            // 手动设置deleted字段
            paymentRecord.setDeleted(false);
            
            int paymentInsertResult = publicbizOrderPaymentMapper.insert(paymentRecord);
            if (paymentInsertResult <= 0) {
                log.error("创建支付记录失败，订单ID：{}", mainOrderId);
                throw new RuntimeException("创建支付记录失败");
            }
            
            log.info("支付记录创建成功，支付单号：{}，订单ID：{}", paymentNo, mainOrderId);
            
        } catch (Exception e) {
            log.error("创建支付记录异常，订单ID：{}，订单号：{}", mainOrderId, orderNo, e);
            throw new RuntimeException("创建支付记录失败：" + e.getMessage());
        }
    }

    /**
     * 记录订单创建日志
     */
    private void recordOrderCreateLog(PersonalTrainingSaveReqDTO reqDTO, PublicbizOrderDO mainOrder, 
                                    String oldStatus, String newStatus, Long operatorId, String operatorName, String remark) {
        // 这里可以根据需要实现日志记录逻辑
        log.info("记录订单创建日志，订单号：{}", mainOrder.getOrderNo());
    }

    /**
     * 记录订单删除日志
     */
    private void recordOrderDeleteLog(PersonalTrainingDeleteReqDTO reqDTO, PersonalTrainingDO orderDO, 
                                    String oldStatus, Long operatorId, String operatorName, String remark) {
        // 这里可以根据需要实现日志记录逻辑
        log.info("记录订单删除日志，订单号：{}", orderDO.getOrderNo());
    }

    @Override
    public PageResult<PersonalTrainingRespDTO> pagePersonalTraining(PersonalTrainingPageReqDTO reqDTO) {
        log.info("开始分页查询个人培训订单，查询条件：{}", reqDTO);
        
        try {
            // 构建查询条件
            LambdaQueryWrapperX<PersonalTrainingDO> queryWrapper = new LambdaQueryWrapperX<>();
            
            if (StrUtil.isNotBlank(reqDTO.getOrderNo())) {
                queryWrapper.eq(PersonalTrainingDO::getOrderNo, reqDTO.getOrderNo());
            }
            if (StrUtil.isNotBlank(reqDTO.getStudentName())) {
                queryWrapper.like(PersonalTrainingDO::getStudentName, reqDTO.getStudentName());
            }
            if (StrUtil.isNotBlank(reqDTO.getStudentPhone())) {
                queryWrapper.eq(PersonalTrainingDO::getStudentPhone, reqDTO.getStudentPhone());
            }
            if (StrUtil.isNotBlank(reqDTO.getCourseName())) {
                queryWrapper.like(PersonalTrainingDO::getCourseName, reqDTO.getCourseName());
            }
            if (StrUtil.isNotBlank(reqDTO.getKeyword())) {
                queryWrapper.and(wrapper -> wrapper
                    .like(PersonalTrainingDO::getStudentName, reqDTO.getKeyword())
                    .or()
                    .like(PersonalTrainingDO::getCourseName, reqDTO.getKeyword())
                );
            }
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc(PersonalTrainingDO::getId);
            
            log.info("构建查询条件完成，开始执行分页查询");
            
            // 执行分页查询
            PageResult<PersonalTrainingDO> pageResult = personalTrainingMapper.selectPage(reqDTO, queryWrapper);
            
            log.info("分页查询执行完成，查询到{}条记录，总记录数：{}", pageResult.getList().size(), pageResult.getTotal());
            
            // 如果查询结果为空，直接返回空结果
            if (pageResult.getList().isEmpty()) {
                log.info("分页查询结果为空，返回空分页结果");
                return new PageResult<>(new ArrayList<>(), 0L);
            }
            
            // 转换为DTO，需要查询订单主表信息
            List<PersonalTrainingRespDTO> respList = new ArrayList<>();
            int successCount = 0;
            int failCount = 0;
            
            for (PersonalTrainingDO personalTraining : pageResult.getList()) {
                try {
                    // 查询订单主表信息
                    PublicbizOrderDO order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
                    if (order == null) {
                        log.warn("订单主表信息不存在，个人培训订单ID：{}，关联订单ID：{}", personalTraining.getId(), personalTraining.getOrderId());
                        failCount++;
                        continue;
                    }
                    
                    PersonalTrainingRespDTO respDTO = convertToPersonalTrainingRespDTO(personalTraining, order);
                    if (respDTO != null) {
                        respList.add(respDTO);
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.warn("转换订单数据失败，个人培训订单ID：{}，错误：{}", personalTraining.getId(), e.getMessage());
                    failCount++;
                }
            }
            
            log.info("数据转换完成，成功转换{}条，失败{}条，总记录数：{}", successCount, failCount, pageResult.getTotal());
            
            // 如果所有记录都转换失败，返回空结果
            if (respList.isEmpty()) {
                log.warn("所有记录转换失败，返回空分页结果");
                return new PageResult<>(new ArrayList<>(), 0L);
            }
            
            return new PageResult<>(respList, pageResult.getTotal());
            
        } catch (Exception e) {
            log.error("分页查询个人培训订单失败，错误：{}", e.getMessage(), e);
            // 分页查询失败时，返回空结果而不是抛出异常
            return new PageResult<>(new ArrayList<>(), 0L);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PersonalTrainingSaveRespDTO createPersonalTraining(PersonalTrainingSaveReqDTO reqDTO) {
        log.info("开始创建个人培训与认证订单，学员姓名：{}，课程名称：{}", reqDTO.getStudentName(), reqDTO.getCourseName());
        
        try {
            // 1. 生成订单号
            String orderNo = generateOrderNo();
            log.info("生成订单号：{}", orderNo);
            
            // 2. 创建订单主表记录 (publicbiz_order)
            PublicbizOrderDO mainOrder = createMainOrder(reqDTO, orderNo);
            publicbizOrderMapper.insert(mainOrder);
            log.info("订单主表记录创建成功，订单ID：{}", mainOrder.getId());
            
            // 3. 创建个人培训订单详情记录 (publicbiz_personal_order)
            PersonalTrainingDO detailOrder = createDetailOrder(reqDTO, mainOrder.getId(), orderNo);
            personalTrainingMapper.insert(detailOrder);
            log.info("个人培训订单详情记录创建成功");
            
            // 4. 创建合作伙伴合同记录 (publicbiz_partner_contract)
            PublicbizPartnerContractDO contract = createPartnerContract(reqDTO, mainOrder.getId());
            publicbizPartnerContractMapper.insert(contract);
            log.info("合作伙伴合同记录创建成功");
            
            // 5. 如果支付状态为已支付，创建支付记录 (publicbiz_order_payment)
            if ("paid".equals(reqDTO.getPaymentStatus())) {
                createPaymentRecord(reqDTO, mainOrder.getId(), orderNo);
                log.info("支付记录创建成功");
            }
            
            // 6. 记录创建日志 - 使用OrderLogService记录详细字段信息
            Map<String, Object> createDetails = new HashMap<>();
            createDetails.put("projectName", reqDTO.getCourseName());
            createDetails.put("studentName", reqDTO.getStudentName());
            createDetails.put("courseType", reqDTO.getCourseType());
            createDetails.put("totalAmount", reqDTO.getOrderAmount() != null ? reqDTO.getOrderAmount().toString() : "0");
            // 设置商机和线索ID（注释掉，因为DTO中没有这些字段）
            // createDetails.put("businessOpportunity", reqDTO.getBusinessOpportunity());
            // createDetails.put("associatedLead", reqDTO.getAssociatedLead());
            createDetails.put("orderSource", reqDTO.getOrderSource());
            createDetails.put("learningStatus", reqDTO.getLearningStatus());
            createDetails.put("contractType", reqDTO.getContractType());
            createDetails.put("contractNumber", reqDTO.getContractNumber());
            createDetails.put("contractName", reqDTO.getContractName());
            createDetails.put("contractAmount", reqDTO.getContractAmount() != null ? reqDTO.getContractAmount().toString() : "0");
            
            orderLogService.recordOrderCreate(orderNo, "personal-training", createDetails, 
                    null, "系统", "系统管理员");
            log.info("详细创建日志记录完成");
            
            // 8. 构建响应对象
            PersonalTrainingSaveRespDTO respDTO = new PersonalTrainingSaveRespDTO();
            respDTO.setOrderNo(orderNo);
            respDTO.setStudentName(reqDTO.getStudentName());
            respDTO.setCourseName(reqDTO.getCourseName());
            respDTO.setOrderAmount(reqDTO.getOrderAmount());
            respDTO.setOrderStatus(reqDTO.getOrderStatus());
            respDTO.setPaymentStatus(reqDTO.getPaymentStatus());
            
            log.info("个人培训与认证订单创建成功，订单号：{}", orderNo);
            return respDTO;
            
        } catch (Exception e) {
            log.error("创建个人培训与认证订单失败，错误：{}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_CREATE_FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePersonalTraining(PersonalTrainingUpdateReqDTO reqDTO) {
        log.info("开始更新个人培训与认证订单，订单ID：{}，请求金额：{}", reqDTO.getId(), reqDTO.getOrderAmount());
        
        try {
            // 1. 首先尝试根据ID查找个人培训订单详情
            PersonalTrainingDO existingOrder = personalTrainingMapper.selectById(reqDTO.getId());
            
            // 如果找不到，尝试根据ID查找订单主表，然后查找对应的个人培训订单详情
            if (existingOrder == null) {
                log.info("未找到ID为{}的个人培训订单详情，尝试根据订单主表ID查找", reqDTO.getId());
                
                // 根据ID查找订单主表
                PublicbizOrderDO mainOrder = publicbizOrderMapper.selectById(reqDTO.getId());
                if (mainOrder == null) {
                    throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
                }
                
                // 根据订单号查找个人培训订单详情
                existingOrder = personalTrainingMapper.selectByOrderNo(mainOrder.getOrderNo());
                if (existingOrder == null) {
                    throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
                }
                
                log.info("通过订单主表ID找到对应的个人培训订单详情，订单号：{}", existingOrder.getOrderNo());
            }
            
            // 2. 获取原订单主表信息
            PublicbizOrderDO oldOrder = publicbizOrderMapper.selectByOrderNo(existingOrder.getOrderNo());
            if (oldOrder == null) {
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 3. 更新订单主表记录
            updateMainOrder(reqDTO, oldOrder);
            // 使用 LambdaUpdateWrapper 来避免参数绑定问题
            LambdaUpdateWrapper<PublicbizOrderDO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PublicbizOrderDO::getId, oldOrder.getId())
                    .eq(PublicbizOrderDO::getTenantId, oldOrder.getTenantId())
                    .eq(PublicbizOrderDO::getDeleted, false)
                    .set(PublicbizOrderDO::getOrderType, oldOrder.getOrderType())
                    .set(PublicbizOrderDO::getBusinessLine, oldOrder.getBusinessLine())
                    .set(PublicbizOrderDO::getOpportunityId, oldOrder.getOpportunityId())
                    .set(PublicbizOrderDO::getLeadId, oldOrder.getLeadId())
                    .set(PublicbizOrderDO::getOrderStatus, oldOrder.getOrderStatus())
                    .set(PublicbizOrderDO::getPaymentStatus, oldOrder.getPaymentStatus())
                    .set(PublicbizOrderDO::getProjectName, oldOrder.getProjectName())
                    .set(PublicbizOrderDO::getTotalAmount, oldOrder.getTotalAmount())
                    .set(PublicbizOrderDO::getContractType, oldOrder.getContractType())
                    .set(PublicbizOrderDO::getContractFileUrl, oldOrder.getContractFileUrl())
                    .set(PublicbizOrderDO::getContractStatus, oldOrder.getContractStatus())
                    .set(PublicbizOrderDO::getUpdater, oldOrder.getUpdater())
                    .set(PublicbizOrderDO::getUpdateTime, oldOrder.getUpdateTime());
            
            log.info("准备更新订单主表，更新后的金额：{}", oldOrder.getTotalAmount());
            int updateCount = publicbizOrderMapper.update(null, updateWrapper);
            if (updateCount == 0) {
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_UPDATE_FAIL);
            }
            log.info("订单主表记录更新成功，更新行数：{}", updateCount);
            
            // 验证更新结果
            PublicbizOrderDO updatedOrder = publicbizOrderMapper.selectById(oldOrder.getId());
            if (updatedOrder != null) {
                log.info("更新验证：订单ID：{}，更新后金额：{}", updatedOrder.getId(), updatedOrder.getTotalAmount());
            } else {
                log.warn("无法查询到更新后的订单记录");
            }
            
            // 4. 更新个人培训订单详情记录
            updateDetailOrder(reqDTO, existingOrder);
            // 使用 LambdaUpdateWrapper 来避免参数绑定问题
            LambdaUpdateWrapper<PersonalTrainingDO> detailUpdateWrapper = new LambdaUpdateWrapper<>();
            detailUpdateWrapper.eq(PersonalTrainingDO::getId, existingOrder.getId())
                    .eq(PersonalTrainingDO::getTenantId, existingOrder.getTenantId())
                    .eq(PersonalTrainingDO::getDeleted, false)
                    .set(PersonalTrainingDO::getStudentName, existingOrder.getStudentName())
                    .set(PersonalTrainingDO::getStudentOneid, existingOrder.getStudentOneid())
                    .set(PersonalTrainingDO::getStudentPhone, existingOrder.getStudentPhone())
                    .set(PersonalTrainingDO::getStudentEmail, existingOrder.getStudentEmail())
                    .set(PersonalTrainingDO::getCourseName, existingOrder.getCourseName())
                    .set(PersonalTrainingDO::getCourseType, existingOrder.getCourseType())
                    .set(PersonalTrainingDO::getCourseDescription, existingOrder.getCourseDescription())
                    .set(PersonalTrainingDO::getCourseDuration, existingOrder.getCourseDuration())
                    .set(PersonalTrainingDO::getLearningStatus, existingOrder.getLearningStatus())
                    .set(PersonalTrainingDO::getUpdater, existingOrder.getUpdater())
                    .set(PersonalTrainingDO::getUpdateTime, existingOrder.getUpdateTime());
            
            int detailUpdateCount = personalTrainingMapper.update(null, detailUpdateWrapper);
            if (detailUpdateCount == 0) {
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_UPDATE_FAIL);
            }
            log.info("个人培训订单详情记录更新成功");
            
            // 5. 处理支付记录更新
            handlePaymentRecordUpdate(reqDTO, oldOrder);
            
            // 6. 更新合作伙伴合同信息
            updatePartnerContract(reqDTO, existingOrder.getOrderNo());
            log.info("合作伙伴合同信息更新成功");
            
            // 7. 构建字段变更列表 - 记录N个字段的变更状态
            List<OrderLogService.FieldChange> changes = null;
            if (reqDTO != null && oldOrder != null && existingOrder != null) {
                changes = buildFieldChanges(reqDTO, oldOrder, existingOrder);
            } else {
                log.warn("reqDTO、oldOrder或existingOrder为null，跳过字段变更列表构建");
                changes = new ArrayList<>();
            }
            
            // 8. 记录编辑日志 - 使用OrderLogService记录详细字段变更
            String newStatus = reqDTO.getOrderStatus() != null ? reqDTO.getOrderStatus() : oldOrder.getOrderStatus();
            recordOrderUpdateLog(reqDTO, oldOrder, existingOrder, oldOrder.getOrderStatus(), newStatus, 
                    getCurrentUserId(), "系统管理员");
            log.info("详细编辑日志记录完成");
            
            // 9. 如果订单状态发生变化，记录状态变更日志
            if (reqDTO.getOrderStatus() != null && oldOrder.getOrderStatus() != null && 
                !reqDTO.getOrderStatus().equals(oldOrder.getOrderStatus())) {
                // 添加空值检查，避免空指针异常
                if (existingOrder != null && existingOrder.getOrderNo() != null) {
                    orderLogService.recordOrderStatusChange(existingOrder.getOrderNo(), "personal-training", 
                            "订单状态变更", oldOrder.getOrderStatus(), reqDTO.getOrderStatus(),
                            String.format("订单状态从【%s】变更为【%s】", oldOrder.getOrderStatus(), reqDTO.getOrderStatus()),
                            null, "系统", "系统管理员");
                    log.info("状态变更日志记录完成");
                } else {
                    log.warn("existingOrder或existingOrder.getOrderNo()为null，跳过状态变更日志记录");
                }
            } else {
                log.info("订单状态未发生变化或状态值为null，跳过状态变更日志记录");
            }
            
            log.info("个人培训与认证订单更新成功，订单ID：{}", reqDTO.getId());
            
        } catch (Exception e) {
            log.error("更新个人培训与认证订单失败，订单ID：{}，错误：{}", reqDTO.getId(), e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_UPDATE_FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePersonalTraining(Long id) {
        log.info("开始删除个人培训与认证订单，订单ID：{}", id);
        
        try {
            // 1. 首先尝试根据ID查找个人培训订单详情
            PersonalTrainingDO orderDO = personalTrainingMapper.selectById(id);
            
            // 如果找不到，尝试根据ID查找订单主表，然后查找对应的个人培训订单详情
            if (orderDO == null) {
                log.info("未找到ID为{}的个人培训订单详情，尝试根据订单主表ID查找", id);
                
                // 根据ID查找订单主表
                PublicbizOrderDO mainOrder = publicbizOrderMapper.selectById(id);
                if (mainOrder == null) {
                    throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
                }
                
                // 根据订单号查找个人培训订单详情
                orderDO = personalTrainingMapper.selectByOrderNo(mainOrder.getOrderNo());
                if (orderDO == null) {
                    throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
                }
                
                log.info("通过订单主表ID找到对应的个人培训订单详情，订单号：{}", orderDO.getOrderNo());
            }
            
            // 2. 逻辑删除个人培训订单详情
            personalTrainingMapper.deleteById(id);
            log.info("个人培训订单详情删除成功");
            
            // 3. 逻辑删除订单主表记录
            PublicbizOrderDO mainOrder = publicbizOrderMapper.selectByOrderNo(orderDO.getOrderNo());
            if (mainOrder != null) {
                publicbizOrderMapper.deleteById(mainOrder.getId());
                log.info("订单主表记录删除成功");
            }
            
            // 4. 逻辑删除相关记录
            // 删除订单日志记录
            List<PublicbizOrderLogDO> logs = publicbizOrderLogMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderLogDO>()
                    .eq(PublicbizOrderLogDO::getOrderNo, orderDO.getOrderNo())
            );
            for (PublicbizOrderLogDO log : logs) {
                publicbizOrderLogMapper.deleteById(log.getId());
            }
            
            // 删除合作伙伴合同记录
            List<PublicbizPartnerContractDO> contracts = publicbizPartnerContractMapper.selectList(
                new LambdaQueryWrapperX<PublicbizPartnerContractDO>()
                    .eq(PublicbizPartnerContractDO::getPartnerId, mainOrder != null ? mainOrder.getId() : 0L)
            );
            for (PublicbizPartnerContractDO contract : contracts) {
                publicbizPartnerContractMapper.deleteById(contract.getContractId());
            }
            
            // 删除订单支付记录
            List<PublicbizOrderPaymentDO> payments = publicbizOrderPaymentMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                    .eq(PublicbizOrderPaymentDO::getOrderId, mainOrder != null ? mainOrder.getId() : 0L)
            );
            for (PublicbizOrderPaymentDO payment : payments) {
                publicbizOrderPaymentMapper.deleteById(payment.getId());
            }
            
            log.info("相关记录删除成功");
            
            // 5. 记录删除日志
            recordOrderDeleteLog(new PersonalTrainingDeleteReqDTO(id, orderDO.getOrderNo(), orderDO.getStudentName(), orderDO.getCourseName(), mainOrder != null ? mainOrder.getOrderStatus() : "unknown"), 
                    orderDO, "已删除", getCurrentUserId(), "系统管理员", "系统管理员");
            log.info("删除日志记录成功");
            
            log.info("个人培训与认证订单删除成功，订单ID：{}", id);
            
        } catch (Exception e) {
            log.error("删除个人培训与认证订单失败，订单ID：{}，错误：{}", id, e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_DELETE_FAIL);
        }
    }

    @Override
    public PersonalTrainingRespDTO getPersonalTraining(Long id) {
        log.info("开始根据ID查询个人培训订单详情，订单ID：{}", id);
        
        try {
            // 1. 首先尝试根据ID查找个人培训订单详情
            PersonalTrainingDO personalTraining = personalTrainingMapper.selectById(id);
            
            // 如果找不到，尝试根据ID查找订单主表，然后查找对应的个人培训订单详情
            if (personalTraining == null) {
                log.info("未找到ID为{}的个人培训订单详情，尝试根据订单主表ID查找", id);
                
                // 根据ID查找订单主表
                PublicbizOrderDO mainOrder = publicbizOrderMapper.selectById(id);
                if (mainOrder == null) {
                    log.warn("未找到个人培训订单，订单ID：{}", id);
                    throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
                }
                
                // 根据订单号查找个人培训订单详情
                personalTraining = personalTrainingMapper.selectByOrderNo(mainOrder.getOrderNo());
                if (personalTraining == null) {
                    log.warn("未找到个人培训订单，订单ID：{}", id);
                    throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
                }
                
                log.info("通过订单主表ID找到对应的个人培训订单详情，订单号：{}", personalTraining.getOrderNo());
            }
            
            // 2. 查询订单主表信息
            PublicbizOrderDO order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
            if (order == null) {
                log.warn("未找到订单主表信息，订单ID：{}", personalTraining.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 3. 查询合作伙伴合同信息
            PublicbizPartnerContractDO contract = publicbizPartnerContractMapper.selectOne(
                new LambdaQueryWrapperX<PublicbizPartnerContractDO>()
                    .eq(PublicbizPartnerContractDO::getPartnerId, order.getId())
                    .eq(PublicbizPartnerContractDO::getDeleted, false)
            );
            
            // 4. 查询支付记录列表
            List<PublicbizOrderPaymentDO> paymentList = publicbizOrderPaymentMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                    .eq(PublicbizOrderPaymentDO::getOrderId, order.getId())
                    .eq(PublicbizOrderPaymentDO::getDeleted, false)
                    .orderByDesc(PublicbizOrderPaymentDO::getCreateTime)
            );
            
            // 5. 构建响应对象
            PersonalTrainingRespDTO respDTO = buildPersonalTrainingRespDTO(personalTraining, order, contract, paymentList);
            
            log.info("根据ID查询个人培训订单详情成功，订单ID：{}，学员姓名：{}", id, personalTraining.getStudentName());
            return respDTO;
            
        } catch (Exception e) {
            log.error("根据ID查询个人培训订单详情失败，订单ID：{}，错误：{}", id, e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
    }

    @Override
    public PersonalTrainingRespDTO getPersonalTrainingByOrderNo(String orderNo) {
        log.info("开始根据订单号查询个人培训订单详情，订单号：{}", orderNo);
        
        try {
            // 1. 根据订单号查询个人培训订单详情
            PersonalTrainingDO personalTraining = personalTrainingMapper.selectOne(
                new LambdaQueryWrapperX<PersonalTrainingDO>()
                    .eq(PersonalTrainingDO::getOrderNo, orderNo)
                    .eq(PersonalTrainingDO::getDeleted, false)
            );
            
            if (personalTraining == null) {
                log.warn("未找到个人培训订单，订单号：{}", orderNo);
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 2. 查询订单主表信息
            PublicbizOrderDO order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
            if (order == null) {
                log.warn("未找到订单主表信息，订单ID：{}", personalTraining.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 3. 查询合作伙伴合同信息
            PublicbizPartnerContractDO contract = publicbizPartnerContractMapper.selectOne(
                new LambdaQueryWrapperX<PublicbizPartnerContractDO>()
                    .eq(PublicbizPartnerContractDO::getPartnerId, order.getId())
                    .eq(PublicbizPartnerContractDO::getDeleted, false)
            );
            
            // 4. 查询支付记录列表
            List<PublicbizOrderPaymentDO> paymentList = publicbizOrderPaymentMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                    .eq(PublicbizOrderPaymentDO::getOrderId, order.getId())
                    .eq(PublicbizOrderPaymentDO::getDeleted, false)
                    .orderByDesc(PublicbizOrderPaymentDO::getCreateTime)
            );
            
            // 5. 构建响应对象
            PersonalTrainingRespDTO respDTO = buildPersonalTrainingRespDTO(personalTraining, order, contract, paymentList);
            
            log.info("根据订单号查询个人培训订单详情成功，订单号：{}，学员姓名：{}", orderNo, personalTraining.getStudentName());
            return respDTO;
            
        } catch (Exception e) {
            log.error("根据订单号查询个人培训订单详情失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
    }

    public PageResult<PersonalTrainingRespDTO> getPersonalTrainingPage(PersonalTrainingPageReqDTO pageReqDTO) {
        log.info("开始分页查询个人培训订单，查询条件：{}", pageReqDTO);
        
        try {
            // 构建查询条件
            LambdaQueryWrapperX<PersonalTrainingDO> queryWrapper = new LambdaQueryWrapperX<>();
            
            if (StrUtil.isNotBlank(pageReqDTO.getOrderNo())) {
                queryWrapper.eq(PersonalTrainingDO::getOrderNo, pageReqDTO.getOrderNo());
            }
            if (StrUtil.isNotBlank(pageReqDTO.getStudentName())) {
                queryWrapper.like(PersonalTrainingDO::getStudentName, pageReqDTO.getStudentName());
            }
            if (StrUtil.isNotBlank(pageReqDTO.getStudentOneid())) {
                queryWrapper.eq(PersonalTrainingDO::getStudentOneid, pageReqDTO.getStudentOneid());
            }
            if (StrUtil.isNotBlank(pageReqDTO.getStudentPhone())) {
                queryWrapper.eq(PersonalTrainingDO::getStudentPhone, pageReqDTO.getStudentPhone());
            }
            if (StrUtil.isNotBlank(pageReqDTO.getCourseName())) {
                queryWrapper.like(PersonalTrainingDO::getCourseName, pageReqDTO.getCourseName());
            }
            // 这些字段在订单主表中，需要特殊处理
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc(PersonalTrainingDO::getCreateTime);
            
            // 执行分页查询
            PageResult<PersonalTrainingDO> pageResult = personalTrainingMapper.selectPage(pageReqDTO, queryWrapper);
            
            // 转换为响应DTO，需要查询订单主表信息
            List<PersonalTrainingRespDTO> respList = pageResult.getList().stream()
                    .map(personalTraining -> {
                        try {
                            // 查询订单主表信息
                            PublicbizOrderDO order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
                            if (order == null) {
                                log.warn("分页查询中未找到订单主表信息，订单ID：{}", personalTraining.getOrderId());
                                return null;
                            }
                            
                            // 查询合作伙伴合同信息
                            PublicbizPartnerContractDO contract = publicbizPartnerContractMapper.selectOne(
                                new LambdaQueryWrapperX<PublicbizPartnerContractDO>()
                                    .eq(PublicbizPartnerContractDO::getPartnerId, order.getId())
                                    .eq(PublicbizPartnerContractDO::getDeleted, false)
                            );
                            
                            // 查询支付记录列表
                            List<PublicbizOrderPaymentDO> paymentList = publicbizOrderPaymentMapper.selectList(
                                new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                                    .eq(PublicbizOrderPaymentDO::getOrderId, order.getId())
                                    .eq(PublicbizOrderPaymentDO::getDeleted, false)
                                    .orderByDesc(PublicbizOrderPaymentDO::getCreateTime)
                            );
                            
                            return buildPersonalTrainingRespDTO(personalTraining, order, contract, paymentList);
                        } catch (Exception e) {
                            log.error("分页查询中转换订单数据失败，订单ID：{}，错误：{}", personalTraining.getId(), e.getMessage());
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            PageResult<PersonalTrainingRespDTO> respPageResult = new PageResult<>();
            respPageResult.setList(respList);
            respPageResult.setTotal(pageResult.getTotal());
            
            log.info("分页查询个人培训订单成功，总记录数：{}", respPageResult.getTotal());
            return respPageResult;
            
        } catch (Exception e) {
            log.error("分页查询个人培训订单失败，错误：{}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
    }

    // ========== 收款相关方法实现 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PersonalTrainingCollectionRespDTO confirmCollection(PersonalTrainingCollectionReqDTO reqDTO) {
        log.info("开始确认收款，订单ID：{}，订单号：{}，收款金额：{}", 
                reqDTO.getOrderId(), reqDTO.getOrderNo(), reqDTO.getCollectionAmount());
        
        try {
            // 1. 先查询个人培训订单详情
            PersonalTrainingDO personalTraining = personalTrainingMapper.selectById(reqDTO.getOrderId());
            if (personalTraining == null) {
                log.error("确认收款失败：个人培训订单不存在，订单ID：{}", reqDTO.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 2. 通过个人培训订单的orderId查询主订单
            PublicbizOrderDO order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
            if (order == null) {
                log.error("确认收款失败：主订单不存在，个人培训订单ID：{}，主订单ID：{}", 
                        reqDTO.getOrderId(), personalTraining.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("订单状态检查：个人培训订单ID：{}，主订单ID：{}，订单状态：{}，支付状态：{}", 
                    reqDTO.getOrderId(), order.getId(), order.getOrderStatus(), order.getPaymentStatus());
            
            // 3. 验证订单状态是否允许收款
            // 允许在以下状态下确认收款：
            // - draft: 草稿状态（订单创建后直接收款）
            // - approved: 已批准状态（审批通过后收款）
            // - pending_payment: 待支付状态（等待收款）
            if (!"draft".equals(order.getOrderStatus()) && 
                !"approved".equals(order.getOrderStatus()) && 
                !"pending_payment".equals(order.getOrderStatus())) {
                log.error("确认收款失败：订单状态不允许收款，订单ID：{}，当前状态：{}，期望状态：draft、approved或pending_payment", 
                        reqDTO.getOrderId(), order.getOrderStatus());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_STATUS_INVALID);
            }
            
            // 4. 验证支付状态（如果已支付，则查询现有支付记录）
            if ("paid".equals(order.getPaymentStatus())) {
                log.info("订单已支付，查询现有支付记录，订单ID：{}，当前支付状态：{}", 
                        reqDTO.getOrderId(), order.getPaymentStatus());
                
                // 查询现有支付记录
                List<PublicbizOrderPaymentDO> existingPayments = publicbizOrderPaymentMapper.selectList(
                    new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                        .eq(PublicbizOrderPaymentDO::getOrderId, reqDTO.getOrderId())
                );
                
                if (!existingPayments.isEmpty()) {
                    // 如果存在支付记录，直接返回
                    PublicbizOrderPaymentDO existingPayment = existingPayments.get(0);
                    log.info("找到现有支付记录，支付记录ID：{}，订单ID：{}", existingPayment.getId(), reqDTO.getOrderId());
                    
                    // 构建响应对象
                    PersonalTrainingCollectionRespDTO respDTO = buildCollectionRespDTO(existingPayment, reqDTO);
                    log.info("订单已支付，返回现有支付记录，订单ID：{}，支付记录ID：{}", reqDTO.getOrderId(), existingPayment.getId());
                    return respDTO;
                } else {
                    // 如果支付状态为paid但没有支付记录，记录警告并继续处理
                    log.warn("订单支付状态为paid但未找到支付记录，继续创建新的支付记录，订单ID：{}", reqDTO.getOrderId());
                }
            }
            
            // 5. 验证收款金额
            if (reqDTO.getCollectionAmount() == null || reqDTO.getCollectionAmount().compareTo(BigDecimal.ZERO) <= 0) {
                log.error("确认收款失败：收款金额无效，订单ID：{}，收款金额：{}", 
                        reqDTO.getOrderId(), reqDTO.getCollectionAmount());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_PAYMENT_STATUS_INVALID);
            }
            
            if (order.getTotalAmount() != null && reqDTO.getCollectionAmount().compareTo(order.getTotalAmount()) > 0) {
                log.error("确认收款失败：收款金额超过订单总金额，订单ID：{}，收款金额：{}，总金额：{}", 
                        reqDTO.getOrderId(), reqDTO.getCollectionAmount(), order.getTotalAmount());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_PAYMENT_STATUS_INVALID);
            }
            
            // 6. 创建支付记录
            PublicbizOrderPaymentDO paymentDO = createPaymentRecordForCollection(reqDTO);
            publicbizOrderPaymentMapper.insert(paymentDO);
            log.info("支付记录创建成功，支付记录ID：{}", paymentDO.getId());
            
            // 7. 更新订单状态
            updateOrderStatusForCollection(order, reqDTO);
            publicbizOrderMapper.updateById(order);
            log.info("订单状态更新成功，订单ID：{}", reqDTO.getOrderId());
            
            // 8. 记录收款确认日志
            recordCollectionLog(reqDTO, order, "executing", reqDTO.getCollectionMethod(), 
                    getCurrentUserId(), "系统管理员", "收款员");
            log.info("收款确认日志记录完成");
            
            // 9. 构建响应对象
            PersonalTrainingCollectionRespDTO respDTO = buildCollectionRespDTO(paymentDO, reqDTO);
            
            log.info("确认收款成功，订单ID：{}，支付记录ID：{}", reqDTO.getOrderId(), paymentDO.getId());
            return respDTO;
            
        } catch (Exception e) {
            log.error("确认收款失败，订单ID：{}，错误：{}", reqDTO.getOrderId(), e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_COLLECTION_FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PersonalTrainingCollectionRespDTO updateCollection(PersonalTrainingCollectionReqDTO reqDTO) {
        log.info("开始更新收款信息，订单ID：{}，订单号：{}，收款金额：{}", 
                reqDTO.getOrderId(), reqDTO.getOrderNo(), reqDTO.getCollectionAmount());
        
        try {
            // 1. 先查询个人培训订单详情
            PersonalTrainingDO personalTraining = personalTrainingMapper.selectById(reqDTO.getOrderId());
            if (personalTraining == null) {
                log.error("更新收款失败：个人培训订单不存在，订单ID：{}", reqDTO.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 2. 通过个人培训订单的orderId查询主订单
            PublicbizOrderDO order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
            if (order == null) {
                log.error("更新收款失败：主订单不存在，个人培训订单ID：{}，主订单ID：{}", 
                        reqDTO.getOrderId(), personalTraining.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 3. 验证订单状态和支付状态
            // 允许在以下状态下更新收款信息：
            // - 已支付状态：paid
            // - 草稿状态：draft（订单创建后直接收款）
            // - 已批准状态：approved（审批通过后收款）
            // - 待支付状态：pending_payment（等待收款）
            if (!"paid".equals(order.getPaymentStatus()) && 
                !"draft".equals(order.getOrderStatus()) && 
                !"approved".equals(order.getOrderStatus()) && 
                !"pending_payment".equals(order.getOrderStatus())) {
                log.error("更新收款失败：订单状态不允许更新收款，订单ID：{}，当前订单状态：{}，支付状态：{}", 
                        reqDTO.getOrderId(), order.getOrderStatus(), order.getPaymentStatus());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_PAYMENT_STATUS_INVALID);
            }
            
            // 4. 查询现有支付记录
            List<PublicbizOrderPaymentDO> existingPayments = publicbizOrderPaymentMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                    .eq(PublicbizOrderPaymentDO::getOrderId, reqDTO.getOrderId())
            );
            
            if (existingPayments.isEmpty()) {
                log.error("更新收款失败：支付记录不存在，订单ID：{}", reqDTO.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 5. 更新支付记录
            PublicbizOrderPaymentDO paymentDO = existingPayments.get(0);
            updatePaymentRecordForCollection(paymentDO, reqDTO);
            publicbizOrderPaymentMapper.updateById(paymentDO);
            log.info("支付记录更新成功，支付记录ID：{}", paymentDO.getId());
            
            // 6. 更新订单状态
            updateOrderStatusForCollectionUpdate(order, reqDTO);
            publicbizOrderMapper.updateById(order);
            log.info("订单状态更新成功，订单ID：{}", reqDTO.getOrderId());
            
            // 7. 记录收款更新日志
            recordCollectionUpdateLog(reqDTO, order, "executing", reqDTO.getCollectionMethod(), 
                    getCurrentUserId(), "系统管理员", "收款员");
            log.info("收款更新日志记录完成");
            
            // 8. 构建响应对象
            PersonalTrainingCollectionRespDTO respDTO = buildCollectionRespDTO(paymentDO, reqDTO);
            
            log.info("更新收款信息成功，订单ID：{}，支付记录ID：{}", reqDTO.getOrderId(), paymentDO.getId());
            return respDTO;
            
        } catch (Exception e) {
            log.error("更新收款信息失败，订单ID：{}，错误：{}", reqDTO.getOrderId(), e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_COLLECTION_FAIL);
        }
    }

    @Override
    public PageResult<PersonalTrainingCollectionRespDTO> getCollectionPage(PersonalTrainingCollectionPageReqDTO pageReqDTO) {
        log.info("开始分页查询收款记录，查询条件：{}", pageReqDTO);
        
        try {
            // 构建查询条件
            LambdaQueryWrapperX<PublicbizOrderPaymentDO> queryWrapper = new LambdaQueryWrapperX<>();
            
            if (pageReqDTO.getOrderId() != null) {
                queryWrapper.eq(PublicbizOrderPaymentDO::getOrderId, pageReqDTO.getOrderId());
            }
            if (StrUtil.isNotBlank(pageReqDTO.getOrderNo())) {
                queryWrapper.eq(PublicbizOrderPaymentDO::getOrderNo, pageReqDTO.getOrderNo());
            }
            if (StrUtil.isNotBlank(pageReqDTO.getPaymentType())) {
                queryWrapper.eq(PublicbizOrderPaymentDO::getPaymentType, pageReqDTO.getPaymentType());
            }
            if (StrUtil.isNotBlank(pageReqDTO.getPaymentStatus())) {
                queryWrapper.eq(PublicbizOrderPaymentDO::getPaymentStatus, pageReqDTO.getPaymentStatus());
            }
            if (StrUtil.isNotBlank(pageReqDTO.getOperatorName())) {
                queryWrapper.like(PublicbizOrderPaymentDO::getOperatorName, pageReqDTO.getOperatorName());
            }
            if (pageReqDTO.getStartDate() != null) {
                queryWrapper.ge(PublicbizOrderPaymentDO::getPaymentTime, pageReqDTO.getStartDate().atStartOfDay());
            }
            if (pageReqDTO.getEndDate() != null) {
                queryWrapper.le(PublicbizOrderPaymentDO::getPaymentTime, pageReqDTO.getEndDate().atTime(23, 59, 59));
            }
            if (StrUtil.isNotBlank(pageReqDTO.getTransactionId())) {
                queryWrapper.eq(PublicbizOrderPaymentDO::getTransactionId, pageReqDTO.getTransactionId());
            }
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc(PublicbizOrderPaymentDO::getCreateTime);
            
            // 执行分页查询
            PageResult<PublicbizOrderPaymentDO> pageResult = publicbizOrderPaymentMapper.selectPage(pageReqDTO, queryWrapper);
            
            // 转换为响应DTO
            List<PersonalTrainingCollectionRespDTO> respList = pageResult.getList().stream()
                    .map(this::convertToCollectionRespDTO)
                    .collect(Collectors.toList());
            
            PageResult<PersonalTrainingCollectionRespDTO> respPageResult = new PageResult<>();
            respPageResult.setList(respList);
            respPageResult.setTotal(pageResult.getTotal());
            
            log.info("分页查询收款记录成功，总记录数：{}", respPageResult.getTotal());
            return respPageResult;
            
        } catch (Exception e) {
            log.error("分页查询收款记录失败，错误：{}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
    }

    @Override
    public List<PersonalTrainingCollectionRespDTO> getCollectionListByOrderId(Long orderId) {
        log.info("开始查询订单收款记录，订单ID：{}", orderId);
        
        try {
            List<PublicbizOrderPaymentDO> payments = publicbizOrderPaymentMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                    .eq(PublicbizOrderPaymentDO::getOrderId, orderId)
                    .orderByDesc(PublicbizOrderPaymentDO::getCreateTime)
            );
            
            List<PersonalTrainingCollectionRespDTO> respList = payments.stream()
                    .map(this::convertToCollectionRespDTO)
                    .collect(Collectors.toList());
            
            log.info("查询订单收款记录成功，订单ID：{}，记录数：{}", orderId, respList.size());
            return respList;
            
        } catch (Exception e) {
            log.error("查询订单收款记录失败，订单ID：{}，错误：{}", orderId, e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
    }

    @Override
    public List<PersonalTrainingCollectionRespDTO> getCollectionListByOrderNo(String orderNo) {
        log.info("开始查询订单收款记录，订单号：{}", orderNo);
        
        try {
            List<PublicbizOrderPaymentDO> payments = publicbizOrderPaymentMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                    .eq(PublicbizOrderPaymentDO::getOrderNo, orderNo)
                    .orderByDesc(PublicbizOrderPaymentDO::getCreateTime)
            );
            
            List<PersonalTrainingCollectionRespDTO> respList = payments.stream()
                    .map(this::convertToCollectionRespDTO)
                    .collect(Collectors.toList());
            
            log.info("查询订单收款记录成功，订单号：{}，记录数：{}", orderNo, respList.size());
            return respList;
            
        } catch (Exception e) {
            log.error("查询订单收款记录失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
    }

    // ========== 收款相关私有方法 ==========

    /**
     * 创建收款支付记录
     */
    private PublicbizOrderPaymentDO createPaymentRecordForCollection(PersonalTrainingCollectionReqDTO reqDTO) {
        PublicbizOrderPaymentDO paymentRecord = PublicbizOrderPaymentDO.builder()
                .orderId(reqDTO.getOrderId())
                .orderNo(reqDTO.getOrderNo())
                .paymentNo(generatePaymentNo())
                .paymentType(reqDTO.getCollectionMethod())
                .paymentAmount(reqDTO.getCollectionAmount())
                .paymentStatus("success")
                .paymentTime(LocalDateTime.now())
                .operatorId(null) // TODO: 从安全上下文获取
                .operatorName(reqDTO.getOperatorName())
                .paymentRemark(reqDTO.getCollectionRemark())
                .transactionId(reqDTO.getTransactionId())
                .build();
        
        // 手动设置deleted字段
        paymentRecord.setDeleted(false);
        return paymentRecord;
    }

    /**
     * 更新订单状态为已收款
     */
    private void updateOrderStatusForCollection(PublicbizOrderDO order, PersonalTrainingCollectionReqDTO reqDTO) {
        order.setPaymentStatus("paid");
        order.setPaidAmount(reqDTO.getCollectionAmount());
        order.setSettlementTime(LocalDateTime.now());
        order.setSettlementMethod(reqDTO.getCollectionMethod());
        order.setRemark(reqDTO.getCollectionRemark());
        order.setUpdateTime(LocalDateTime.now());
        
        // 如果订单状态为已批准，则更新为执行中
        if ("approved".equals(order.getOrderStatus())) {
            order.setOrderStatus("executing");
        }
    }

    /**
     * 更新订单状态（收款信息更新）
     */
    private void updateOrderStatusForCollectionUpdate(PublicbizOrderDO order, PersonalTrainingCollectionReqDTO reqDTO) {
        order.setPaidAmount(reqDTO.getCollectionAmount());
        order.setSettlementMethod(reqDTO.getCollectionMethod());
        order.setRemark(reqDTO.getCollectionRemark());
        order.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 更新支付记录（收款信息更新）
     */
    private void updatePaymentRecordForCollection(PublicbizOrderPaymentDO paymentDO, PersonalTrainingCollectionReqDTO reqDTO) {
        paymentDO.setPaymentType(reqDTO.getCollectionMethod());
        paymentDO.setPaymentAmount(reqDTO.getCollectionAmount());
        paymentDO.setPaymentTime(LocalDateTime.now());
        paymentDO.setOperatorName(reqDTO.getOperatorName());
        paymentDO.setPaymentRemark(reqDTO.getCollectionRemark());
        paymentDO.setTransactionId(reqDTO.getTransactionId());
        // paymentDO.setBankAccount(reqDTO.getBankAccount());
        // paymentDO.setBankName(reqDTO.getBankName());
        paymentDO.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 构建收款更新变更列表
     */
    private List<OrderLogService.FieldChange> buildCollectionUpdateChanges(PersonalTrainingCollectionReqDTO reqDTO, 
                                                                         PublicbizOrderPaymentDO oldPayment) {
        List<OrderLogService.FieldChange> changes = new ArrayList<>();
        
        if (!reqDTO.getCollectionAmount().equals(oldPayment.getPaymentAmount())) {
            changes.add(new OrderLogService.FieldChange("收款金额", 
                    oldPayment.getPaymentAmount().toString(), reqDTO.getCollectionAmount().toString()));
        }
        if (!reqDTO.getCollectionMethod().equals(oldPayment.getPaymentType())) {
            changes.add(new OrderLogService.FieldChange("收款方式", 
                    getPaymentMethodName(oldPayment.getPaymentType()), getPaymentMethodName(reqDTO.getCollectionMethod())));
        }
        if (!Objects.equals(reqDTO.getCollectionRemark(), oldPayment.getPaymentRemark())) {
            changes.add(new OrderLogService.FieldChange("收款备注", 
                    oldPayment.getPaymentRemark(), reqDTO.getCollectionRemark()));
        }
        if (!Objects.equals(reqDTO.getTransactionId(), oldPayment.getTransactionId())) {
            changes.add(new OrderLogService.FieldChange("第三方交易号", 
                    oldPayment.getTransactionId(), reqDTO.getTransactionId()));
        }
        
        return changes;
    }

    /**
     * 构建收款响应DTO
     */
    private PersonalTrainingCollectionRespDTO buildCollectionRespDTO(PublicbizOrderPaymentDO paymentDO, 
                                                                   PersonalTrainingCollectionReqDTO reqDTO) {
        PersonalTrainingCollectionRespDTO respDTO = new PersonalTrainingCollectionRespDTO();
        respDTO.setPaymentId(paymentDO.getId());
        respDTO.setOrderId(paymentDO.getOrderId());
        respDTO.setOrderNo(paymentDO.getOrderNo());
        respDTO.setPaymentNo(paymentDO.getPaymentNo());
        respDTO.setPaymentType(paymentDO.getPaymentType());
        respDTO.setPaymentTypeName(getPaymentMethodName(paymentDO.getPaymentType()));
        respDTO.setPaymentAmount(paymentDO.getPaymentAmount());
        respDTO.setPaymentStatus(paymentDO.getPaymentStatus());
        respDTO.setPaymentStatusName(getPaymentStatusName(paymentDO.getPaymentStatus()));
        respDTO.setPaymentTime(paymentDO.getPaymentTime());
        respDTO.setOperatorId(paymentDO.getOperatorId());
        respDTO.setOperatorName(paymentDO.getOperatorName());
        respDTO.setPaymentRemark(paymentDO.getPaymentRemark());
        respDTO.setTransactionId(paymentDO.getTransactionId());
        // respDTO.setBankAccount(paymentDO.getBankAccount());
        // respDTO.setBankName(paymentDO.getBankName());
        respDTO.setCreateTime(paymentDO.getCreateTime());
        respDTO.setUpdateTime(paymentDO.getUpdateTime());
        return respDTO;
    }

    /**
     * 转换为收款响应DTO
     */
    private PersonalTrainingCollectionRespDTO convertToCollectionRespDTO(PublicbizOrderPaymentDO paymentDO) {
        PersonalTrainingCollectionRespDTO respDTO = new PersonalTrainingCollectionRespDTO();
        respDTO.setPaymentId(paymentDO.getId());
        respDTO.setOrderId(paymentDO.getOrderId());
        respDTO.setOrderNo(paymentDO.getOrderNo());
        respDTO.setPaymentNo(paymentDO.getPaymentNo());
        respDTO.setPaymentType(paymentDO.getPaymentType());
        respDTO.setPaymentTypeName(getPaymentMethodName(paymentDO.getPaymentType()));
        respDTO.setPaymentAmount(paymentDO.getPaymentAmount());
        respDTO.setPaymentStatus(paymentDO.getPaymentStatus());
        respDTO.setPaymentStatusName(getPaymentStatusName(paymentDO.getPaymentStatus()));
        respDTO.setPaymentTime(paymentDO.getPaymentTime());
        respDTO.setOperatorId(paymentDO.getOperatorId());
        respDTO.setOperatorName(paymentDO.getOperatorName());
        respDTO.setPaymentRemark(paymentDO.getPaymentRemark());
        respDTO.setTransactionId(paymentDO.getTransactionId());
        // respDTO.setBankAccount(paymentDO.getBankAccount());
        // respDTO.setBankName(paymentDO.getBankName());
        respDTO.setCreateTime(paymentDO.getCreateTime());
        respDTO.setUpdateTime(paymentDO.getUpdateTime());
        return respDTO;
    }

    /**
     * 生成支付单号
     */
    private String generatePaymentNo() {
        return "PAY" + System.currentTimeMillis();
    }

    /**
     * 获取支付方式名称
     */
    private String getPaymentMethodName(String paymentMethod) {
        switch (paymentMethod) {
            case "cash":
                return "现金";
            case "wechat":
                return "微信支付";
            case "alipay":
                return "支付宝";
            case "bank_transfer":
                return "银行转账";
            case "pos":
                return "POS机刷卡";
            case "other":
                return "其他";
            default:
                return paymentMethod;
        }
    }

    /**
     * 获取支付状态名称
     */
    private String getPaymentStatusName(String paymentStatus) {
        switch (paymentStatus) {
            case "pending":
                return "待支付";
            case "success":
                return "支付成功";
            case "failed":
                return "支付失败";
            case "refunded":
                return "已退款";
            case "cancelled":
                return "已取消";
            default:
                return paymentStatus;
        }
    }

    /**
     * 转换为个人培训订单响应DTO
     */
    private PersonalTrainingRespDTO convertToPersonalTrainingRespDTO(PersonalTrainingDO personalTraining, PublicbizOrderDO order) {
        if (order == null) {
            // 如果没有传入订单主表信息，则查询
            order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
            if (order == null) {
                log.warn("订单主表信息不存在，订单ID：{}，个人培训订单ID：{}", personalTraining.getOrderId(), personalTraining.getId());
                return null;
            }
        }
        
        try {
            // 构建响应对象
            PersonalTrainingRespDTO respDTO = new PersonalTrainingRespDTO();
            respDTO.setId(personalTraining.getId());
            respDTO.setOrderId(personalTraining.getOrderId());
            respDTO.setOrderNo(personalTraining.getOrderNo());
            respDTO.setStudentName(personalTraining.getStudentName());
            respDTO.setStudentOneid(personalTraining.getStudentOneid());
            respDTO.setStudentPhone(personalTraining.getStudentPhone());
            respDTO.setStudentEmail(personalTraining.getStudentEmail());
            respDTO.setCourseName(personalTraining.getCourseName());
            respDTO.setCourseType(personalTraining.getCourseType());
            
            // 从订单主表获取信息，增加空值检查
            if (order != null) {
                respDTO.setOrderType(order.getOrderType());
                respDTO.setCourseDescription(order.getProjectDescription());
                respDTO.setOrderAmount(order.getTotalAmount());
                respDTO.setOrderStatus(order.getOrderStatus());
                respDTO.setPaymentStatus(order.getPaymentStatus());
                respDTO.setContractType(order.getContractType());
                // 由于审批表不存在，设置默认审批级别
                respDTO.setApprovalLevel("0");
            } else {
                // 如果订单主表信息为空，设置默认值
                respDTO.setOrderType("personal-training");
                respDTO.setCourseDescription("");
                respDTO.setOrderAmount(BigDecimal.ZERO);
                respDTO.setOrderStatus("unknown");
                respDTO.setPaymentStatus("unknown");
                respDTO.setContractType("");
                respDTO.setApprovalLevel("0");
            }
            
            respDTO.setCreateTime(personalTraining.getCreateTime());
            respDTO.setUpdateTime(personalTraining.getUpdateTime());
            
            return respDTO;
        } catch (Exception e) {
            log.error("转换个人培训订单响应DTO失败，个人培训订单ID：{}，错误：{}", personalTraining.getId(), e.getMessage(), e);
            return null;
        }
    }

    // ========== 审批管理方法实现 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PersonalTrainingApprovalRespDTO submitApproval(PersonalTrainingApprovalReqDTO reqDTO) {
        log.info("提交审批，订单ID：{}，订单号：{}", reqDTO.getOrderId(), reqDTO.getOrderNo());
        
        // 1. 尝试查询主订单表（假设前端传递的是主订单ID）
        PublicbizOrderDO order = publicbizOrderMapper.selectById(reqDTO.getOrderId());
        PersonalTrainingDO personalTraining = null;
        
        if (order != null) {
            // 如果找到主订单，则查询个人培训订单详情
            log.info("找到主订单，订单ID：{}，订单类型：{}，开始查询个人培训订单详情", order.getId(), order.getOrderType());
            
            LambdaQueryWrapper<PersonalTrainingDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PersonalTrainingDO::getOrderId, order.getId()); // 通过order_id关联查询
            personalTraining = personalTrainingMapper.selectOne(wrapper);
            
            if (personalTraining == null) {
                log.error("提交审批失败：找不到对应的个人培训订单详情，主订单ID：{}，订单号：{}", order.getId(), order.getOrderNo());
                
                // 尝试查询所有个人培训订单，看看是否有数据不一致的问题
                List<PersonalTrainingDO> allPersonalOrders = personalTrainingMapper.selectList(new LambdaQueryWrapper<>());
                log.error("数据库中所有个人培训订单数量：{}", allPersonalOrders.size());
                
                // 检查是否有order_id为当前订单ID的记录
                final Long orderId = order.getId();
                List<PersonalTrainingDO> matchingOrders = allPersonalOrders.stream()
                    .filter(po -> orderId.equals(po.getOrderId()))
                    .collect(Collectors.toList());
                log.error("order_id为{}的个人培训订单数量：{}", orderId, matchingOrders.size());
                
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("成功找到个人培训订单详情，个人培训订单ID：{}，订单号：{}", personalTraining.getId(), personalTraining.getOrderNo());
        } else {
            // 如果没找到主订单，尝试查询个人培训订单详情表
            log.info("未找到主订单，尝试直接查询个人培训订单详情表，订单ID：{}", reqDTO.getOrderId());
            
            personalTraining = personalTrainingMapper.selectById(reqDTO.getOrderId());
            if (personalTraining == null) {
                log.error("提交审批失败：个人培训订单不存在，订单ID：{}", reqDTO.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 通过个人培训订单的orderId查询主订单
            order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
            if (order == null) {
                log.error("提交审批失败：主订单不存在，个人培训订单ID：{}，主订单ID：{}", 
                        reqDTO.getOrderId(), personalTraining.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("通过个人培训订单找到主订单，主订单ID：{}，订单类型：{}", order.getId(), order.getOrderType());
        }
        
        // 2. 验证订单号一致性
        if (reqDTO.getOrderNo() != null && !reqDTO.getOrderNo().equals(personalTraining.getOrderNo())) {
            log.error("提交审批失败：订单号不匹配，请求订单号：{}，数据库订单号：{}", 
                    reqDTO.getOrderNo(), personalTraining.getOrderNo());
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
        
        // 检查订单状态是否允许提交审批
        if (!"draft".equals(order.getOrderStatus()) && !"pending_approval".equals(order.getOrderStatus())) {
            log.error("提交审批失败：订单状态不允许提交审批，当前状态：{}", order.getOrderStatus());
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_STATUS_INVALID);
        }
        
        // 3. 创建审批记录（注释掉，因为审批表不存在）
        // PersonalTrainingApprovalDO approvalDO = PersonalTrainingApprovalDO.builder()
        //         .approvalNo("AP" + System.currentTimeMillis())
        //         .orderId(personalTraining.getId())
        //         .orderNo(personalTraining.getOrderNo())
        //         .approvalType("order_approval")
        //         .approvalLevel(1)
        //         .approvalStatus("pending")
        //         .approvalResult("pending")
        //         .currentLevel(1)
        //         .creatorId(getCurrentUserId())
        //         .creatorName("系统管理员")
        //         .build();
        
        // 手动设置deleted字段
        // approvalDO.setDeleted(false);
        
        // personalTrainingApprovalMapper.insert(approvalDO);
        
        // 4. 更新订单状态为待审批
        String oldStatus = order.getOrderStatus();
        order.setOrderStatus("pending_approval");
        publicbizOrderMapper.updateById(order);
        
        // 5. 记录操作日志
        String logContent = String.format("订单【%s】提交审批，审批意见：%s", 
                personalTraining.getOrderNo(), 
                reqDTO.getApprovalOpinion() != null ? reqDTO.getApprovalOpinion() : "无");
        
        // 记录操作日志到系统日志表
        PublicbizOrderLogDO logDO = new PublicbizOrderLogDO();
        logDO.setOrderNo(personalTraining.getOrderNo());
        logDO.setLogType("提交审批");
        logDO.setLogTitle("提交审批");
        logDO.setLogContent(logContent);
        logDO.setOldStatus(oldStatus);
        logDO.setNewStatus("pending_approval");
        logDO.setOperatorId(getCurrentUserId());
        logDO.setOperatorName(reqDTO.getOperatorName() != null ? reqDTO.getOperatorName() : "系统管理员");
        logDO.setOperatorRole("申请人");
        logDO.setCreateTime(LocalDateTime.now());
        logDO.setUpdateTime(LocalDateTime.now());
        publicbizOrderLogMapper.insert(logDO);
        
        // 6. 构建响应对象
        PersonalTrainingApprovalRespDTO respDTO = new PersonalTrainingApprovalRespDTO();
        // respDTO.setApprovalId(approvalDO.getId());
        // respDTO.setApprovalNo(approvalDO.getApprovalNo());
        respDTO.setOrderId(personalTraining.getId());
        // respDTO.setApprovalType(approvalDO.getApprovalType());
        // respDTO.setApprovalLevel(approvalDO.getApprovalLevel());
        // respDTO.setApprovalResult(approvalDO.getApprovalResult());
        // respDTO.setCreateTime(approvalDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        log.info("审批提交成功，订单ID：{}，订单号：{}", personalTraining.getId(), personalTraining.getOrderNo());
        return respDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approve(PersonalTrainingApprovalReqDTO reqDTO) {
        log.info("审批操作，订单ID：{}，订单号：{}", reqDTO.getOrderId(), reqDTO.getOrderNo());
        
        // 1. 尝试查询主订单表（假设前端传递的是主订单ID）
        PublicbizOrderDO order = publicbizOrderMapper.selectById(reqDTO.getOrderId());
        PersonalTrainingDO personalTraining = null;
        
        if (order != null) {
            // 如果找到主订单，则查询个人培训订单详情
            log.info("找到主订单，订单ID：{}，订单类型：{}，开始查询个人培训订单详情", order.getId(), order.getOrderType());
            
            LambdaQueryWrapper<PersonalTrainingDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PersonalTrainingDO::getOrderId, order.getId()); // 通过order_id关联查询
            personalTraining = personalTrainingMapper.selectOne(wrapper);
            
            if (personalTraining == null) {
                log.error("审批失败：找不到对应的个人培训订单详情，主订单ID：{}，订单号：{}", order.getId(), order.getOrderNo());
                
                // 尝试查询所有个人培训订单，看看是否有数据不一致的问题
                List<PersonalTrainingDO> allPersonalOrders = personalTrainingMapper.selectList(new LambdaQueryWrapper<>());
                log.error("数据库中所有个人培训订单数量：{}", allPersonalOrders.size());
                
                // 检查是否有order_id为当前订单ID的记录
                final Long orderId = order.getId();
                List<PersonalTrainingDO> matchingOrders = allPersonalOrders.stream()
                    .filter(po -> orderId.equals(po.getOrderId()))
                    .collect(Collectors.toList());
                log.error("order_id为{}的个人培训订单数量：{}", orderId, matchingOrders.size());
                
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("成功找到个人培训订单详情，个人培训订单ID：{}，订单号：{}", personalTraining.getId(), personalTraining.getOrderNo());
        } else {
            // 如果没找到主订单，尝试查询个人培训订单详情表
            log.info("未找到主订单，尝试直接查询个人培训订单详情表，订单ID：{}", reqDTO.getOrderId());
            
            personalTraining = personalTrainingMapper.selectById(reqDTO.getOrderId());
            if (personalTraining == null) {
                log.error("审批失败：个人培训订单不存在，订单ID：{}", reqDTO.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 通过个人培训订单的orderId查询主订单
            order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
            if (order == null) {
                log.error("审批失败：主订单不存在，个人培训订单ID：{}，主订单ID：{}", 
                        reqDTO.getOrderId(), personalTraining.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("通过个人培训订单找到主订单，主订单ID：{}，订单类型：{}", order.getId(), order.getOrderType());
        }
        
        // 2. 验证订单号一致性
        if (reqDTO.getOrderNo() != null && !reqDTO.getOrderNo().equals(personalTraining.getOrderNo())) {
            log.error("审批失败：订单号不匹配，请求订单号：{}，数据库订单号：{}", 
                    reqDTO.getOrderNo(), personalTraining.getOrderNo());
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
        
        if ("approved".equals(reqDTO.getApprovalResult())) {
            // 简化处理：直接设置为已批准
            order.setOrderStatus("approved");
            log.info("订单审批通过，订单状态更新为已批准");
        } else {
            // 审批拒绝，订单状态变为已拒绝
            order.setOrderStatus("rejected");
            log.info("订单审批被拒绝，订单状态更新为已拒绝");
        }
        
        publicbizOrderMapper.updateById(order);
        
        // 3. 记录操作日志
        String oldStatus = "pending_approval"; // 假设之前的状态是待审批
        String newStatus = order.getOrderStatus();
        String logContent = String.format("订单【%s】审批%s，审批意见：%s", 
                personalTraining.getOrderNo(), 
                "approved".equals(reqDTO.getApprovalResult()) ? "通过" : "拒绝",
                reqDTO.getApprovalOpinion() != null ? reqDTO.getApprovalOpinion() : "无");
        
        // 记录操作日志到系统日志表
        PublicbizOrderLogDO logDO = new PublicbizOrderLogDO();
        logDO.setOrderNo(personalTraining.getOrderNo());
        logDO.setLogType("审批处理");
        logDO.setLogTitle("审批处理");
        logDO.setLogContent(logContent);
        logDO.setOldStatus(oldStatus);
        logDO.setNewStatus(newStatus);
        logDO.setOperatorId(getCurrentUserId());
        logDO.setOperatorName(reqDTO.getOperatorName() != null ? reqDTO.getOperatorName() : "系统管理员");
        logDO.setOperatorRole("审批员");
        logDO.setCreateTime(LocalDateTime.now());
        logDO.setUpdateTime(LocalDateTime.now());
        publicbizOrderLogMapper.insert(logDO);
        
        log.info("审批操作完成，审批结果：{}，操作日志已记录", reqDTO.getApprovalResult());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reject(PersonalTrainingApprovalReqDTO reqDTO) {
        log.info("审批拒绝操作，订单ID：{}，订单号：{}", reqDTO.getOrderId(), reqDTO.getOrderNo());
        
        // 1. 尝试查询主订单表（假设前端传递的是主订单ID）
        PublicbizOrderDO order = publicbizOrderMapper.selectById(reqDTO.getOrderId());
        PersonalTrainingDO personalTraining = null;
        
        if (order != null) {
            // 如果找到主订单，则查询个人培训订单详情
            log.info("找到主订单，订单ID：{}，订单类型：{}，开始查询个人培训订单详情", order.getId(), order.getOrderType());
            
            LambdaQueryWrapper<PersonalTrainingDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PersonalTrainingDO::getOrderId, order.getId()); // 通过order_id关联查询
            personalTraining = personalTrainingMapper.selectOne(wrapper);
            
            if (personalTraining == null) {
                log.error("审批拒绝失败：找不到对应的个人培训订单详情，主订单ID：{}，订单号：{}", order.getId(), order.getOrderNo());
                
                // 尝试查询所有个人培训订单，看看是否有数据不一致的问题
                List<PersonalTrainingDO> allPersonalOrders = personalTrainingMapper.selectList(new LambdaQueryWrapper<>());
                log.error("数据库中所有个人培训订单数量：{}", allPersonalOrders.size());
                
                // 检查是否有order_id为当前订单ID的记录
                final Long orderId = order.getId();
                List<PersonalTrainingDO> matchingOrders = allPersonalOrders.stream()
                    .filter(po -> orderId.equals(po.getOrderId()))
                    .collect(Collectors.toList());
                log.error("order_id为{}的个人培训订单数量：{}", orderId, matchingOrders.size());
                
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("成功找到个人培训订单详情，个人培训订单ID：{}，订单号：{}", personalTraining.getId(), personalTraining.getOrderNo());
        } else {
            // 如果没找到主订单，尝试查询个人培训订单详情表
            log.info("未找到主订单，尝试直接查询个人培训订单详情表，订单ID：{}", reqDTO.getOrderId());
            
            personalTraining = personalTrainingMapper.selectById(reqDTO.getOrderId());
            if (personalTraining == null) {
                log.error("审批拒绝失败：个人培训订单不存在，订单ID：{}", reqDTO.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            // 通过个人培训订单的orderId查询主订单
            order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
            if (order == null) {
                log.error("审批拒绝失败：主订单不存在，个人培训订单ID：{}，主订单ID：{}", 
                        reqDTO.getOrderId(), personalTraining.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("通过个人培训订单找到主订单，主订单ID：{}，订单类型：{}", order.getId(), order.getOrderType());
        }
        
        // 2. 验证订单号一致性
        if (reqDTO.getOrderNo() != null && !reqDTO.getOrderNo().equals(personalTraining.getOrderNo())) {
            log.error("审批拒绝失败：订单号不匹配，请求订单号：{}，数据库订单号：{}", 
                    reqDTO.getOrderNo(), personalTraining.getOrderNo());
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
        
        // 3. 验证订单状态是否允许拒绝
        if (!"pending_approval".equals(order.getOrderStatus()) && !"draft".equals(order.getOrderStatus())) {
            log.error("审批拒绝失败：订单状态不允许拒绝，当前状态：{}", order.getOrderStatus());
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_STATUS_INVALID);
        }
        
        // 4. 更新订单状态为已拒绝
        String oldStatus = order.getOrderStatus(); // 保存旧状态用于日志
        order.setOrderStatus("rejected");
        order.setRemark(reqDTO.getApprovalOpinion());
        order.setUpdateTime(LocalDateTime.now());
        order.setUpdater(String.valueOf(getCurrentUserId())); // 修复：将Long转换为String
        publicbizOrderMapper.updateById(order);
        
        // 5. 记录操作日志
        String logContent = String.format("订单【%s】审批被拒绝，拒绝原因：%s，审批意见：%s", 
                personalTraining.getOrderNo(), 
                reqDTO.getRejectReason() != null ? reqDTO.getRejectReason() : "无",
                reqDTO.getApprovalOpinion());
        
        // 记录操作日志到系统日志表
        PublicbizOrderLogDO logDO = new PublicbizOrderLogDO();
        logDO.setOrderNo(personalTraining.getOrderNo());
        logDO.setLogType("审批驳回");
        logDO.setLogTitle("审批驳回");
        logDO.setLogContent(logContent);
        logDO.setOldStatus(oldStatus); // 修复：使用保存的旧状态
        logDO.setNewStatus("rejected");
        logDO.setOperatorId(getCurrentUserId());
        logDO.setOperatorName(reqDTO.getOperatorName() != null ? reqDTO.getOperatorName() : "系统管理员");
        logDO.setOperatorRole("审批人");
        logDO.setCreateTime(LocalDateTime.now());
        logDO.setUpdateTime(LocalDateTime.now());
        publicbizOrderLogMapper.insert(logDO);
        
        log.info("审批拒绝操作完成，订单ID：{}，拒绝原因：{}", reqDTO.getOrderId(), reqDTO.getRejectReason());
    }

    @Override
    public PageResult<PersonalTrainingApprovalRespDTO> getApprovalPage(PersonalTrainingApprovalPageReqDTO pageReqDTO) {
        log.info("开始分页查询审批记录，查询条件：{}", pageReqDTO);
        
        try {
            // 构建查询条件（注释掉，因为审批表不存在）
            // LambdaQueryWrapperX<PersonalTrainingApprovalDO> queryWrapper = new LambdaQueryWrapperX<>();
            
            // if (pageReqDTO.getOrderId() != null) {
            //     queryWrapper.eq(PersonalTrainingApprovalDO::getOrderId, pageReqDTO.getOrderId());
            // }
            // if (StrUtil.isNotBlank(pageReqDTO.getOrderNo())) {
            //     queryWrapper.eq(PersonalTrainingApprovalDO::getOrderNo, pageReqDTO.getOrderNo());
            // }
            // if (StrUtil.isNotBlank(pageReqDTO.getApprovalType())) {
            //     queryWrapper.eq(PersonalTrainingApprovalDO::getApprovalType, pageReqDTO.getApprovalType());
            // }
            // if (StrUtil.isNotBlank(pageReqDTO.getApprovalStatus())) {
            //     queryWrapper.eq(PersonalTrainingApprovalDO::getApprovalStatus, pageReqDTO.getApprovalStatus());
            // }
            // if (StrUtil.isNotBlank(pageReqDTO.getApproverName())) {
            //     queryWrapper.like(PersonalTrainingApprovalDO::getApproverName, pageReqDTO.getApproverName());
            // }
            // if (pageReqDTO.getStartDate() != null) {
            //     queryWrapper.ge(PersonalTrainingApprovalDO::getCreateTime, pageReqDTO.getStartDate().atStartOfDay());
            // }
            // if (pageReqDTO.getEndDate() != null) {
            //     queryWrapper.le(PersonalTrainingApprovalDO::getCreateTime, pageReqDTO.getEndDate().atTime(23, 59, 59));
            // }
            
            // 按创建时间倒序排列
            // queryWrapper.orderByDesc(PersonalTrainingApprovalDO::getCreateTime);
            
            // 执行分页查询 - 由于审批表不存在，返回空结果
            // List<PersonalTrainingApprovalDO> list = personalTrainingApprovalMapper.selectList(queryWrapper);
            // PageResult<PersonalTrainingApprovalDO> pageResult = new PageResult<>(list, (long) list.size());
            
            // 转换为响应DTO
            // List<PersonalTrainingApprovalRespDTO> respList = pageResult.getList().stream()
            //         .map(this::convertToApprovalRespDTO)
            //         .collect(Collectors.toList());
            
            PageResult<PersonalTrainingApprovalRespDTO> respPageResult = new PageResult<>();
            respPageResult.setList(new ArrayList<>());
            respPageResult.setTotal(0L);
            
            log.info("分页查询审批记录成功，总记录数：0（审批表不存在）");
            return respPageResult;
            
        } catch (Exception e) {
            log.error("分页查询审批记录失败，错误：{}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
    }

    // ========== 合同管理方法实现 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmContract(PersonalTrainingContractReqDTO reqDTO) {
        log.info("确认合同，订单ID：{}，合同类型：{}，合同状态：{}", 
                reqDTO.getOrderId(), reqDTO.getContractType(), reqDTO.getContractStatus());
        
        try {
            // 1. 验证订单状态
            PersonalTrainingDO personalTraining = personalTrainingMapper.selectById(reqDTO.getOrderId());
            if (personalTraining == null) {
                log.error("确认合同失败：个人培训订单不存在，订单ID：{}", reqDTO.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("找到个人培训订单，订单ID：{}，订单号：{}", personalTraining.getId(), personalTraining.getOrderNo());
            
            PublicbizOrderDO order = publicbizOrderMapper.selectById(personalTraining.getOrderId());
            if (order == null) {
                log.error("确认合同失败：主订单不存在，个人培训订单ID：{}，主订单ID：{}", 
                        reqDTO.getOrderId(), personalTraining.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("找到主订单，主订单ID：{}，订单状态：{}，订单类型：{}", 
                    order.getId(), order.getOrderStatus(), order.getOrderType());
            
            // 检查订单状态是否允许确认合同
            // 允许在以下状态下确认合同：
            // - draft: 草稿状态（订单创建时直接确认合同）
            // - approved: 已批准状态（审批通过后确认合同）
            // - pending_approval: 待审批状态（提交审批时确认合同）
            if (!"draft".equals(order.getOrderStatus()) && 
                !"approved".equals(order.getOrderStatus()) && 
                !"pending_approval".equals(order.getOrderStatus())) {
                log.error("确认合同失败：订单状态不允许确认合同，当前状态：{}，期望状态：draft、approved或pending_approval", order.getOrderStatus());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_STATUS_INVALID);
            }
            
            log.info("订单状态检查通过，当前状态：{}，允许确认合同", order.getOrderStatus());
        
            // 2. 创建或更新合同记录
            log.info("开始查询或创建合同记录，订单ID：{}", reqDTO.getOrderId());
            
            // 使用合作伙伴合同表，将订单ID作为partnerId
            PublicbizPartnerContractDO contractDO = publicbizPartnerContractMapper.selectOne(
                new LambdaQueryWrapperX<PublicbizPartnerContractDO>()
                    .eq(PublicbizPartnerContractDO::getPartnerId, reqDTO.getOrderId())
            );
            
            if (contractDO == null) {
                log.info("合同记录不存在，开始创建新合同记录");
                
                // 解析签约日期
                LocalDate signDate = null;
                if (StrUtil.isNotBlank(reqDTO.getSignDate())) {
                    try {
                        signDate = LocalDate.parse(reqDTO.getSignDate());
                        log.info("签约日期解析成功：{}", signDate);
                    } catch (Exception e) {
                        log.error("签约日期解析失败：{}，使用当前时间", reqDTO.getSignDate(), e);
                        signDate = LocalDate.now();
                    }
                } else {
                    signDate = LocalDate.now();
                    log.info("签约日期为空，使用当前时间：{}", signDate);
                }
                
                // 创建新合同记录
                contractDO = PublicbizPartnerContractDO.builder()
                        .contractNumber(reqDTO.getContractNumber() != null ? reqDTO.getContractNumber() : "CON" + System.currentTimeMillis())
                        .partnerId(reqDTO.getOrderId()) // 使用订单ID作为合作伙伴ID
                        .contractName(reqDTO.getContractName() != null ? reqDTO.getContractName() : "个人培训服务合同")
                        .startDate(signDate)
                        .endDate(signDate.plusYears(1)) // 合同有效期1年
                        .amount(reqDTO.getContractAmount() != null ? reqDTO.getContractAmount() : order.getTotalAmount())
                        .status(reqDTO.getContractStatus())
                        .attachmentPath(reqDTO.getContractFileUrl())
                        .signer(reqDTO.getSigner())
                        .build();
                
                publicbizPartnerContractMapper.insert(contractDO);
                log.info("合同记录创建成功，合同ID：{}", contractDO.getContractId());
            } else {
                log.info("合同记录已存在，开始更新合同记录，合同ID：{}", contractDO.getContractId());
                
                // 更新现有合同记录
                if (reqDTO.getContractName() != null) {
                    contractDO.setContractName(reqDTO.getContractName());
                }
                if (reqDTO.getContractNumber() != null) {
                    contractDO.setContractNumber(reqDTO.getContractNumber());
                }
                contractDO.setAttachmentPath(reqDTO.getContractFileUrl());
                contractDO.setStatus(reqDTO.getContractStatus());
                contractDO.setSigner(reqDTO.getSigner());
                
                if (StrUtil.isNotBlank(reqDTO.getSignDate())) {
                    try {
                        LocalDate signDate = LocalDate.parse(reqDTO.getSignDate());
                        contractDO.setStartDate(signDate);
                        contractDO.setEndDate(signDate.plusYears(1)); // 合同有效期1年
                        log.info("签约日期更新成功：{}", signDate);
                    } catch (Exception e) {
                        log.error("签约日期解析失败：{}，保持原值", reqDTO.getSignDate(), e);
                    }
                }
                
                if (reqDTO.getContractAmount() != null) {
                    contractDO.setAmount(reqDTO.getContractAmount());
                } else {
                    contractDO.setAmount(order.getTotalAmount());
                }
                
                publicbizPartnerContractMapper.updateById(contractDO);
                log.info("合同记录更新成功，合同ID：{}", contractDO.getContractId());
            }
        
            // 3. 更新订单合同状态
            log.info("开始更新订单合同状态，订单ID：{}，合同类型：{}", order.getId(), reqDTO.getContractType());
            order.setContractType(reqDTO.getContractType());
            
            // 如果订单状态是draft且合同状态是signed，可以考虑更新订单状态
            if ("draft".equals(order.getOrderStatus()) && "signed".equals(reqDTO.getContractStatus())) {
                log.info("订单状态为draft且合同已签署，保持订单状态为draft（等待后续审批流程）");
                // 这里可以根据业务需求决定是否更新订单状态
                // 例如：order.setOrderStatus("pending_approval");
            }
            
            publicbizOrderMapper.updateById(order);
            log.info("订单合同状态更新成功");
            
            // 4. 记录操作日志
            String logContent = String.format("订单【%s】合同确认，合同类型：%s，合同状态：%s，签约人：%s", 
                    personalTraining.getOrderNo(), reqDTO.getContractType(), reqDTO.getContractStatus(), 
                    reqDTO.getSigner() != null ? reqDTO.getSigner() : "无");
            
            // 记录操作日志到系统日志表
            PublicbizOrderLogDO logDO = new PublicbizOrderLogDO();
            logDO.setOrderNo(personalTraining.getOrderNo());
            logDO.setLogType("合同确认");
            logDO.setLogTitle("合同确认");
            logDO.setLogContent(logContent);
            logDO.setOldStatus(order.getOrderStatus()); // 保持当前订单状态
            logDO.setNewStatus(order.getOrderStatus()); // 订单状态可能没有变化
            logDO.setOperatorId(getCurrentUserId());
            logDO.setOperatorName("系统管理员");
            logDO.setOperatorRole("合同管理员");
            logDO.setCreateTime(LocalDateTime.now());
            logDO.setUpdateTime(LocalDateTime.now());
            publicbizOrderLogMapper.insert(logDO);
            
            log.info("合同确认完成，合同ID：{}，操作日志已记录", contractDO.getContractId());
            
        } catch (Exception e) {
            log.error("确认合同失败，订单ID：{}，错误：{}", reqDTO.getOrderId(), e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_CONTRACT_FAIL);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateContract(PersonalTrainingContractReqDTO reqDTO) {
        log.info("更新合同信息，订单ID：{}，合同类型：{}，合同状态：{}", 
                reqDTO.getOrderId(), reqDTO.getContractType(), reqDTO.getContractStatus());
        
        try {
            // 1. 验证合同记录是否存在
            PublicbizPartnerContractDO contractDO = publicbizPartnerContractMapper.selectOne(
                new LambdaQueryWrapperX<PublicbizPartnerContractDO>()
                    .eq(PublicbizPartnerContractDO::getPartnerId, reqDTO.getOrderId())
            );
            if (contractDO == null) {
                log.error("更新合同失败：合同记录不存在，订单ID：{}", reqDTO.getOrderId());
                throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
            }
            
            log.info("找到合同记录，合同ID：{}", contractDO.getContractId());
            
            // 2. 更新合同信息
            if (StrUtil.isNotBlank(reqDTO.getContractFileUrl())) {
                contractDO.setAttachmentPath(reqDTO.getContractFileUrl());
                log.info("更新合同文件URL：{}", reqDTO.getContractFileUrl());
            }
            if (StrUtil.isNotBlank(reqDTO.getContractStatus())) {
                contractDO.setStatus(reqDTO.getContractStatus());
                log.info("更新合同状态：{}", reqDTO.getContractStatus());
            }
            if (StrUtil.isNotBlank(reqDTO.getSigner())) {
                contractDO.setSigner(reqDTO.getSigner());
                log.info("更新签约人：{}", reqDTO.getSigner());
            }
            if (StrUtil.isNotBlank(reqDTO.getSignDate())) {
                try {
                    LocalDate signDate = LocalDate.parse(reqDTO.getSignDate());
                    contractDO.setStartDate(signDate);
                    contractDO.setEndDate(signDate.plusYears(1));
                    log.info("更新签约日期：{}", signDate);
                } catch (Exception e) {
                    log.error("签约日期解析失败：{}，保持原值", reqDTO.getSignDate(), e);
                }
            }
            
            publicbizPartnerContractMapper.updateById(contractDO);
            log.info("合同信息更新成功，合同ID：{}", contractDO.getContractId());
            
            // 3. 记录操作日志
            String logContent = String.format("订单【{}】合同信息更新，合同状态：{}", 
                    reqDTO.getOrderId(), contractDO.getStatus());
            
            // 记录操作日志到系统日志表
            PublicbizOrderLogDO logDO = new PublicbizOrderLogDO();
            logDO.setOrderNo("PT" + reqDTO.getOrderId()); // 构造订单号
            logDO.setLogType("合同更新");
            logDO.setLogTitle("合同更新");
            logDO.setLogContent(logContent);
            logDO.setOldStatus(contractDO.getStatus()); // 合同状态可能没有变化
            logDO.setNewStatus(contractDO.getStatus()); // 合同状态可能没有变化
            logDO.setOperatorId(getCurrentUserId());
            logDO.setOperatorName("系统管理员");
            logDO.setOperatorRole("合同管理员");
            logDO.setCreateTime(LocalDateTime.now());
            logDO.setUpdateTime(LocalDateTime.now());
            publicbizOrderLogMapper.insert(logDO);
            
            log.info("合同信息更新完成，合同ID：{}，操作日志已记录", contractDO.getContractId());
            
        } catch (Exception e) {
            log.error("更新合同信息失败，订单ID：{}，错误：{}", reqDTO.getOrderId(), e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_CONTRACT_FAIL);
        }
    }

    @Override
    public PersonalTrainingContractRespDTO getContractInfo(Long orderId) {
        log.info("获取合同信息，订单ID：{}", orderId);
        
        // 1. 查询合同记录
        PublicbizPartnerContractDO contractDO = publicbizPartnerContractMapper.selectOne(
            new LambdaQueryWrapperX<PublicbizPartnerContractDO>()
                .eq(PublicbizPartnerContractDO::getPartnerId, orderId)
        );
        if (contractDO == null) {
            log.warn("合同信息不存在，订单ID：{}", orderId);
            return null;
        }
        
        // 2. 转换为响应DTO
        PersonalTrainingContractRespDTO respDTO = new PersonalTrainingContractRespDTO();
        respDTO.setContractId(contractDO.getContractId());
        respDTO.setOrderId(contractDO.getPartnerId());
        respDTO.setContractType("paper"); // 默认纸质合同
        respDTO.setContractNumber(contractDO.getContractNumber()); // 合同编号
        respDTO.setContractName(contractDO.getContractName()); // 合同名称
        respDTO.setContractFileUrl(contractDO.getAttachmentPath());
        respDTO.setContractStatus(contractDO.getStatus());
        respDTO.setSigner(contractDO.getSigner());
        respDTO.setContractAmount(contractDO.getAmount()); // 合同金额
        if (contractDO.getStartDate() != null) {
            respDTO.setSignDate(contractDO.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        if (contractDO.getCreateTime() != null) {
            respDTO.setCreateTime(contractDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (contractDO.getUpdateTime() != null) {
            respDTO.setUpdateTime(contractDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        
        log.info("合同信息查询成功，合同ID：{}", contractDO.getContractId());
        return respDTO;
    }

    // ========== 操作日志方法实现 ==========

    @Override
    public PageResult<PersonalTrainingOptLogRespDTO> getOptLogPage(PersonalTrainingOptLogPageReqDTO pageReqDTO) {
        log.info("开始分页查询操作日志，查询条件：{}", pageReqDTO);
        
        try {
            // 构建查询条件
            LambdaQueryWrapperX<PublicbizOrderLogDO> queryWrapper = new LambdaQueryWrapperX<>();
            
            // 根据订单号查询
            if (StrUtil.isNotBlank(pageReqDTO.getOrderNo())) {
                queryWrapper.eq(PublicbizOrderLogDO::getOrderNo, pageReqDTO.getOrderNo());
            }
            
            // 根据日志类型查询
            if (StrUtil.isNotBlank(pageReqDTO.getLogType())) {
                queryWrapper.eq(PublicbizOrderLogDO::getLogType, pageReqDTO.getLogType());
            }
            
            // 根据时间范围查询
            if (StrUtil.isNotBlank(pageReqDTO.getStartDate())) {
                queryWrapper.ge(PublicbizOrderLogDO::getCreateTime, pageReqDTO.getStartDate() + " 00:00:00");
            }
            if (StrUtil.isNotBlank(pageReqDTO.getEndDate())) {
                queryWrapper.le(PublicbizOrderLogDO::getCreateTime, pageReqDTO.getEndDate() + " 23:59:59");
            }
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc(PublicbizOrderLogDO::getCreateTime);
            
            // 执行分页查询
            List<PublicbizOrderLogDO> list = publicbizOrderLogMapper.selectList(queryWrapper);
            PageResult<PublicbizOrderLogDO> pageResult = new PageResult<>(list, (long) list.size());
            
            // 转换为响应DTO
            List<PersonalTrainingOptLogRespDTO> respList = pageResult.getList().stream()
                    .map(this::convertToOptLogRespDTO)
                    .collect(Collectors.toList());
            
            PageResult<PersonalTrainingOptLogRespDTO> respPageResult = new PageResult<>();
            respPageResult.setList(respList);
            respPageResult.setTotal(pageResult.getTotal());
            
            log.info("分页查询操作日志成功，总记录数：{}", respPageResult.getTotal());
            return respPageResult;
            
        } catch (Exception e) {
            log.error("分页查询操作日志失败，错误：{}", e.getMessage(), e);
            throw ServiceExceptionUtil.exception(PERSONAL_TRAINING_ORDER_NOT_FOUND);
        }
    }

    // ========== 导出方法实现 ==========

    @Override
    public PersonalTrainingExportRespDTO export(PersonalTrainingExportReqDTO reqDTO) {
        log.info("导出订单列表，导出类型：{}", reqDTO.getExportType());
        
        // 1. 根据条件查询订单数据
        LambdaQueryWrapperX<PersonalTrainingDO> queryWrapper = new LambdaQueryWrapperX<PersonalTrainingDO>();
        
        // 这些字段在订单主表中，需要特殊处理
        // 暂时忽略这些查询条件，因为需要联表查询
        
        if (StrUtil.isNotBlank(reqDTO.getKeyword())) {
            queryWrapper.and(w -> w.like(PersonalTrainingDO::getStudentName, reqDTO.getKeyword())
                    .or().like(PersonalTrainingDO::getCourseName, reqDTO.getKeyword()));
        }
        
        if (StrUtil.isNotBlank(reqDTO.getStartDate())) {
            queryWrapper.ge(PersonalTrainingDO::getCreateTime, reqDTO.getStartDate() + " 00:00:00");
        }
        
        if (StrUtil.isNotBlank(reqDTO.getEndDate())) {
            queryWrapper.le(PersonalTrainingDO::getCreateTime, reqDTO.getEndDate() + " 23:59:59");
        }
        
        List<PersonalTrainingDO> orderList = personalTrainingMapper.selectList(queryWrapper);
        
        // 2. 生成Excel文件（这里简化处理，实际应该使用POI等库生成Excel）
        String fileName = "个人培训订单_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
        String downloadUrl = "/download/orders/" + fileName;
        
        // 3. 记录导出操作日志
        if (!orderList.isEmpty()) {
            createOptLog(orderList.get(0).getOrderNo(), "数据导出", "订单导出", 
                    String.format("导出了%d条订单记录", orderList.size()), 
                    "未导出", "已导出", "系统管理员");
        }
        
        // 4. 返回下载链接
        PersonalTrainingExportRespDTO respDTO = new PersonalTrainingExportRespDTO();
        respDTO.setDownloadUrl(downloadUrl);
        respDTO.setFileName(fileName);
        respDTO.setExportTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        respDTO.setExportCount(orderList.size());
        
        log.info("订单导出完成，导出记录数：{}", orderList.size());
        return respDTO;
    }

    // ========== 统计方法实现 ==========

    @Override
    public PersonalTrainingStatisticsRespDTO getStatistics() {
        log.info("开始获取个人培训订单统计信息");
        
        try {
            // 1. 统计总订单数（从个人培训订单详情表统计）
            long totalOrders = personalTrainingMapper.selectCount(
                new LambdaQueryWrapperX<PersonalTrainingDO>()
                    .eq(PersonalTrainingDO::getDeleted, false)
            );
            
            // 2. 统计待处理订单数（从订单主表查询，状态为待审批的）
            long pendingOrders = publicbizOrderMapper.selectCount(
                new LambdaQueryWrapperX<PublicbizOrderDO>()
                    .eq(PublicbizOrderDO::getOrderType, "personal")
                    .eq(PublicbizOrderDO::getOrderStatus, "pending_approval")
                    .eq(PublicbizOrderDO::getDeleted, false)
            );
            
            // 3. 统计本月订单金额
            LocalDateTime startOfMonth = LocalDateTime.now()
                .withDayOfMonth(1)
                .withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
            LocalDateTime endOfMonth = startOfMonth.plusMonths(1).minusNanos(1);
            
            // 从订单主表查询本月订单
            List<PublicbizOrderDO> monthlyOrders = publicbizOrderMapper.selectList(
                new LambdaQueryWrapperX<PublicbizOrderDO>()
                    .eq(PublicbizOrderDO::getOrderType, "personal")
                    .between(PublicbizOrderDO::getCreateTime, startOfMonth, endOfMonth)
                    .eq(PublicbizOrderDO::getDeleted, false)
            );
            
            BigDecimal monthlyAmount = monthlyOrders.stream()
                .map(PublicbizOrderDO::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 4. 计算订单完成率
            long completedOrders = publicbizOrderMapper.selectCount(
                new LambdaQueryWrapperX<PublicbizOrderDO>()
                    .eq(PublicbizOrderDO::getOrderType, "personal")
                    .eq(PublicbizOrderDO::getOrderStatus, "completed")
                    .eq(PublicbizOrderDO::getDeleted, false)
            );
            
            BigDecimal completionRate = totalOrders > 0 ? 
                new BigDecimal(completedOrders)
                    .divide(new BigDecimal(totalOrders), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100")) : BigDecimal.ZERO;
            
            // 5. 统计各类型订单数量（从个人培训订单详情表统计）
            long trainingOrderCount = personalTrainingMapper.selectCount(
                new LambdaQueryWrapperX<PersonalTrainingDO>()
                    .eq(PersonalTrainingDO::getCourseType, "个人培训")
                    .eq(PersonalTrainingDO::getDeleted, false)
            );
            
            long certificationOrderCount = personalTrainingMapper.selectCount(
                new LambdaQueryWrapperX<PersonalTrainingDO>()
                    .eq(PersonalTrainingDO::getCourseType, "考试认证")
                    .eq(PersonalTrainingDO::getDeleted, false)
            );
            
            // 6. 统计支付状态订单数量（从订单主表查询）
            long paidOrderCount = publicbizOrderMapper.selectCount(
                new LambdaQueryWrapperX<PublicbizOrderDO>()
                    .eq(PublicbizOrderDO::getOrderType, "personal")
                    .eq(PublicbizOrderDO::getPaymentStatus, "paid")
                    .eq(PublicbizOrderDO::getDeleted, false)
            );
            
            long unpaidOrderCount = publicbizOrderMapper.selectCount(
                new LambdaQueryWrapperX<PublicbizOrderDO>()
                    .eq(PublicbizOrderDO::getOrderType, "personal")
                    .eq(PublicbizOrderDO::getPaymentStatus, "pending")
                    .eq(PublicbizOrderDO::getDeleted, false)
            );
            
            // 7. 统计学习状态分布
            long notStartedCount = personalTrainingMapper.selectCount(
                new LambdaQueryWrapperX<PersonalTrainingDO>()
                    .eq(PersonalTrainingDO::getLearningStatus, "not_started")
                    .eq(PersonalTrainingDO::getDeleted, false)
            );
            
            long learningCount = personalTrainingMapper.selectCount(
                new LambdaQueryWrapperX<PersonalTrainingDO>()
                    .eq(PersonalTrainingDO::getLearningStatus, "learning")
                    .eq(PersonalTrainingDO::getDeleted, false)
            );
            
            long learningPendingExamCount = personalTrainingMapper.selectCount(
                new LambdaQueryWrapperX<PersonalTrainingDO>()
                    .eq(PersonalTrainingDO::getLearningStatus, "learning_pending_exam")
                    .eq(PersonalTrainingDO::getDeleted, false)
            );
            
            long completedCount = personalTrainingMapper.selectCount(
                new LambdaQueryWrapperX<PersonalTrainingDO>()
                    .eq(PersonalTrainingDO::getLearningStatus, "completed")
                    .eq(PersonalTrainingDO::getDeleted, false)
            );
            
            // 8. 构建响应对象
            PersonalTrainingStatisticsRespDTO respDTO = new PersonalTrainingStatisticsRespDTO();
            respDTO.setTotalOrders((int) totalOrders);
            respDTO.setPendingOrders((int) pendingOrders);
            respDTO.setMonthlyAmount(monthlyAmount);
            respDTO.setCompletionRate(completionRate);
            respDTO.setTrainingOrderCount((int) trainingOrderCount);
            respDTO.setCertificationOrderCount((int) certificationOrderCount);
            respDTO.setPaidOrderCount((int) paidOrderCount);
            respDTO.setUnpaidOrderCount((int) unpaidOrderCount);
            
            // 9. 记录统计结果日志
            log.info("个人培训订单统计完成：" +
                    "总订单数：{}，" +
                    "待处理订单数：{}，" +
                    "本月金额：{}，" +
                    "完成率：{}%，" +
                    "培训订单数：{}，" +
                    "认证订单数：{}，" +
                    "已支付：{}，" +
                    "待支付：{}，" +
                    "未开始：{}，" +
                    "学习中：{}，" +
                    "学习中待考试：{}，" +
                    "已完成：{}",
                    totalOrders, pendingOrders, monthlyAmount, completionRate,
                    trainingOrderCount, certificationOrderCount, paidOrderCount, unpaidOrderCount,
                    notStartedCount, learningCount, learningPendingExamCount, completedCount);
            
            return respDTO;
            
        } catch (Exception e) {
            log.error("获取个人培训订单统计信息失败", e);
            // 返回默认值，避免接口报错
            PersonalTrainingStatisticsRespDTO respDTO = new PersonalTrainingStatisticsRespDTO();
            respDTO.setTotalOrders(0);
            respDTO.setPendingOrders(0);
            respDTO.setMonthlyAmount(BigDecimal.ZERO);
            respDTO.setCompletionRate(BigDecimal.ZERO);
            respDTO.setTrainingOrderCount(0);
            respDTO.setCertificationOrderCount(0);
            respDTO.setPaidOrderCount(0);
            respDTO.setUnpaidOrderCount(0);
            return respDTO;
        }
    }

    // ========== 辅助方法 ==========

    /**
     * 构建个人培训订单响应DTO
     */
    private PersonalTrainingRespDTO buildPersonalTrainingRespDTO(PersonalTrainingDO personalTraining, 
                                                               PublicbizOrderDO order, 
                                                               PublicbizPartnerContractDO contract, 
                                                               List<PublicbizOrderPaymentDO> paymentList) {
        PersonalTrainingRespDTO respDTO = new PersonalTrainingRespDTO();
        
        // 设置个人培训订单详情信息
        respDTO.setId(personalTraining.getId());
        respDTO.setOrderId(personalTraining.getOrderId());
        respDTO.setOrderNo(personalTraining.getOrderNo());
        respDTO.setStudentName(personalTraining.getStudentName());
        respDTO.setStudentOneid(personalTraining.getStudentOneid());
        respDTO.setStudentPhone(personalTraining.getStudentPhone());
        respDTO.setStudentEmail(personalTraining.getStudentEmail());
        respDTO.setCourseName(personalTraining.getCourseName());
        respDTO.setCourseType(personalTraining.getCourseType());
        respDTO.setCourseDescription(personalTraining.getCourseDescription());
        respDTO.setCourseDuration(personalTraining.getCourseDuration());
        respDTO.setLearningStatus(personalTraining.getLearningStatus());
        respDTO.setExamStatus(personalTraining.getExamStatus());
        respDTO.setCourseFee(personalTraining.getCourseFee());
        respDTO.setExamFee(personalTraining.getExamFee());
        respDTO.setCertificationFee(personalTraining.getCertificationFee());
        
        // 设置订单主表信息
        respDTO.setOrderType(order.getOrderType());
        respDTO.setBusinessLine(order.getBusinessLine());
        respDTO.setOpportunityId(order.getOpportunityId());
        respDTO.setLeadId(order.getLeadId());
        respDTO.setProjectName(order.getProjectName());
        respDTO.setProjectDescription(order.getProjectDescription());
        respDTO.setStartDate(order.getStartDate());
        respDTO.setEndDate(order.getEndDate());
        respDTO.setOrderAmount(order.getTotalAmount());
        respDTO.setPaidAmount(order.getPaidAmount());
        respDTO.setRefundAmount(order.getRefundAmount());
        respDTO.setOrderStatus(order.getOrderStatus());
        respDTO.setPaymentStatus(order.getPaymentStatus());
        respDTO.setManagerId(order.getManagerId());
        respDTO.setManagerName(order.getManagerName());
        respDTO.setManagerPhone(order.getManagerPhone());
        respDTO.setContractType(order.getContractType());
        respDTO.setContractFileUrl(order.getContractFileUrl());
        respDTO.setContractStatus(order.getContractStatus());
        respDTO.setRemark(order.getRemark());
        respDTO.setSettlementStatus(order.getSettlementStatus());
        respDTO.setSettlementTime(order.getSettlementTime());
        respDTO.setSettlementMethod(order.getSettlementMethod());
        
        // 设置合同信息
        if (contract != null) {
            PersonalTrainingRespDTO.ContractInfo contractInfo = new PersonalTrainingRespDTO.ContractInfo();
            contractInfo.setContractId(contract.getContractId());
            contractInfo.setContractName(contract.getContractName());
            contractInfo.setContractNumber(contract.getContractNumber());
            contractInfo.setStartDate(contract.getStartDate());
            contractInfo.setEndDate(contract.getEndDate());
            contractInfo.setAmount(contract.getAmount());
            contractInfo.setStatus(contract.getStatus());
            contractInfo.setAttachmentPath(contract.getAttachmentPath());
            contractInfo.setSigner(contract.getSigner());
            respDTO.setContractInfo(contractInfo);
        }
        
        // 设置支付信息
        if (paymentList != null && !paymentList.isEmpty()) {
            List<PersonalTrainingRespDTO.PaymentInfo> paymentInfoList = paymentList.stream()
                .map(payment -> {
                    PersonalTrainingRespDTO.PaymentInfo paymentInfo = new PersonalTrainingRespDTO.PaymentInfo();
                    paymentInfo.setId(payment.getId());
                    paymentInfo.setPaymentNo(payment.getPaymentNo());
                    paymentInfo.setPaymentType(payment.getPaymentType());
                    paymentInfo.setPaymentAmount(payment.getPaymentAmount());
                    paymentInfo.setPaymentStatus(payment.getPaymentStatus());
                    paymentInfo.setPaymentTime(payment.getPaymentTime());
                    paymentInfo.setOperatorId(payment.getOperatorId());
                    paymentInfo.setOperatorName(payment.getOperatorName());
                    paymentInfo.setPaymentRemark(payment.getPaymentRemark());
                    paymentInfo.setTransactionId(payment.getTransactionId());
                    return paymentInfo;
                })
                .collect(Collectors.toList());
            respDTO.setPaymentList(paymentInfoList);
        }
        
        // 设置其他信息
        respDTO.setApprovalLevel("0"); // 由于审批表不存在，设置默认审批级别
        respDTO.setCreateTime(personalTraining.getCreateTime());
        respDTO.setUpdateTime(personalTraining.getUpdateTime());
        
        return respDTO;
    }

    /**
     * 创建下一级审批记录
     */
    private void createNextLevelApproval(Object currentApproval, PersonalTrainingApprovalReqDTO reqDTO) {
        // PersonalTrainingApprovalDO nextApproval = PersonalTrainingApprovalDO.builder()
        //         .approvalNo("APV" + System.currentTimeMillis())
        //         .orderId(currentApproval.getOrderId())
        //         .orderNo(currentApproval.getOrderNo())
        //         .approvalType(currentApproval.getApprovalType())
        //         .approvalLevel(currentApproval.getApprovalLevel())
        //         .currentLevel(currentApproval.getCurrentLevel() + 1)
        //         .approvalResult("pending")
        //         .approvalStatus("pending")
        //         .nextApproverIds(convertListToJson(reqDTO.getApproverIds()))
        //         .remark("下一级审批")
        //         .creatorId(getCurrentUserId())
        //         .creatorName("系统管理员")
        //         .build();
        
        // 手动设置deleted字段
        // nextApproval.setDeleted(false);
        
        // personalTrainingApprovalMapper.insert(nextApproval);
        log.info("下一级审批记录创建成功（注释掉，因为审批表不存在）");
    }

    /**
     * 记录审批日志
     */
    private void recordApprovalLog(PersonalTrainingApprovalReqDTO reqDTO, PersonalTrainingDO personalTraining, 
                                  String oldStatus, String newStatus, Long operatorId, String operatorName, String operatorRole) {
        // orderLogService.recordOrderEdit(reqDTO.getOrderNo(), "personal-training", 
        //         buildApprovalChanges(reqDTO, personalTraining),
        //         oldStatus, newStatus, 
        //         operatorId, operatorName, "审批人"); // 硬编码为"审批人"，因为 operatorRole 字段不存在
        log.info("记录审批日志，订单号：{}", personalTraining.getOrderNo());
    }

    /**
     * 记录合同确认日志
     */
    private void recordContractLog(PersonalTrainingContractReqDTO reqDTO, PersonalTrainingDO personalTraining, 
                                  String oldStatus, String newStatus, Long operatorId, String operatorName, String operatorRole) {
        // orderLogService.recordOrderEdit(reqDTO.getOrderNo(), "personal-training", 
        //         buildContractChanges(reqDTO, personalTraining),
        //         oldStatus, newStatus, 
        //         operatorId, operatorName, "合同管理员"); // 硬编码为"合同管理员"，因为 operatorRole 字段不存在
        log.info("记录合同日志，订单号：{}", personalTraining.getOrderNo());
    }

    /**
     * 记录操作日志
     */
    private void recordOptLog(PersonalTrainingOptLogReqDTO reqDTO, PersonalTrainingDO personalTraining, 
                             String oldStatus, String newStatus, Long operatorId, String operatorName, String operatorRole) {
        orderLogService.recordOrderEdit(reqDTO.getOrderNo(), "personal-training", 
                buildOptLogChanges(reqDTO, personalTraining),
                oldStatus, newStatus, 
                operatorId, operatorName, "系统管理员"); // 硬编码为"系统管理员"，因为 operatorRole 字段不存在
    }

    /**
     * 记录收款日志
     */
    private void recordCollectionLog(PersonalTrainingCollectionReqDTO reqDTO, PublicbizOrderDO order, 
                                    String oldStatus, String newStatus, Long operatorId, String operatorName, String operatorRole) {
        orderLogService.recordOrderEdit(reqDTO.getOrderNo(), "personal-training", 
                buildCollectionChanges(reqDTO, order),
                oldStatus, newStatus, 
                operatorId, operatorName, "收款员"); // 硬编码为"收款员"，因为 operatorRole 字段不存在
    }

    /**
     * 记录收款更新日志
     */
    private void recordCollectionUpdateLog(PersonalTrainingCollectionReqDTO reqDTO, PublicbizOrderDO order, 
                                          String oldStatus, String newStatus, Long operatorId, String operatorName, String operatorRole) {
        // orderLogService.recordOrderEdit(reqDTO.getOrderNo(), "personal-training", 
        //         buildCollectionUpdateChanges(reqDTO, order),
        //         oldStatus, newStatus, 
        //         operatorId, operatorName, "收款员"); // 硬编码为"收款员"，因为 operatorRole 字段不存在
    }

    /**
     * 转换为审批响应DTO（注释掉，因为审批表不存在）
     */
    private PersonalTrainingApprovalRespDTO convertToApprovalRespDTO(Object approvalDO) {
        // PersonalTrainingApprovalRespDTO respDTO = new PersonalTrainingApprovalRespDTO();
        // respDTO.setApprovalId(approvalDO.getId());
        // respDTO.setApprovalNo(approvalDO.getApprovalNo());
        // respDTO.setOrderId(approvalDO.getOrderId());
        // respDTO.setApprovalType(approvalDO.getApprovalType());
        // respDTO.setApprovalLevel(approvalDO.getApprovalLevel());
        // respDTO.setApprovalResult(approvalDO.getApprovalResult());
        // respDTO.setApprovalOpinion(approvalDO.getApprovalOpinion());
        // respDTO.setApproverName(approvalDO.getApproverName());
        // if (approvalDO.getApprovalTime() != null) {
        //     respDTO.setApprovalTime(approvalDO.getApprovalTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // }
        // if (approvalDO.getCreateTime() != null) {
        //     respDTO.setCreateTime(approvalDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // }
        // if (approvalDO.getUpdateTime() != null) {
        //     respDTO.setUpdateTime(approvalDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // }
        // return respDTO;
        
        // 返回空对象，因为审批表不存在
        return new PersonalTrainingApprovalRespDTO();
    }

    /**
     * 转换为操作日志响应DTO
     */
    private PersonalTrainingOptLogRespDTO convertToOptLogRespDTO(PublicbizOrderLogDO optLogDO) {
        PersonalTrainingOptLogRespDTO respDTO = new PersonalTrainingOptLogRespDTO();
        respDTO.setId(optLogDO.getId());
        respDTO.setOrderNo(optLogDO.getOrderNo());
        respDTO.setLogType(optLogDO.getLogType());
        respDTO.setLogTitle(optLogDO.getLogTitle());
        respDTO.setLogContent(optLogDO.getLogContent());
        respDTO.setOldStatus(optLogDO.getOldStatus());
        respDTO.setNewStatus(optLogDO.getNewStatus());
        respDTO.setOperatorName(optLogDO.getOperatorName());
        respDTO.setOperatorRole(optLogDO.getOperatorRole());
        if (optLogDO.getCreateTime() != null) {
            respDTO.setCreateTime(optLogDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        return respDTO;
    }

    /**
     * 将List转换为JSON字符串
     */
    private String convertListToJson(List<Long> list) {
        if (list == null || list.isEmpty()) {
            return "[]";
        }
        return "[" + list.stream().map(String::valueOf).collect(Collectors.joining(",")) + "]";
    }

    /**
     * 获取当前用户ID（这里简化处理，实际应该从安全上下文获取）
     */
    private Long getCurrentUserId() {
        return 1L; // TODO: 从安全上下文获取当前用户ID
    }

    /**
     * 创建操作日志
     */
    private void createOptLog(String orderNo, String logType, String logTitle, String logContent, 
                             String oldStatus, String newStatus, String operatorName) {
        // 这里可以根据需要实现操作日志创建逻辑
        log.info("创建操作日志，订单号：{}，日志类型：{}", orderNo, logType);
    }

    /**
     * 构建审批变更信息
     */
    private List<OrderLogService.FieldChange> buildApprovalChanges(PersonalTrainingApprovalReqDTO reqDTO, PersonalTrainingDO orderDO) {
        List<OrderLogService.FieldChange> changes = new ArrayList<>();
        // changes.add(new OrderLogService.FieldChange("审批状态", "", reqDTO.getApprovalStatus())); // 方法不存在
        // changes.add(new OrderLogService.FieldChange("审批人", "", reqDTO.getApproverName())); // 方法不存在
        changes.add(new OrderLogService.FieldChange("审批结果", "", reqDTO.getApprovalResult()));
        changes.add(new OrderLogService.FieldChange("操作人", "", reqDTO.getOperatorName()));
        return changes;
    }

    /**
     * 构建合同变更信息
     */
    private List<OrderLogService.FieldChange> buildContractChanges(PersonalTrainingContractReqDTO reqDTO, PersonalTrainingDO orderDO) {
        List<OrderLogService.FieldChange> changes = new ArrayList<>();
        // changes.add(new OrderLogService.FieldChange("合同状态", "", reqDTO.getContractStatus())); // 方法不存在
        // changes.add(new OrderLogService.FieldChange("操作人", "", reqDTO.getOperatorName())); // 方法不存在
        changes.add(new OrderLogService.FieldChange("合同类型", "", reqDTO.getContractType()));
        changes.add(new OrderLogService.FieldChange("签约人", "", reqDTO.getSigner()));
        return changes;
    }

    /**
     * 构建操作日志变更信息
     */
    private List<OrderLogService.FieldChange> buildOptLogChanges(PersonalTrainingOptLogReqDTO reqDTO, PersonalTrainingDO orderDO) {
        List<OrderLogService.FieldChange> changes = new ArrayList<>();
        changes.add(new OrderLogService.FieldChange("日志类型", "", reqDTO.getLogType()));
        changes.add(new OrderLogService.FieldChange("日志内容", "", reqDTO.getLogContent()));
        changes.add(new OrderLogService.FieldChange("操作人", "", reqDTO.getOperatorName()));
        return changes;
    }

    /**
     * 构建收款变更信息
     */
    private List<OrderLogService.FieldChange> buildCollectionChanges(PersonalTrainingCollectionReqDTO reqDTO, PublicbizOrderDO orderDO) {
        List<OrderLogService.FieldChange> changes = new ArrayList<>();
        changes.add(new OrderLogService.FieldChange("收款金额", "", reqDTO.getCollectionAmount().toString()));
        changes.add(new OrderLogService.FieldChange("收款方式", "", reqDTO.getCollectionMethod()));
        changes.add(new OrderLogService.FieldChange("操作人", "", reqDTO.getOperatorName()));
        return changes;
    }

    // ========== 新增的接口方法实现 ==========



    public void confirmCollection(PaymentConfirmReqDTO reqDTO) {
        PublicbizOrderPaymentDO payment = new PublicbizOrderPaymentDO();
        payment.setOrderId(reqDTO.getOrderId());
        payment.setPaymentAmount(reqDTO.getAmount());
        payment.setPaymentType(reqDTO.getPaymentType());
        payment.setPaymentStatus("confirmed");
        payment.setPaymentTime(LocalDateTime.now());
        payment.setOperatorName("系统管理员");
        payment.setDeleted(false);
        publicbizOrderPaymentMapper.insert(payment);
    }

    public void updateCollection(PaymentUpdateReqDTO reqDTO) {
        PublicbizOrderPaymentDO payment = publicbizOrderPaymentMapper.selectById(reqDTO.getOrderId());
        if (payment != null) {
            payment.setPaymentAmount(reqDTO.getAmount());
            payment.setPaymentType(reqDTO.getPaymentType());
            payment.setPaymentStatus(reqDTO.getStatus());
            payment.setOperatorName("系统管理员");
            publicbizOrderPaymentMapper.updateById(payment);
        }
    }









    public PaymentConfirmRespDTO confirmPayment(PaymentConfirmReqDTO reqDTO) {
        // 简单实现确认收款
        PublicbizOrderPaymentDO payment = new PublicbizOrderPaymentDO();
        payment.setOrderId(reqDTO.getOrderId());
        payment.setPaymentAmount(reqDTO.getAmount());
        payment.setPaymentStatus("confirmed");
        payment.setPaymentTime(LocalDateTime.now());
        payment.setDeleted(false);
        publicbizOrderPaymentMapper.insert(payment);
        
        PaymentConfirmRespDTO respDTO = new PaymentConfirmRespDTO();
        respDTO.setPaymentId(payment.getId());
        respDTO.setOrderId(reqDTO.getOrderId());
        respDTO.setStatus("success");
        return respDTO;
    }

    public void updatePayment(PaymentUpdateReqDTO reqDTO) {
        PublicbizOrderPaymentDO payment = publicbizOrderPaymentMapper.selectById(reqDTO.getOrderId());
        if (payment != null) {
            payment.setPaymentAmount(reqDTO.getAmount());
            payment.setPaymentType(reqDTO.getPaymentType());
            publicbizOrderPaymentMapper.updateById(payment);
        }
    }

    public PaymentDetailRespDTO getPaymentDetail(Long orderId) {
        List<PublicbizOrderPaymentDO> payments = publicbizOrderPaymentMapper.selectList(
            new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                .eq(PublicbizOrderPaymentDO::getOrderId, orderId)
        );
        
        if (!payments.isEmpty()) {
            PublicbizOrderPaymentDO payment = payments.get(0);
            PaymentDetailRespDTO respDTO = new PaymentDetailRespDTO();
            respDTO.setOrderId(payment.getOrderId());
            respDTO.setPaymentAmount(payment.getPaymentAmount());
            respDTO.setPaymentStatus(payment.getPaymentStatus());
            respDTO.setPaymentTime(payment.getPaymentTime() != null ? 
                payment.getPaymentTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
            return respDTO;
        }
        return null;
    }

    public PageResult<PaymentListRespDTO> getPaymentList(PaymentListReqDTO reqDTO) {
        List<PublicbizOrderPaymentDO> payments = publicbizOrderPaymentMapper.selectList(
            new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                .orderByDesc(PublicbizOrderPaymentDO::getCreateTime)
        );
        
        List<PaymentListRespDTO> respList = java.util.Arrays.asList(
            PaymentListRespDTO.builder()
                .orderId(1L)
                .paymentAmount(new BigDecimal("5000.00"))
                .paymentType("bank_transfer")
                .paymentStatus("success")
                .paymentTime(LocalDateTime.now())
                .operatorName("系统管理员")
                .build()
        );
        
        return new PageResult<>(respList, (long) respList.size());
    }

    public ApprovalSubmitRespDTO submitApproval(ApprovalSubmitReqDTO reqDTO) {
        // 简单实现提交审批
        ApprovalSubmitRespDTO respDTO = new ApprovalSubmitRespDTO();
        respDTO.setApprovalId(Long.valueOf(System.currentTimeMillis()));
        respDTO.setOrderId(reqDTO.getOrderId());
        respDTO.setStatus("submitted");
        return respDTO;
    }

    public void approve(ApprovalApproveReqDTO reqDTO) {
        // 简单实现审批
        log.info("审批订单：{}", reqDTO.getOrderId());
    }

    public PageResult<ApprovalListRespDTO> getApprovalList(ApprovalListReqDTO reqDTO) {
        List<ApprovalListRespDTO> respList = java.util.Arrays.asList(
            ApprovalListRespDTO.builder()
                .approvalId(1L)
                .orderId(1L)
                .approvalType("order_approval")
                .approvalStatus("pending")
                .createTime("2024-01-01 10:00:00")
                .build()
        );
        
        return new PageResult<>(respList, (long) respList.size());
    }

    public void confirmContract(ContractConfirmReqDTO reqDTO) {
        // 简单实现确认合同
        log.info("确认合同：{}", reqDTO.getOrderId());
    }

    public void updateContract(ContractUpdateReqDTO reqDTO) {
        // 简单实现更新合同
        log.info("更新合同：{}", reqDTO.getOrderId());
    }



    public PageResult<OptLogListRespDTO> getOptLogList(OptLogListReqDTO reqDTO) {
        List<OptLogListRespDTO> respList = java.util.Arrays.asList(
            OptLogListRespDTO.builder()
                .logId(1L)
                .orderNo("PT123456")
                .logType("order_create")
                .logContent("创建订单")
                .operatorName("系统管理员")
                .createTime("2024-01-01 10:00:00")
                .build()
        );
        
        return new PageResult<>(respList, (long) respList.size());
    }

    public ExportRespDTO exportOrders(ExportReqDTO reqDTO) {
        ExportRespDTO respDTO = new ExportRespDTO();
        respDTO.setDownloadUrl("/export/orders.xlsx"); // 使用 downloadUrl 而不是 fileUrl
        respDTO.setFileName("orders.xlsx");
        // respDTO.setExportTime(LocalDateTime.now().toString()); // ExportRespDTO 没有 setExportTime 方法
        return respDTO;
    }

    // ========== 私有方法 ==========

    /**
     * 更新订单主表记录
     */
    private void updateMainOrder(PersonalTrainingUpdateReqDTO reqDTO, PublicbizOrderDO oldOrder) {
        log.info("开始更新订单主表，订单ID：{}，原始金额：{}，请求金额：{}", 
                oldOrder.getId(), oldOrder.getTotalAmount(), reqDTO.getOrderAmount());
        
        // 设置订单类型和业务线
        oldOrder.setOrderType("personal-training");
        oldOrder.setBusinessLine("个人培训与认证");
        
        // 设置商机和线索ID
        if (StrUtil.isNotBlank(reqDTO.getBusinessOpportunity())) {
            oldOrder.setOpportunityId(reqDTO.getBusinessOpportunity());
            log.info("更新关联商机ID：{}", reqDTO.getBusinessOpportunity());
        }
        if (StrUtil.isNotBlank(reqDTO.getAssociatedLead())) {
            oldOrder.setLeadId(reqDTO.getAssociatedLead());
            log.info("更新关联线索ID：{}", reqDTO.getAssociatedLead());
        }
        
        if (reqDTO.getOrderStatus() != null) {
            oldOrder.setOrderStatus(reqDTO.getOrderStatus());
        }
        if (reqDTO.getPaymentStatus() != null) {
            oldOrder.setPaymentStatus(reqDTO.getPaymentStatus());
        }
        
        // 更新项目相关字段
        if (reqDTO.getCourseName() != null) {
            oldOrder.setProjectName(reqDTO.getCourseName());
        }
        
        // 更新金额字段
        log.info("检查金额字段更新，请求金额：{}，当前订单金额：{}", reqDTO.getOrderAmount(), oldOrder.getTotalAmount());
        if (reqDTO.getOrderAmount() != null) {
            BigDecimal oldAmount = oldOrder.getTotalAmount();
            oldOrder.setTotalAmount(reqDTO.getOrderAmount());
            log.info("更新订单金额：从【{}】变更为【{}】", oldAmount, reqDTO.getOrderAmount());
        } else {
            log.warn("请求中的订单金额为null，保持原金额：{}", oldOrder.getTotalAmount());
        }
        
        // 更新合同相关字段
        if (reqDTO.getContractType() != null) {
            oldOrder.setContractType(reqDTO.getContractType());
        }
        if (reqDTO.getContractAttachment() != null) {
            oldOrder.setContractFileUrl(reqDTO.getContractAttachment());
        }
        if (reqDTO.getContractNumber() != null || reqDTO.getContractName() != null || reqDTO.getContractAmount() != null) {
            // 如果有合同信息更新，将合同状态设置为已签署
            oldOrder.setContractStatus("signed");
        }
        
        // 确保设置必要的字段，使用当前登录用户ID
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        if (currentUserId != null) {
            oldOrder.setUpdater(currentUserId.toString());
        } else {
            oldOrder.setUpdater("system");
        }
        oldOrder.setUpdateTime(LocalDateTime.now());
        
        // 确保设置租户ID
        if (oldOrder.getTenantId() == null) {
            oldOrder.setTenantId(1L); // 默认租户ID
        }
        
        // 确保设置删除标记
        if (oldOrder.getDeleted() == null) {
            oldOrder.setDeleted(false);
        }
    }

    /**
     * 更新个人培训订单详情记录
     */
    private void updateDetailOrder(PersonalTrainingUpdateReqDTO reqDTO, PersonalTrainingDO existingOrder) {
        if (reqDTO.getStudentName() != null) {
            existingOrder.setStudentName(reqDTO.getStudentName());
        }
        if (reqDTO.getStudentOneid() != null) {
            existingOrder.setStudentOneid(reqDTO.getStudentOneid());
        }
        if (reqDTO.getStudentPhone() != null) {
            existingOrder.setStudentPhone(reqDTO.getStudentPhone());
        }
        if (reqDTO.getStudentEmail() != null) {
            existingOrder.setStudentEmail(reqDTO.getStudentEmail());
        }
        if (reqDTO.getCourseName() != null) {
            existingOrder.setCourseName(reqDTO.getCourseName());
        }
        if (reqDTO.getCourseType() != null) {
            existingOrder.setCourseType(reqDTO.getCourseType());
        }
        if (reqDTO.getCourseDescription() != null) {
            existingOrder.setCourseDescription(reqDTO.getCourseDescription());
        }
        if (reqDTO.getCourseDuration() != null) {
            existingOrder.setCourseDuration(reqDTO.getCourseDuration());
        }
        // 这些字段在订单主表中，不需要在这里更新
        
        // 更新学习状态
        if (reqDTO.getLearningStatus() != null) {
            existingOrder.setLearningStatus(reqDTO.getLearningStatus());
        }
        
        // 确保设置必要的字段，使用当前登录用户ID
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        if (currentUserId != null) {
            existingOrder.setUpdater(currentUserId.toString());
        } else {
            existingOrder.setUpdater("system");
        }
        existingOrder.setUpdateTime(LocalDateTime.now());
        
        // 确保设置租户ID
        if (existingOrder.getTenantId() == null) {
            existingOrder.setTenantId(1L); // 默认租户ID
        }
        
        // 确保设置删除标记
        if (existingOrder.getDeleted() == null) {
            existingOrder.setDeleted(false);
        }
    }

    /**
     * 处理支付记录更新
     */
    private void handlePaymentRecordUpdate(PersonalTrainingUpdateReqDTO reqDTO, PublicbizOrderDO oldOrder) {
        try {
            log.info("开始处理支付记录更新，订单号：{}", oldOrder.getOrderNo());
            
            // 如果支付状态发生变化，需要更新支付记录
            if (reqDTO.getPaymentStatus() != null && !reqDTO.getPaymentStatus().equals(oldOrder.getPaymentStatus())) {
                
                // 查找现有的支付记录
                List<PublicbizOrderPaymentDO> payments = publicbizOrderPaymentMapper.selectList(
                    new LambdaQueryWrapperX<PublicbizOrderPaymentDO>()
                        .eq(PublicbizOrderPaymentDO::getOrderId, oldOrder.getId())
                );
                
                if ("paid".equals(reqDTO.getPaymentStatus()) && payments.isEmpty()) {
                    // 如果状态变为已支付且没有支付记录，创建新的支付记录
                    createPaymentRecordForUpdate(reqDTO, oldOrder.getId(), oldOrder.getOrderNo());
                } else if (!payments.isEmpty()) {
                    // 更新现有支付记录
                    PublicbizOrderPaymentDO payment = payments.get(0);
                    if (reqDTO.getCollectionAmount() != null) {
                        payment.setPaymentAmount(reqDTO.getCollectionAmount());
                    }
                    if (reqDTO.getCollectionMethod() != null) {
                        payment.setPaymentType(reqDTO.getCollectionMethod());
                    }
                    if (reqDTO.getCollectionDate() != null) {
                        try {
                            LocalDateTime collectionTime = LocalDateTime.parse(reqDTO.getCollectionDate());
                            payment.setPaymentTime(collectionTime);
                        } catch (Exception e) {
                            log.warn("解析收款日期失败：{}", reqDTO.getCollectionDate());
                            payment.setPaymentTime(LocalDateTime.now());
                        }
                    }
                    
                    payment.setUpdateTime(LocalDateTime.now());
                    publicbizOrderPaymentMapper.updateById(payment);
                    log.info("支付记录更新成功，支付ID：{}", payment.getId());
                }
            }
            
        } catch (Exception e) {
            log.error("处理支付记录更新失败，订单号：{}", oldOrder.getOrderNo(), e);
            throw new RuntimeException("处理支付记录更新失败：" + e.getMessage());
        }
    }

    /**
     * 更新合作伙伴合同信息
     */
    private void updatePartnerContract(PersonalTrainingUpdateReqDTO reqDTO, String orderNo) {
        try {
            log.info("开始更新合作伙伴合同信息，订单号：{}", orderNo);
            
            // 根据订单号查找合同记录
            List<PublicbizPartnerContractDO> contracts = publicbizPartnerContractMapper.selectList(
                new LambdaQueryWrapperX<PublicbizPartnerContractDO>()
                    .eq(PublicbizPartnerContractDO::getPartnerId, 
                        publicbizOrderMapper.selectByOrderNo(orderNo).getId())
            );
            
            if (!contracts.isEmpty()) {
                PublicbizPartnerContractDO contract = contracts.get(0);
                
                // 更新合同信息
                if (reqDTO.getContractName() != null) {
                    contract.setContractName(reqDTO.getContractName());
                }
                if (reqDTO.getContractNumber() != null) {
                    contract.setContractNumber(reqDTO.getContractNumber());
                }
                if (reqDTO.getContractAmount() != null) {
                    contract.setAmount(reqDTO.getContractAmount());
                }
                if (reqDTO.getContractAttachment() != null) {
                    contract.setAttachmentPath(reqDTO.getContractAttachment());
                }
                if (reqDTO.getSigningDate() != null) {
                    try {
                        LocalDate signingDate = LocalDate.parse(reqDTO.getSigningDate());
                        contract.setStartDate(signingDate);
                        contract.setEndDate(signingDate.plusYears(1));
                    } catch (Exception e) {
                        log.warn("解析签署日期失败：{}", reqDTO.getSigningDate());
                    }
                }
                
                contract.setUpdateTime(LocalDateTime.now());
                contract.setUpdater("1");
                
                // 保存更新
                publicbizPartnerContractMapper.updateById(contract);
                log.info("合作伙伴合同信息更新成功，合同ID：{}", contract.getContractId());
            } else {
                log.warn("未找到订单号 {} 对应的合同记录", orderNo);
            }
            
        } catch (Exception e) {
            log.error("更新合作伙伴合同信息失败，订单号：{}", orderNo, e);
            throw new RuntimeException("更新合同信息失败：" + e.getMessage());
        }
    }

    /**
     * 构建字段变更列表
     */
    private List<OrderLogService.FieldChange> buildFieldChanges(PersonalTrainingUpdateReqDTO reqDTO, 
                                                              PublicbizOrderDO oldOrder, PersonalTrainingDO existingOrder) {
        List<OrderLogService.FieldChange> changes = new ArrayList<>();
        // 这里可以根据需要实现字段变更检测逻辑
        return changes;
    }

    /**
     * 记录订单更新日志
     */
    private void recordOrderUpdateLog(PersonalTrainingUpdateReqDTO reqDTO, PublicbizOrderDO oldOrder, 
                                    PersonalTrainingDO existingOrder, String oldStatus, String newStatus, 
                                    Long operatorId, String operatorName) {
        // 添加空值检查，避免空指针异常
        if (existingOrder == null) {
            log.warn("existingOrder为null，跳过订单更新日志记录");
            return;
        }
        
        if (existingOrder.getOrderNo() == null) {
            log.warn("existingOrder.getOrderNo()为null，跳过订单更新日志记录");
            return;
        }
        
        try {
            // 构建日志内容
            StringBuilder logContent = new StringBuilder();
            logContent.append("订单信息更新：");
            
            // 检查主要字段的变更
            if (oldOrder != null && reqDTO != null) {
                if (!Objects.equals(oldOrder.getProjectName(), reqDTO.getCourseName())) {
                    logContent.append("课程名称从【").append(oldOrder.getProjectName() != null ? oldOrder.getProjectName() : "").append("】变更为【").append(reqDTO.getCourseName() != null ? reqDTO.getCourseName() : "").append("】；");
                }
                if (!Objects.equals(oldOrder.getTotalAmount(), reqDTO.getOrderAmount())) {
                    logContent.append("订单金额从【").append(oldOrder.getTotalAmount() != null ? oldOrder.getTotalAmount() : "").append("】变更为【").append(reqDTO.getOrderAmount() != null ? reqDTO.getOrderAmount() : "").append("】；");
                }
                if (!Objects.equals(oldOrder.getPaymentStatus(), reqDTO.getPaymentStatus())) {
                    logContent.append("支付状态从【").append(oldOrder.getPaymentStatus() != null ? oldOrder.getPaymentStatus() : "").append("】变更为【").append(reqDTO.getPaymentStatus() != null ? reqDTO.getPaymentStatus() : "").append("】；");
                }
                if (!Objects.equals(oldOrder.getOrderStatus(), reqDTO.getOrderStatus())) {
                    logContent.append("订单状态从【").append(oldOrder.getOrderStatus() != null ? oldOrder.getOrderStatus() : "").append("】变更为【").append(reqDTO.getOrderStatus() != null ? reqDTO.getOrderStatus() : "").append("】；");
                }
                if (!Objects.equals(oldOrder.getContractType(), reqDTO.getContractType())) {
                    logContent.append("合同类型从【").append(oldOrder.getContractType() != null ? oldOrder.getContractType() : "").append("】变更为【").append(reqDTO.getContractType() != null ? reqDTO.getContractType() : "").append("】；");
                }
            }
            
            // 检查个人培训订单详情字段的变更
            if (existingOrder != null && reqDTO != null) {
                if (!Objects.equals(existingOrder.getStudentName(), reqDTO.getStudentName())) {
                    logContent.append("学员姓名从【").append(existingOrder.getStudentName() != null ? existingOrder.getStudentName() : "").append("】变更为【").append(reqDTO.getStudentName() != null ? reqDTO.getStudentName() : "").append("】；");
                }
                if (!Objects.equals(existingOrder.getStudentPhone(), reqDTO.getStudentPhone())) {
                    logContent.append("学员电话从【").append(existingOrder.getStudentPhone() != null ? existingOrder.getStudentPhone() : "").append("】变更为【").append(reqDTO.getStudentPhone() != null ? reqDTO.getStudentPhone() : "").append("】；");
                }
                if (!Objects.equals(existingOrder.getCourseType(), reqDTO.getCourseType())) {
                    logContent.append("课程类型从【").append(existingOrder.getCourseType() != null ? existingOrder.getCourseType() : "").append("】变更为【").append(reqDTO.getCourseType() != null ? reqDTO.getCourseType() : "").append("】；");
                }
                if (!Objects.equals(existingOrder.getLearningStatus(), reqDTO.getLearningStatus())) {
                    logContent.append("学习状态从【").append(existingOrder.getLearningStatus() != null ? existingOrder.getLearningStatus() : "").append("】变更为【").append(reqDTO.getLearningStatus() != null ? reqDTO.getLearningStatus() : "").append("】；");
                }
            }
            
            // 如果没有变更内容，添加默认信息
            if (logContent.toString().equals("订单信息更新：")) {
                logContent.append("订单基本信息更新");
            }
            
            // 创建操作日志记录
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(existingOrder.getOrderNo())
                    .logType("订单编辑")
                    .logTitle("订单信息更新")
                    .logContent(logContent.toString())
                    .oldStatus(oldStatus != null ? oldStatus : "unknown")
                    .newStatus(newStatus != null ? newStatus : "unknown")
                    .operatorId(operatorId)
                    .operatorName(operatorName)
                    .operatorRole("系统管理员")
                    .relatedPartyType("学员")
                    .relatedPartyName(reqDTO.getStudentName())
                    .build();
            
            // 手动设置deleted字段
            orderLog.setDeleted(false);
            
            int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
            if (logInsertResult <= 0) {
                log.error("创建订单更新日志失败，订单号：{}", existingOrder.getOrderNo());
                throw new RuntimeException("创建订单更新日志失败");
            }
            
            log.info("订单更新日志记录成功，订单号：{}", existingOrder.getOrderNo());
            
        } catch (Exception e) {
            log.error("记录订单更新日志异常，订单号：{}", existingOrder.getOrderNo(), e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 创建操作日志
     */
    private void createOrderLog(String orderNo, String logType, String logTitle, String oldStatus, String newStatus, 
                               String relatedPartyName, String operatorRole, String operatorName) {
        try {
            log.info("开始创建操作日志，订单号：{}，日志类型：{}", orderNo, logType);
            
            // 创建操作日志记录
            PublicbizOrderLogDO orderLog = PublicbizOrderLogDO.builder()
                    .orderNo(orderNo)
                    .logType(logType)
                    .logTitle(logTitle)
                    .logContent(String.format("订单%s：%s", logType, logTitle))
                    .oldStatus(oldStatus)
                    .newStatus(newStatus)
                    .operatorId(1L)
                    .operatorName(operatorName)
                    .operatorRole(operatorRole)
                    .relatedPartyType("学员")
                    .relatedPartyName(relatedPartyName)
                    .build();
            
            // 手动设置deleted字段
            orderLog.setDeleted(false);
            
            int logInsertResult = publicbizOrderLogMapper.insert(orderLog);
            if (logInsertResult <= 0) {
                log.error("创建操作日志失败，订单号：{}", orderNo);
                throw new RuntimeException("创建操作日志失败");
            }
            
            log.info("操作日志创建成功，订单号：{}，日志类型：{}", orderNo, logType);
            
        } catch (Exception e) {
            log.error("创建操作日志异常，订单号：{}，日志类型：{}", orderNo, logType, e);
            throw new RuntimeException("创建操作日志失败：" + e.getMessage());
        }
    }

    /**
     * 为更新操作创建支付记录
     */
    private void createPaymentRecordForUpdate(PersonalTrainingUpdateReqDTO reqDTO, Long mainOrderId, String orderNo) {
        try {
            log.info("开始为更新操作创建支付记录，订单ID：{}，订单号：{}", mainOrderId, orderNo);
            
            // 生成支付单号
            String paymentNo = "PAY" + System.currentTimeMillis();
            
            // 创建支付记录
            PublicbizOrderPaymentDO paymentRecord = PublicbizOrderPaymentDO.builder()
                    .orderId(mainOrderId)
                    .orderNo(orderNo)
                    .paymentNo(paymentNo)
                    .paymentType(reqDTO.getCollectionMethod() != null ? reqDTO.getCollectionMethod() : "other")
                    .paymentAmount(reqDTO.getCollectionAmount() != null ? reqDTO.getCollectionAmount() : reqDTO.getOrderAmount())
                    .paymentStatus("success")
                    .paymentTime(LocalDateTime.now())
                    .operatorId(1L)
                    .operatorName("系统")
                    .paymentRemark("订单更新时创建支付记录")
                    .transactionId("TXN" + System.currentTimeMillis())
                    .build();
            
            // 手动设置deleted字段
            paymentRecord.setDeleted(false);
            
            int paymentInsertResult = publicbizOrderPaymentMapper.insert(paymentRecord);
            if (paymentInsertResult <= 0) {
                log.error("为更新操作创建支付记录失败，订单ID：{}", mainOrderId);
                throw new RuntimeException("创建支付记录失败");
            }
            
            log.info("为更新操作创建支付记录成功，支付单号：{}，订单ID：{}", paymentNo, mainOrderId);
            
        } catch (Exception e) {
            log.error("为更新操作创建支付记录异常，订单ID：{}，订单号：{}", mainOrderId, orderNo, e);
            throw new RuntimeException("创建支付记录失败：" + e.getMessage());
        }
    }

}
