package cn.bztmaster.cnt.module.publicbiz.dal.mysql.evaluation;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.evaluation.OrderEvaluationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 订单评价 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OrderEvaluationMapper extends BaseMapperX<OrderEvaluationDO> {

    /**
     * 根据订单ID查询评价信息
     *
     * @param orderId 订单ID
     * @return 评价信息
     */
    OrderEvaluationDO selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单号查询评价信息
     *
     * @param orderNo 订单号
     * @return 评价信息
     */
    OrderEvaluationDO selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据服务人员ID查询评价列表
     *
     * @param practitionerId 服务人员ID
     * @return 评价列表
     */
    java.util.List<OrderEvaluationDO> selectByPractitionerId(@Param("practitionerId") Long practitionerId);

    /**
     * 更新评价信息（排除生成列rating）
     *
     * @param evaluation 评价信息
     * @return 更新结果
     */
    int updateEvaluationExcludeRating(OrderEvaluationDO evaluation);
}
