package cn.bztmaster.cnt.module.publicbiz.dal.mysql.order;

import cn.bztmaster.cnt.framework.mybatis.core.mapper.BaseMapperX;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.DomesticTaskOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 家政服务订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DomesticTaskOrderMapper extends BaseMapperX<DomesticTaskOrderDO> {

    /**
     * 根据订单ID查询家政服务订单详情
     *
     * @param orderId 订单ID
     * @return 家政服务订单详情
     */
    DomesticTaskOrderDO selectByOrderId(@Param("orderId") Long orderId);

    /**
     * 根据订单号查询家政服务订单详情
     *
     * @param orderNo 订单号
     * @return 家政服务订单详情
     */
    DomesticTaskOrderDO selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据客户电话查询订单列表
     *
     * @param customerPhone 客户电话
     * @return 订单列表
     */
    List<DomesticTaskOrderDO> selectByCustomerPhone(@Param("customerPhone") String customerPhone);

    /**
     * 根据服务人员OneID查询订单列表
     *
     * @param practitionerOneid 服务人员OneID
     * @return 订单列表
     */
    List<DomesticTaskOrderDO> selectByPractitionerOneid(@Param("practitionerOneid") String practitionerOneid);

    /**
     * 根据服务机构ID查询订单列表
     *
     * @param agencyId 服务机构ID
     * @return 订单列表
     */
    List<DomesticTaskOrderDO> selectByAgencyId(@Param("agencyId") Long agencyId);

    /**
     * 根据日期范围查询订单列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 订单列表
     */
    List<DomesticTaskOrderDO> selectByDateRange(@Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate);

    /**
     * 更新任务统计信息
     *
     * @param orderId         订单ID
     * @param completedTasks  已完成任务数
     * @param totalTasks      总任务数
     * @param taskProgress    任务进度
     * @return 更新记录数
     */
    int updateTaskStatistics(@Param("orderId") Long orderId,
                            @Param("completedTasks") Integer completedTasks,
                            @Param("totalTasks") Integer totalTasks,
                            @Param("taskProgress") java.math.BigDecimal taskProgress);
}
