package cn.bztmaster.cnt.module.publicbiz.service.agency.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.LeaveApprovalRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.domestic.DomesticTaskDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.domestic.DomesticTaskMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.agency.AgencyLeaveApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AgencyLeaveApprovalServiceImpl implements AgencyLeaveApprovalService {

    @Resource
    private WorkOrderMapper workOrderMapper;

    @Resource
    private DomesticTaskMapper domesticTaskMapper;

    @Override
    public PageResult<LeaveApprovalListRespVO> getLeaveRequests(Long agencyId, String status, Integer page, Integer size) {
        // 构建查询条件
        LambdaQueryWrapperX<WorkOrderDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(WorkOrderDO::getAgencyId, agencyId)
                .in(WorkOrderDO::getWorkOrderType, "take_leave", "leave_adjustment")
                .eq(WorkOrderDO::getDeleted, false);

        // 状态过滤
        if ("pending".equals(status)) {
            wrapper.eq(WorkOrderDO::getWorkOrderStatus, "pending");
        } else if ("approved".equals(status)) {
            wrapper.eq(WorkOrderDO::getWorkOrderStatus, "approved");
        } else if ("rejected".equals(status)) {
            wrapper.eq(WorkOrderDO::getWorkOrderStatus, "rejected");
        }

        // 创建分页参数
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(page);
        pageParam.setPageSize(size);

        // 分页查询
        PageResult<WorkOrderDO> pageResult = workOrderMapper.selectPage(pageParam, wrapper);
        
        // 转换为VO
        List<LeaveApprovalListRespVO> list = pageResult.getList().stream()
                .map(this::convertToLeaveApprovalListRespVO)
                .collect(Collectors.toList());

        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public LeaveApprovalDetailRespVO getLeaveRequestDetail(Long agencyId, Long requestId) {
        // 查询工单信息
        WorkOrderDO workOrder = workOrderMapper.selectById(requestId);
        if (workOrder == null || !workOrder.getAgencyId().equals(agencyId)) {
            throw new RuntimeException("工单不存在");
        }

        // 查询受影响订单
        List<LeaveApprovalDetailRespVO.AffectedOrderVO> affectedOrders = getAffectedOrdersByWorkOrder(workOrder);

        // 构建响应
        LeaveApprovalDetailRespVO resp = new LeaveApprovalDetailRespVO();
        resp.setRequestInfo(convertToRequestInfo(workOrder));
        resp.setAffectedOrders(affectedOrders);

        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LeaveApprovalRespVO approveLeaveRequest(Long agencyId, Long requestId, LeaveApprovalReqVO reqVO) {
        // 查询工单信息
        WorkOrderDO workOrder = workOrderMapper.selectById(requestId);
        if (workOrder == null || !workOrder.getAgencyId().equals(agencyId)) {
            throw new RuntimeException("工单不存在");
        }

        // 检查状态是否允许操作
        if (!"pending".equals(workOrder.getWorkOrderStatus())) {
            throw new RuntimeException("工单状态不允许操作");
        }

        // 更新工单状态
        String newStatus = "approve".equals(reqVO.getAction()) ? "approved" : "rejected";
        workOrder.setWorkOrderStatus(newStatus);
        workOrder.setStatus("approve".equals(reqVO.getAction()) ? 1 : 2);
        workOrder.setApproveTime(LocalDateTime.now());
        workOrder.setApproveRemark(reqVO.getRemark());
        workOrder.setAssigneeId(reqVO.getOperatorId());
        workOrder.setAssigneeName(reqVO.getOperatorName());

        workOrderMapper.updateById(workOrder);

        // 构建响应
        LeaveApprovalRespVO resp = new LeaveApprovalRespVO();
        resp.setRequestId(requestId);
        resp.setNewStatus(newStatus);
        resp.setApproveTime(workOrder.getApproveTime());

        return resp;
    }

    @Override
    public LeaveApprovalDetailRespVO.AffectedOrderVO[] getAffectedOrders(Long agencyId, Long requestId) {
        // 查询工单信息
        WorkOrderDO workOrder = workOrderMapper.selectById(requestId);
        if (workOrder == null || !workOrder.getAgencyId().equals(agencyId)) {
            throw new RuntimeException("工单不存在");
        }

        List<LeaveApprovalDetailRespVO.AffectedOrderVO> affectedOrders = getAffectedOrdersByWorkOrder(workOrder);
        return affectedOrders.toArray(new LeaveApprovalDetailRespVO.AffectedOrderVO[0]);
    }

    /**
     * 转换为请假申请列表响应VO
     */
    private LeaveApprovalListRespVO convertToLeaveApprovalListRespVO(WorkOrderDO workOrder) {
        LeaveApprovalListRespVO vo = new LeaveApprovalListRespVO();
        vo.setId(workOrder.getId());
        vo.setWorkOrderNo(workOrder.getWorkOrderNo());
        vo.setAuntOneId(workOrder.getAuntOneid());
        vo.setAuntName(workOrder.getAuntName());
        vo.setLeaveType(workOrder.getLeaveType());
        vo.setLeaveTypeText(workOrder.getLeaveType() != null && workOrder.getLeaveType() == 1 ? "事假" : "调休");
        vo.setStartTime(workOrder.getStartTime());
        vo.setEndTime(workOrder.getEndTime());
        vo.setDurationDays(workOrder.getDurationDays() != null ? workOrder.getDurationDays().doubleValue() : null);
        vo.setDurationHours(workOrder.getDurationHours() != null ? workOrder.getDurationHours().doubleValue() : null);
        vo.setReason(workOrder.getWorkOrderContent());
        vo.setWorkOrderStatus(workOrder.getWorkOrderStatus());
        vo.setCreateTime(workOrder.getCreateTime());

        // 计算受影响订单数量
        if (workOrder.getStartTime() != null && workOrder.getEndTime() != null) {
            int count = countAffectedOrders(workOrder.getAuntOneid(), workOrder.getStartTime().toLocalDate(), workOrder.getEndTime().toLocalDate());
            vo.setAffectedOrdersCount(count);
        } else {
            vo.setAffectedOrdersCount(0);
        }

        return vo;
    }

    /**
     * 转换为申请信息
     */
    private LeaveApprovalDetailRespVO.RequestInfo convertToRequestInfo(WorkOrderDO workOrder) {
        LeaveApprovalDetailRespVO.RequestInfo info = new LeaveApprovalDetailRespVO.RequestInfo();
        info.setId(workOrder.getId());
        info.setWorkOrderNo(workOrder.getWorkOrderNo());
        info.setAuntOneId(workOrder.getAuntOneid());
        info.setAuntName(workOrder.getAuntName());
        info.setLeaveType(workOrder.getLeaveType());
        info.setLeaveTypeText(workOrder.getLeaveType() != null && workOrder.getLeaveType() == 1 ? "事假" : "调休");
        info.setStartTime(workOrder.getStartTime());
        info.setEndTime(workOrder.getEndTime());
        info.setDurationDays(workOrder.getDurationDays() != null ? workOrder.getDurationDays().doubleValue() : null);
        info.setDurationHours(workOrder.getDurationHours() != null ? workOrder.getDurationHours().doubleValue() : null);
        info.setReason(workOrder.getWorkOrderContent());
        info.setWorkOrderStatus(workOrder.getWorkOrderStatus());
        info.setCreateTime(workOrder.getCreateTime());
        return info;
    }

    /**
     * 根据工单查询受影响订单
     */
    private List<LeaveApprovalDetailRespVO.AffectedOrderVO> getAffectedOrdersByWorkOrder(WorkOrderDO workOrder) {
        if (workOrder.getStartTime() == null || workOrder.getEndTime() == null) {
            return new ArrayList<>();
        }

        LocalDate startDate = workOrder.getStartTime().toLocalDate();
        LocalDate endDate = workOrder.getEndTime().toLocalDate();

        // 查询受影响的任务
        List<DomesticTaskDO> tasks = domesticTaskMapper.selectByAuntOneIdAndDateRange(
                workOrder.getAuntOneid(), startDate, endDate);

        return tasks.stream()
                .filter(task -> !"cancelled".equals(task.getTaskStatus()) && !"completed".equals(task.getTaskStatus()))
                .map(this::convertToAffectedOrderVO)
                .collect(Collectors.toList());
    }

    /**
     * 转换为受影响订单VO
     */
    private LeaveApprovalDetailRespVO.AffectedOrderVO convertToAffectedOrderVO(DomesticTaskDO task) {
        LeaveApprovalDetailRespVO.AffectedOrderVO vo = new LeaveApprovalDetailRespVO.AffectedOrderVO();
        vo.setOrderId(task.getId());
        vo.setOrderNo(task.getTaskNo());
        vo.setOrderType("package".equals(task.getTaskType()) ? "package" : "single");
        vo.setOrderTypeText("package".equals(task.getTaskType()) ? "套餐订单" : "单次服务");
        vo.setServiceName(task.getServiceCategoryName() != null ? task.getServiceCategoryName() : task.getTaskName());
        vo.setProgress("1/1次"); // 简化处理，实际应该根据订单类型计算进度
        vo.setCustomerName(task.getCustomerName());
        vo.setCustomerPhone(maskPhone(task.getCustomerPhone()));
        vo.setAddress(task.getServiceAddress());
        vo.setServiceTime(task.getScheduleDate() != null ? task.getScheduleDate().atStartOfDay() : null);
        
        // 计算服务结束时间
        if (task.getScheduleDate() != null && task.getDuration() != null) {
            try {
                // 尝试解析时长字符串，如 "2小时" -> 2
                String durationStr = task.getDuration();
                if (durationStr.contains("小时")) {
                    String hoursStr = durationStr.replace("小时", "").trim();
                    int hours = Integer.parseInt(hoursStr);
                    vo.setServiceEndTime(task.getScheduleDate().atStartOfDay().plusHours(hours));
                }
            } catch (NumberFormatException e) {
                // 如果解析失败，不设置结束时间
                log.warn("无法解析任务时长: {}", task.getDuration());
            }
        }
        
        return vo;
    }

    /**
     * 统计受影响订单数量
     */
    private int countAffectedOrders(String auntOneId, LocalDate startDate, LocalDate endDate) {
        List<DomesticTaskDO> tasks = domesticTaskMapper.selectByAuntOneIdAndDateRange(auntOneId, startDate, endDate);
        return (int) tasks.stream()
                .filter(task -> !"cancelled".equals(task.getTaskStatus()) && !"completed".equals(task.getTaskStatus()))
                .count();
    }

    /**
     * 手机号脱敏
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) return phone;
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
}
