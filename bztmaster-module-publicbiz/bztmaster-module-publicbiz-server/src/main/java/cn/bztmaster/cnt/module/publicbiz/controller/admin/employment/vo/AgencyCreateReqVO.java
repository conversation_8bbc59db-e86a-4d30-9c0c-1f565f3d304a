package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.mzt.logapi.starter.annotation.DiffLogField;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构新增请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构新增请求 VO")
@Data
public class AgencyCreateReqVO {

    @Schema(description = "机构名称", example = "测试机构")
    @NotBlank(message = "机构名称不能为空")
    @DiffLogField(name = "机构名称")
    private String agencyName;

    @Schema(description = "机构简称", example = "测试")
    private String agencyShortName;

    @Schema(description = "机构编号", example = "AG001")
    @NotBlank(message = "机构编号不能为空")
    private String agencyNo;

    @Schema(description = "机构类型", example = "cooperation")
    @NotBlank(message = "机构类型不能为空")
    private String agencyType;

    @Schema(description = "法人代表", example = "李四")
    private String legalRepresentative;

    @Schema(description = "统一社会信用代码", example = "91110000123456789X")
    private String unifiedSocialCreditCode;

    @Schema(description = "成立日期", example = "2020-01-01")
    private LocalDate establishmentDate;

    @Schema(description = "注册地址", example = "四川省成都市高新区")
    private String registeredAddress;

    @Schema(description = "经营地址", example = "四川省成都市高新区")
    private String operatingAddress;

    @Schema(description = "经营范围", example = "家政服务")
    private String businessScope;

    @Schema(description = "申请人姓名", example = "张三")
    private String applicantName;

    @Schema(description = "申请人电话", example = "***********")
    private String applicantPhone;

    @Schema(description = "申请时间", example = "2024-01-01 10:00:00")
    private LocalDateTime applicationTime;

    @Schema(description = "联系人", example = "张三")
    private String contactPerson;

    @Schema(description = "联系电话", example = "***********")
    private String contactPhone;

    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;

    @Schema(description = "机构地址", example = "四川省成都市高新区")
    private String agencyAddress;

    @Schema(description = "省份code", example = "510000")
    private String provinceCode;

    @Schema(description = "省份", example = "四川省")
    private String province;

    @Schema(description = "城市code", example = "510100")
    private String cityCode;

    @Schema(description = "城市", example = "成都市")
    private String city;

    @Schema(description = "区县code", example = "510107")
    private String districtCode;

    @Schema(description = "区县", example = "高新区")
    private String district;

    @Schema(description = "街道code", example = "510107001")
    private String streetCode;

    @Schema(description = "街道", example = "桂溪街道")
    private String street;

    @Schema(description = "详细地址", example = "天府大道中段1388号")
    private String detailAddress;

    @Schema(description = "经度", example = "104.065735")
    private BigDecimal longitude;

    @Schema(description = "纬度", example = "30.659462")
    private BigDecimal latitude;

    @Schema(description = "位置精度", example = "high")
    private String locationAccuracy;

    @Schema(description = "注册资本（万元）", example = "100.00")
    private BigDecimal registeredCapital;

    @Schema(description = "合同编号", example = "HT001")
    private String contractNo;

    @Schema(description = "合同开始日期", example = "2024-01-01")
    private LocalDate contractStartDate;

    @Schema(description = "合同结束日期", example = "2024-12-31")
    private LocalDate contractEndDate;

    @Schema(description = "开票名称", example = "测试机构开票名称")
    private String invoiceName;

    @Schema(description = "纳税人识别号", example = "91110000123456789X")
    private String taxpayerId;

    @Schema(description = "开户银行", example = "中国银行")
    private String bankName;

    @Schema(description = "银行账号", example = "****************")
    private String bankAccount;

    @Schema(description = "佣金比例", example = "5.00")
    private BigDecimal commissionRate;

    @Schema(description = "备注", example = "备注信息")
    private String remark;

    @Schema(description = "资质文件列表")
    private List<AgencyQualificationCreateVO> qualifications;
} 