package cn.bztmaster.cnt.module.publicbiz.service.order;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.*;

import java.util.Map;

/**
 * 家政服务订单 Service 接口
 *
 * <AUTHOR>
 */
public interface DomesticTaskOrderService {

    /**
     * 分页查询家政服务订单列表
     *
     * @param reqDTO 查询条件
     * @return 订单列表
     */
    PageResult<DomesticTaskOrderRespDTO> pageDomesticTaskOrder(DomesticTaskOrderPageReqDTO reqDTO);

    /**
     * 新增家政服务订单
     *
     * @param reqDTO 订单信息
     * @return 创建结果
     */
    DomesticTaskOrderSaveRespDTO createDomesticTaskOrder(DomesticTaskOrderSaveReqDTO reqDTO);

    /**
     * 更新家政服务订单
     *
     * @param reqDTO 订单信息
     * @return 更新结果
     */
    Boolean updateDomesticTaskOrder(DomesticTaskOrderUpdateReqDTO reqDTO);

    /**
     * 删除家政服务订单
     *
     * @param id 订单ID
     * @return 删除结果
     */
    Boolean deleteDomesticTaskOrder(Long id);

    /**
     * 获取家政服务订单详情
     *
     * @param id 订单ID
     * @return 订单详情
     */
    DomesticTaskOrderDetailRespDTO getDomesticTaskOrderDetail(Long id);

    /**
     * 导出家政服务订单列表
     *
     * @param reqDTO 导出条件
     * @return 导出结果
     */
    DomesticTaskOrderExportRespDTO exportDomesticTaskOrder(DomesticTaskOrderExportReqDTO reqDTO);

    /**
     * 根据订单ID查询家政服务订单详情
     *
     * @param orderId 订单ID
     * @return 家政服务订单详情
     */
    DomesticTaskOrderRespDTO getDomesticTaskOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询家政服务订单详情
     *
     * @param orderNo 订单号
     * @return 家政服务订单详情
     */
    DomesticTaskOrderRespDTO getDomesticTaskOrderByOrderNo(String orderNo);

    /**
     * 更新任务统计信息
     *
     * @param orderId 订单ID
     * @param completedTasks 已完成任务数
     * @param totalTasks 总任务数
     * @return 更新结果
     */
    Boolean updateTaskStatistics(Long orderId, Integer completedTasks, Integer totalTasks);

    /**
     * 获取订单统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getOrderStatistics();

    /**
     * 查询订单操作日志列表
     *
     * @param orderNo 订单号
     * @param page 页码
     * @param size 每页大小
     * @param logType 日志类型
     * @return 日志列表
     */
    PageResult<Map<String, Object>> getOrderLogList(String orderNo, Integer page, Integer size, String logType);

    // ========== 任务管理接口 ==========

    /**
     * 分页查询服务任务列表
     *
     * @param orderId     订单ID
     * @param page        页码
     * @param size        每页大小
     * @param taskStatus  任务状态
     * @param executor    执行人
     * @return 任务列表
     */
    PageResult<Map<String, Object>> pageTasks(Long orderId, Integer page, Integer size, String taskStatus, String executor);

    /**
     * 完成任务并上传完成凭证
     *
     * @param reqVO 请求参数
     * @return 完成结果
     */
    Boolean completeTask(Map<String, Object> reqVO);

    /**
     * 指派任务给服务人员
     *
     * @param reqVO 请求参数
     * @return 指派结果
     */
    Boolean assignTask(Map<String, Object> reqVO);

    /**
     * 取消任务
     *
     * @param reqVO 请求参数
     * @return 取消结果
     */
    Boolean cancelTask(Map<String, Object> reqVO);

    /**
     * 编辑任务信息
     *
     * @param reqVO 请求参数
     * @return 编辑结果
     */
    Boolean editTask(Map<String, Object> reqVO);

    /**
     * 查看任务完成凭证
     *
     * @param taskId 任务ID
     * @return 凭证信息
     */
    Map<String, Object> getTaskCertificate(String taskId);

    /**
     * 批量重指派任务
     *
     * @param reqVO 请求参数
     * @return 重指派结果
     */
    Map<String, Object> batchReassignTasks(Map<String, Object> reqVO);

    // ========== 收款信息管理接口 ==========

    /**
     * 查询订单收款信息
     *
     * @param orderId 订单ID
     * @return 收款信息
     */
    Map<String, Object> getPaymentInfo(String orderId);

    /**
     * 新增收款信息
     *
     * @param reqVO 请求参数
     * @return 新增结果
     */
    Map<String, Object> addPayment(Map<String, Object> reqVO);

    /**
     * 修改收款信息
     *
     * @param reqVO 请求参数
     * @return 修改结果
     */
    Boolean updatePayment(Map<String, Object> reqVO);

    // ========== 收支记录管理接口 ==========

    /**
     * 查询订单收支记录列表
     *
     * @param orderId 订单ID
     * @return 收支记录列表
     */
    Map<String, Object> getIncomeExpenseList(String orderId);

    /**
     * 新增收支记录
     *
     * @param reqVO 请求参数
     * @return 新增结果
     */
    Map<String, Object> addIncomeExpense(Map<String, Object> reqVO);

    /**
     * 修改收支记录
     *
     * @param reqVO 请求参数
     * @return 修改结果
     */
    Boolean updateIncomeExpense(Map<String, Object> reqVO);

    /**
     * 删除收支记录
     *
     * @param reqVO 请求参数
     * @return 删除结果
     */
    Boolean deleteIncomeExpense(Map<String, Object> reqVO);

    // ========== 服务评价管理接口 ==========

    /**
     * 查询订单服务评价信息
     *
     * @param orderId 订单ID
     * @return 评价信息
     */
    Map<String, Object> getEvaluationInfo(String orderId);

    /**
     * 新增服务评价
     *
     * @param reqVO 请求参数
     * @return 新增结果
     */
    Boolean addEvaluation(Map<String, Object> reqVO);

    /**
     * 更新服务评价
     *
     * @param reqVO 请求参数
     * @return 更新结果
     */
    Boolean updateEvaluation(Map<String, Object> reqVO);

    // ========== 新增接口 ==========

    /**
     * 获取服务套餐列表
     *
     * @return 套餐列表
     */
    Map<String, Object> getPackageList();

    /**
     * 获取服务机构列表
     *
     * @return 机构列表
     */
    Map<String, Object> getAgencyList();

    /**
     * 获取服务人员列表
     *
     * @return 人员列表
     */
    Map<String, Object> getPractitionerList(Long agencyId);

    // ========== 售后记录管理接口 ==========

    /**
     * 查询售后记录列表
     *
     * @param orderId 订单ID
     * @return 售后记录列表
     */
    Map<String, Object> getAfterSalesList(String orderId);

    /**
     * 新增售后记录
     *
     * @param reqVO 售后记录信息
     * @return 新增结果
     */
    Map<String, Object> addAfterSalesRecord(Map<String, Object> reqVO);

    /**
     * 修改售后记录
     *
     * @param reqVO 售后记录信息
     * @return 修改结果
     */
    Boolean updateAfterSalesRecord(Map<String, Object> reqVO);

    /**
     * 删除售后记录
     *
     * @param reqVO 删除请求
     * @return 删除结果
     */
    Boolean deleteAfterSalesRecord(Map<String, Object> reqVO);
}
