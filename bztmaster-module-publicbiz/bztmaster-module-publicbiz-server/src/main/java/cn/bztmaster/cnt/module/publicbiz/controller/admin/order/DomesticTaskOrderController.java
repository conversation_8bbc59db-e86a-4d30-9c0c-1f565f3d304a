package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.order.DomesticTaskOrderConvert;
import cn.bztmaster.cnt.module.publicbiz.service.order.DomesticTaskOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 家政服务订单管理 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 家政服务订单")
@RestController
@RequestMapping("/publicbiz/domestic-task-order")
@Validated
@Slf4j
public class DomesticTaskOrderController {

    @Resource
    private DomesticTaskOrderService domesticTaskOrderService;

    // ========== 订单管理接口 ==========

    @PostMapping("/page")
    @Operation(summary = "分页查询家政服务订单列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<PageResult<DomesticTaskOrderRespVO>> pageDomesticTaskOrder(@Valid @RequestBody DomesticTaskOrderPageReqVO reqVO) {
        DomesticTaskOrderPageReqDTO reqDTO = DomesticTaskOrderConvert.INSTANCE.convert(reqVO);
        PageResult<DomesticTaskOrderRespDTO> pageResult = domesticTaskOrderService.pageDomesticTaskOrder(reqDTO);
        return success(DomesticTaskOrderConvert.INSTANCE.convertPageFromDTO(pageResult));
    }

    @PostMapping("/add")
    @Operation(summary = "新增家政服务订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:create')")
    public CommonResult<DomesticTaskOrderSaveRespVO> createDomesticTaskOrder(@Valid @RequestBody DomesticTaskOrderSaveReqVO reqVO) {
        DomesticTaskOrderSaveReqDTO reqDTO = DomesticTaskOrderConvert.INSTANCE.convert(reqVO);
        DomesticTaskOrderSaveRespDTO respDTO = domesticTaskOrderService.createDomesticTaskOrder(reqDTO);
        return success(DomesticTaskOrderConvert.INSTANCE.convert(respDTO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新家政服务订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> updateDomesticTaskOrder(@Valid @RequestBody DomesticTaskOrderUpdateReqVO reqVO) {
        DomesticTaskOrderUpdateReqDTO reqDTO = DomesticTaskOrderConvert.INSTANCE.convert(reqVO);
        domesticTaskOrderService.updateDomesticTaskOrder(reqDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除家政服务订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:delete')")
    public CommonResult<Boolean> deleteDomesticTaskOrder(@PathVariable("id") Long id) {
        domesticTaskOrderService.deleteDomesticTaskOrder(id);
        return success(true);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取家政服务订单详情")
    @Parameter(name = "id", description = "订单ID", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<DomesticTaskOrderDetailRespVO> getDomesticTaskOrderDetail(@PathVariable("id") Long id) {
        DomesticTaskOrderDetailRespDTO respDTO = domesticTaskOrderService.getDomesticTaskOrderDetail(id);
        return success(DomesticTaskOrderConvert.INSTANCE.convert(respDTO));
    }

    @GetMapping("/get")
    @Operation(summary = "获取家政服务订单详情（GET方法）")
    @Parameter(name = "id", description = "订单ID", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<DomesticTaskOrderDetailRespVO> getDomesticTaskOrderDetailGet(@RequestParam("id") Long id) {
        DomesticTaskOrderDetailRespDTO respDTO = domesticTaskOrderService.getDomesticTaskOrderDetail(id);
        return success(DomesticTaskOrderConvert.INSTANCE.convert(respDTO));
    }

    @PostMapping("/export")
    @Operation(summary = "导出家政服务订单列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:export')")
    public CommonResult<DomesticTaskOrderExportRespVO> exportDomesticTaskOrder(@Valid @RequestBody DomesticTaskOrderExportReqVO reqVO) {
        DomesticTaskOrderExportReqDTO reqDTO = DomesticTaskOrderConvert.INSTANCE.convert(reqVO);
        DomesticTaskOrderExportRespDTO respDTO = domesticTaskOrderService.exportDomesticTaskOrder(reqDTO);
        return success(DomesticTaskOrderConvert.INSTANCE.convert(respDTO));
    }

    // ========== 任务管理接口 ==========

    @GetMapping("/task/page")
    @Operation(summary = "分页查询服务任务列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<PageResult<Map<String, Object>>> pageTasks(
            @RequestParam Long orderId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String taskStatus,
            @RequestParam(required = false) String executor) {
        
        PageResult<Map<String, Object>> result = domesticTaskOrderService.pageTasks(orderId, page, size, taskStatus, executor);
        return success(result);
    }

    @PostMapping("/task/complete")
    @Operation(summary = "完成任务并上传完成凭证")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> completeTask(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.completeTask(reqVO);
        return success(result);
    }

    @PostMapping("/task/assign")
    @Operation(summary = "指派任务给服务人员")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> assignTask(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.assignTask(reqVO);
        return success(result);
    }

    @PostMapping("/task/cancel")
    @Operation(summary = "取消任务")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> cancelTask(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.cancelTask(reqVO);
        return success(result);
    }

    @PostMapping("/task/edit")
    @Operation(summary = "编辑任务信息")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> editTask(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.editTask(reqVO);
        return success(result);
    }

    @GetMapping("/task/certificate")
    @Operation(summary = "查看任务完成凭证")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Map<String, Object>> getTaskCertificate(@RequestParam String taskId) {
        Map<String, Object> result = domesticTaskOrderService.getTaskCertificate(taskId);
        return success(result);
    }

    @PostMapping("/task/batch-reassign")
    @Operation(summary = "批量重指派任务")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Map<String, Object>> batchReassignTasks(@Valid @RequestBody Map<String, Object> reqVO) {
        Map<String, Object> result = domesticTaskOrderService.batchReassignTasks(reqVO);
        return success(result);
    }

    // ========== 收款信息管理接口 ==========

    @GetMapping("/payment/info")
    @Operation(summary = "查询订单收款信息")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Map<String, Object>> getPaymentInfo(@RequestParam String orderId) {
        Map<String, Object> result = domesticTaskOrderService.getPaymentInfo(orderId);
        return success(result);
    }

    @PostMapping("/payment/add")
    @Operation(summary = "新增收款信息")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Map<String, Object>> addPayment(@Valid @RequestBody Map<String, Object> reqVO) {
        Map<String, Object> result = domesticTaskOrderService.addPayment(reqVO);
        return success(result);
    }

    @PostMapping("/payment/update")
    @Operation(summary = "修改收款信息")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> updatePayment(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.updatePayment(reqVO);
        return success(result);
    }

    // ========== 收支记录管理接口 ==========

    @GetMapping("/income-expense/list")
    @Operation(summary = "查询订单收支记录列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Map<String, Object>> getIncomeExpenseList(@RequestParam String orderId) {
        Map<String, Object> result = domesticTaskOrderService.getIncomeExpenseList(orderId);
        return success(result);
    }

    @PostMapping("/income-expense/add")
    @Operation(summary = "新增收支记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Map<String, Object>> addIncomeExpense(@Valid @RequestBody Map<String, Object> reqVO) {
        Map<String, Object> result = domesticTaskOrderService.addIncomeExpense(reqVO);
        return success(result);
    }

    @PostMapping("/income-expense/update")
    @Operation(summary = "修改收支记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> updateIncomeExpense(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.updateIncomeExpense(reqVO);
        return success(result);
    }

    @PostMapping("/income-expense/delete")
    @Operation(summary = "删除收支记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:delete')")
    public CommonResult<Boolean> deleteIncomeExpense(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.deleteIncomeExpense(reqVO);
        return success(result);
    }

    // ========== 服务评价管理接口 ==========

    @GetMapping("/evaluation/info")
    @Operation(summary = "查询订单服务评价信息")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Map<String, Object>> getEvaluationInfo(@RequestParam String orderId) {
        Map<String, Object> result = domesticTaskOrderService.getEvaluationInfo(orderId);
        return success(result);
    }

    @PostMapping("/evaluation/add")
    @Operation(summary = "新增服务评价")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> addEvaluation(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.addEvaluation(reqVO);
        return success(result);
    }

    // ========== 新增接口 ==========

    @GetMapping("/package/list")
    @Operation(summary = "获取服务套餐列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Map<String, Object>> getPackageList() {
        Map<String, Object> result = domesticTaskOrderService.getPackageList();
        return success(result);
    }

    @GetMapping("/agency/list")
    @Operation(summary = "获取服务机构列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Map<String, Object>> getAgencyList() {
        Map<String, Object> result = domesticTaskOrderService.getAgencyList();
        return success(result);
    }

    @GetMapping("/practitioner/list")
    @Operation(summary = "获取服务人员列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Map<String, Object>> getPractitionerList(@RequestParam(required = false) Long agencyId) {
        Map<String, Object> result = domesticTaskOrderService.getPractitionerList(agencyId);
        return success(result);
    }

    @PostMapping("/evaluation/update")
    @Operation(summary = "更新服务评价")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> updateEvaluation(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.updateEvaluation(reqVO);
        return success(result);
    }

    // ========== 售后记录管理接口 ==========

    @GetMapping("/after-sales/list")
    @Operation(summary = "查询售后记录列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Map<String, Object>> getAfterSalesList(@RequestParam String orderId) {
        Map<String, Object> result = domesticTaskOrderService.getAfterSalesList(orderId);
        return success(result);
    }

    @PostMapping("/after-sales/add")
    @Operation(summary = "新增售后记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:create')")
    public CommonResult<Map<String, Object>> addAfterSalesRecord(@Valid @RequestBody Map<String, Object> reqVO) {
        Map<String, Object> result = domesticTaskOrderService.addAfterSalesRecord(reqVO);
        return success(result);
    }

    @PostMapping("/after-sales/update")
    @Operation(summary = "修改售后记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:update')")
    public CommonResult<Boolean> updateAfterSalesRecord(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.updateAfterSalesRecord(reqVO);
        return success(result);
    }

    @PostMapping("/after-sales/delete")
    @Operation(summary = "删除售后记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:delete')")
    public CommonResult<Boolean> deleteAfterSalesRecord(@Valid @RequestBody Map<String, Object> reqVO) {
        Boolean result = domesticTaskOrderService.deleteAfterSalesRecord(reqVO);
        return success(result);
    }

    // ========== 测试接口（无权限验证） ==========

    @GetMapping("/page-test")
    @Operation(summary = "分页查询家政服务订单列表（测试接口，无权限验证）")
    public CommonResult<PageResult<DomesticTaskOrderRespVO>> pageDomesticTaskOrderTest(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword) {
        
        DomesticTaskOrderPageReqDTO reqDTO = new DomesticTaskOrderPageReqDTO();
        reqDTO.setPageNo(page);
        reqDTO.setPageSize(size);
        reqDTO.setKeyword(keyword);
        
        PageResult<DomesticTaskOrderRespDTO> pageResult = domesticTaskOrderService.pageDomesticTaskOrder(reqDTO);
        return success(DomesticTaskOrderConvert.INSTANCE.convertPageFromDTO(pageResult));
    }

    @GetMapping("/detail-test/{id}")
    @Operation(summary = "获取家政服务订单详情（测试接口，无权限验证）")
    @Parameter(name = "id", description = "订单ID", required = true)
    public CommonResult<DomesticTaskOrderDetailRespVO> getDomesticTaskOrderDetailTest(@PathVariable("id") Long id) {
        DomesticTaskOrderDetailRespDTO respDTO = domesticTaskOrderService.getDomesticTaskOrderDetail(id);
        return success(DomesticTaskOrderConvert.INSTANCE.convert(respDTO));
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取订单统计信息")
    //@PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<Map<String, Object>> getOrderStatistics() {
        Map<String, Object> statistics = domesticTaskOrderService.getOrderStatistics();
        return success(statistics);
    }

    @PostMapping("/add-test")
    @Operation(summary = "新增家政服务订单（测试接口，无权限验证）")
    public CommonResult<DomesticTaskOrderSaveRespVO> createDomesticTaskOrderTest(@Valid @RequestBody DomesticTaskOrderSaveReqVO reqVO) {
        DomesticTaskOrderSaveReqDTO reqDTO = DomesticTaskOrderConvert.INSTANCE.convert(reqVO);
        DomesticTaskOrderSaveRespDTO respDTO = domesticTaskOrderService.createDomesticTaskOrder(reqDTO);
        return success(DomesticTaskOrderConvert.INSTANCE.convert(respDTO));
    }

    // ========== 日志管理接口 ==========

    @GetMapping("/log/list")
    @Operation(summary = "查询订单操作日志列表")
    @PreAuthorize("@ss.hasPermission('publicbiz:domestic-task-order:query')")
    public CommonResult<PageResult<Map<String, Object>>> getOrderLogList(
            @RequestParam String orderNo,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String logType) {
        
        PageResult<Map<String, Object>> result = domesticTaskOrderService.getOrderLogList(orderNo, page, size, logType);
        return success(result);
    }
}
