package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "请假申请详情响应VO")
@Data
public class LeaveApprovalDetailRespVO {

    @Schema(description = "申请信息")
    private RequestInfo requestInfo;

    @Schema(description = "受影响订单列表")
    private List<AffectedOrderVO> affectedOrders;

    @Schema(description = "申请信息")
    @Data
    public static class RequestInfo {
        @Schema(description = "申请ID")
        private Long id;

        @Schema(description = "工单编号")
        private String workOrderNo;

        @Schema(description = "阿姨OneID")
        private String auntOneId;

        @Schema(description = "阿姨姓名")
        private String auntName;

        @Schema(description = "请假类型(1-请假,2-调休)")
        private Integer leaveType;

        @Schema(description = "请假类型文本")
        private String leaveTypeText;

        @Schema(description = "开始时间")
        private LocalDateTime startTime;

        @Schema(description = "结束时间")
        private LocalDateTime endTime;

        @Schema(description = "请假天数")
        private Double durationDays;

        @Schema(description = "请假时长(小时)")
        private Double durationHours;

        @Schema(description = "请假理由")
        private String reason;

        @Schema(description = "工单状态")
        private String workOrderStatus;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;
    }

    @Schema(description = "受影响订单信息")
    @Data
    public static class AffectedOrderVO {
        @Schema(description = "订单ID")
        private Long orderId;

        @Schema(description = "订单编号")
        private String orderNo;

        @Schema(description = "订单类型")
        private String orderType;

        @Schema(description = "订单类型文本")
        private String orderTypeText;

        @Schema(description = "服务名称")
        private String serviceName;

        @Schema(description = "服务进度")
        private String progress;

        @Schema(description = "客户姓名")
        private String customerName;

        @Schema(description = "客户电话(脱敏)")
        private String customerPhone;

        @Schema(description = "服务地址")
        private String address;

        @Schema(description = "服务开始时间")
        private LocalDateTime serviceTime;

        @Schema(description = "服务结束时间")
        private LocalDateTime serviceEndTime;
    }
}
