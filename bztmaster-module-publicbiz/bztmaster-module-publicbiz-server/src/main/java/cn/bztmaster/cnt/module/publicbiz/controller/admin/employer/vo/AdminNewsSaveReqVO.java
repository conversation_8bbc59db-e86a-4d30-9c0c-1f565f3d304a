package cn.bztmaster.cnt.module.publicbiz.controller.admin.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 管理后台 - 资讯保存请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 资讯保存请求 VO")
@Data
public class AdminNewsSaveReqVO {

    @Schema(description = "资讯ID（更新时必填）", example = "1024")
    private Long id;

    @Schema(description = "资讯标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "家政服务标准化建设指南")
    @NotBlank(message = "资讯标题不能为空")
    @Size(max = 200, message = "资讯标题长度不能超过200字符")
    private String newsTitle;

    @Schema(description = "资讯摘要", requiredMode = Schema.RequiredMode.REQUIRED, example = "本文详细介绍了家政服务标准化的具体实施方法和注意事项...")
    @NotBlank(message = "资讯摘要不能为空")
    private String newsSummary;

    @Schema(description = "资讯内容", example = "<p>家政服务标准化是提升服务质量的重要保障...</p>")
    private String newsContent;

    @Schema(description = "分类ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "就业服务")
    @NotBlank(message = "分类名称不能为空")
    private String categoryName;

    @Schema(description = "封面图片URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/images/guide.jpg")
    @NotBlank(message = "封面图片URL不能为空")
    private String coverImageUrl;

    @Schema(description = "关联素材文章ID", example = "1024")
    private Long materialId;

    @Schema(description = "作者", requiredMode = Schema.RequiredMode.REQUIRED, example = "李专家")
    @NotBlank(message = "作者不能为空")
    @Size(max = 50, message = "作者长度不能超过50字符")
    private String author;

    @Schema(description = "发布时间", example = "2024-01-20 16:00:00")
    private LocalDateTime publishTime;

    @Schema(description = "状态：draft-草稿/published-已发布", requiredMode = Schema.RequiredMode.REQUIRED, example = "draft")
    @NotBlank(message = "状态不能为空")
    private String status;

    @Schema(description = "排序值，数字越小越靠前", example = "5")
    private Integer sort;
} 