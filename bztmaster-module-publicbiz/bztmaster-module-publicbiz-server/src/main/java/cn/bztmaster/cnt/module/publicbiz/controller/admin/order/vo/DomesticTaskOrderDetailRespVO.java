package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 家政服务订单详情 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务订单详情 Response VO")
@Data
public class DomesticTaskOrderDetailRespVO {

    @Schema(description = "订单基本信息")
    private OrderInfo orderInfo;

    @Schema(description = "客户信息")
    private CustomerInfo customerInfo;

    @Schema(description = "服务信息")
    private ServiceInfo serviceInfo;

    @Schema(description = "支付信息")
    private PaymentInfo paymentInfo;

    @Schema(description = "任务信息")
    private TaskInfo taskInfo;

    @Schema(description = "服务人员信息")
    private PractitionerInfo practitionerInfo;

    @Schema(description = "服务机构信息")
    private AgencyInfo agencyInfo;

    @Data
    public static class OrderInfo {
        @Schema(description = "订单ID", example = "1")
        private Long id;

        @Schema(description = "订单编号", example = "DS202406001")
        private String orderNumber;

        @Schema(description = "订单状态", example = "in_service")
        private String orderStatus;

        @Schema(description = "支付状态", example = "paid")
        private String paymentStatus;

        @Schema(description = "商机ID", example = "OPP001")
        private String opportunityId;

        @Schema(description = "线索ID", example = "LEAD001")
        private String leadId;

        @Schema(description = "订单金额", example = "12800.00")
        private BigDecimal totalAmount;

        @Schema(description = "服务套餐", example = "SP001 - 月嫂服务套餐")
        private String servicePackage;

        @Schema(description = "服务地址", example = "北京市朝阳区建国路88号")
        private String serviceAddress;

        @Schema(description = "阿姨oneid", example = "15e95a55-7b44-11f0-ae0c-00163e1f6ba5")
        private String practitionerOneid;

        @Schema(description = "预约时间", example = "2024-06-01 09:00:00")
        private LocalDateTime appointmentTime;

        @Schema(description = "服务开始日期", example = "2024-06-01")
        private String serviceStartDate;

        @Schema(description = "服务结束日期", example = "2024-06-30")
        private String serviceEndDate;

        @Schema(description = "服务时长", example = "30天")
        private String serviceDuration;

        @Schema(description = "服务频次", example = "每日")
        private String serviceFrequency;

        @Schema(description = "服务描述", example = "专业月嫂服务，包含新生儿护理、产妇护理等")
        private String serviceDescription;

        @Schema(description = "创建时间", example = "2024-06-01 09:00:00")
        private LocalDateTime createTime;

        @Schema(description = "更新时间", example = "2024-06-15 10:30:00")
        private LocalDateTime updateTime;
    }

    @Data
    public static class CustomerInfo {
        @Schema(description = "客户姓名", example = "李女士")
        private String customerName;

        @Schema(description = "客户电话", example = "13812345678")
        private String customerPhone;

        @Schema(description = "服务地址", example = "北京市朝阳区建国路88号")
        private String serviceAddress;
    }

    @Data
    public static class ServiceInfo {
        @Schema(description = "服务类型", example = "月嫂服务")
        private String serviceType;

        @Schema(description = "服务套餐", example = "SP001 - 月嫂服务套餐")
        private String servicePackage;

        @Schema(description = "服务开始日期", example = "2024-06-01")
        private String serviceStartDate;

        @Schema(description = "服务结束日期", example = "2024-06-30")
        private String serviceEndDate;

        @Schema(description = "服务时长", example = "30天")
        private String serviceDuration;

        @Schema(description = "服务金额", example = "12800.00")
        private BigDecimal serviceAmount;
    }

    @Data
    public static class PaymentInfo {
        @Schema(description = "订单总金额", example = "12800.00")
        private BigDecimal totalAmount;

        @Schema(description = "已支付金额", example = "12800.00")
        private BigDecimal paidAmount;

        @Schema(description = "支付方式", example = "银行转账")
        private String paymentMethod;

        @Schema(description = "支付时间", example = "2024-06-01 10:00:00")
        private LocalDateTime paymentTime;
    }

    @Data
    public static class TaskInfo {
        @Schema(description = "总任务数", example = "30")
        private Integer totalTasks;

        @Schema(description = "已完成任务数", example = "15")
        private Integer completedTasks;

        @Schema(description = "任务进度", example = "50.0")
        private BigDecimal taskProgress;
    }

    @Data
    public static class PractitionerInfo {
        @Schema(description = "服务人员OneID", example = "15e95a55-7b44-11f0-ae0c-00163e1f6ba5")
        private String practitionerOneid;

        @Schema(description = "服务人员姓名", example = "李阿姨")
        private String practitionerName;

        @Schema(description = "服务人员电话", example = "19808009090")
        private String practitionerPhone;

        @Schema(description = "服务人员评级", example = "4.8")
        private BigDecimal rating;

        @Schema(description = "从业年限", example = "5")
        private Integer experienceYears;
    }

    @Data
    public static class AgencyInfo {
        @Schema(description = "服务机构ID", example = "1001")
        private Long agencyId;

        @Schema(description = "服务机构编码", example = "1001")
        private String agencyCode;

        @Schema(description = "服务机构名称", example = "测试机构A")
        private String agencyName;

        @Schema(description = "机构类型", example = "cooperation")
        private String agencyType;

        @Schema(description = "合作状态", example = "cooperating")
        private String cooperationStatus;

        @Schema(description = "联系人", example = "李经理")
        private String contactPerson;

        @Schema(description = "联系电话", example = "13812345678")
        private String contactPhone;
    }
}
