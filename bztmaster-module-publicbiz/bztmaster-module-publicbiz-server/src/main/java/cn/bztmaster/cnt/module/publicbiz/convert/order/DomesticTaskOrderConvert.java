package cn.bztmaster.cnt.module.publicbiz.convert.order;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.DomesticTaskOrderDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 家政服务订单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DomesticTaskOrderConvert {

    DomesticTaskOrderConvert INSTANCE = Mappers.getMapper(DomesticTaskOrderConvert.class);

    // ========== DO 转 DTO ==========
    @Mapping(target = "orderNumber", source = "orderNo")
    @Mapping(target = "serviceType", source = "serviceCategoryName")
    @Mapping(target = "servicePersonnel", source = "practitionerName")
    @Mapping(target = "serviceAgency", source = "agencyName")
    @Mapping(target = "serviceAmount", source = "actualAmount")
    @Mapping(target = "completedTasks", source = "completedTaskCount")
    @Mapping(target = "totalTasks", source = "taskCount")
    @Mapping(target = "orderStatus", ignore = true)
    @Mapping(target = "paymentStatus", ignore = true)
    @Mapping(target = "appointmentTime", ignore = true)
    DomesticTaskOrderRespDTO convert(DomesticTaskOrderDO bean);

    List<DomesticTaskOrderRespDTO> convertList(List<DomesticTaskOrderDO> list);

    PageResult<DomesticTaskOrderRespDTO> convertPage(PageResult<DomesticTaskOrderDO> page);

    // ========== DTO 转 DO ==========
    DomesticTaskOrderDO convert(DomesticTaskOrderSaveReqDTO bean);

    DomesticTaskOrderDO convert(DomesticTaskOrderUpdateReqDTO bean);

    // ========== VO 转 DTO ==========
    DomesticTaskOrderPageReqDTO convert(DomesticTaskOrderPageReqVO bean);

    DomesticTaskOrderSaveReqDTO convert(DomesticTaskOrderSaveReqVO bean);

    @Mapping(target = "orderNo", ignore = true)
    @Mapping(target = "unitPrice", ignore = true)
    @Mapping(target = "totalAmount", ignore = true)
    @Mapping(target = "actualAmount", ignore = true)
    DomesticTaskOrderUpdateReqDTO convert(DomesticTaskOrderUpdateReqVO bean);

    DomesticTaskOrderExportReqDTO convert(DomesticTaskOrderExportReqVO bean);

    // ========== DTO 转 VO ==========
    DomesticTaskOrderRespVO convert(DomesticTaskOrderRespDTO bean);

    List<DomesticTaskOrderRespVO> convertListFromDTO(List<DomesticTaskOrderRespDTO> list);

    PageResult<DomesticTaskOrderRespVO> convertPageFromDTO(PageResult<DomesticTaskOrderRespDTO> page);

    DomesticTaskOrderSaveRespVO convert(DomesticTaskOrderSaveRespDTO bean);

    DomesticTaskOrderDetailRespVO convert(DomesticTaskOrderDetailRespDTO bean);

    DomesticTaskOrderExportRespVO convert(DomesticTaskOrderExportRespDTO bean);
}
