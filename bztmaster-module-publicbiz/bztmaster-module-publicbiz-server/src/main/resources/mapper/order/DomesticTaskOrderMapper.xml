<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.DomesticTaskOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.DomesticTaskOrderDO">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_no" property="orderNo"/>
        <result column="customer_oneid" property="customerOneid"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
        <result column="customer_address" property="customerAddress"/>
        <result column="customer_remark" property="customerRemark"/>
        <result column="service_category_id" property="serviceCategoryId"/>
        <result column="service_category_name" property="serviceCategoryName"/>
        <result column="service_package_id" property="servicePackageId"/>
        <result column="service_package_name" property="servicePackageName"/>
        <result column="service_start_date" property="serviceStartDate"/>
        <result column="service_end_date" property="serviceEndDate"/>
        <result column="service_duration" property="serviceDuration"/>
        <result column="service_frequency" property="serviceFrequency"/>
        <result column="service_package_thumbnail" property="servicePackageThumbnail"/>
        <result column="service_package_price" property="servicePackagePrice"/>
        <result column="service_package_original_price" property="servicePackageOriginalPrice"/>
        <result column="service_package_unit" property="servicePackageUnit"/>
        <result column="service_package_duration" property="servicePackageDuration"/>
        <result column="service_package_type" property="servicePackageType"/>
        <result column="service_description" property="serviceDescription"/>
        <result column="service_details" property="serviceDetails"/>
        <result column="service_process" property="serviceProcess"/>
        <result column="purchase_notice" property="purchaseNotice"/>
        <result column="service_times" property="serviceTimes"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="total_amount" property="totalAmount"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="actual_amount" property="actualAmount"/>
        <result column="service_address" property="serviceAddress"/>
        <result column="service_address_detail" property="serviceAddressDetail"/>
        <result column="service_latitude" property="serviceLatitude"/>
        <result column="service_longitude" property="serviceLongitude"/>
        <result column="service_schedule" property="serviceSchedule"/>
        <result column="practitioner_oneid" property="practitionerOneid"/>
        <result column="practitioner_name" property="practitionerName"/>
        <result column="practitioner_phone" property="practitionerPhone"/>
        <result column="agency_id" property="agencyId"/>
        <result column="agency_name" property="agencyName"/>
        <result column="task_count" property="taskCount"/>
        <result column="completed_task_count" property="completedTaskCount"/>
        <result column="task_progress" property="taskProgress"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="agency_fee" property="agencyFee"/>
        <result column="platform_fee" property="platformFee"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, order_no, customer_oneid, customer_name, customer_phone, customer_address, customer_remark,
        service_category_id, service_category_name, service_package_id, service_package_name, service_start_date,
        service_end_date, service_duration, service_frequency, service_package_thumbnail, service_package_price,
        service_package_original_price, service_package_unit, service_package_duration, service_package_type,
        service_description, service_details, service_process, purchase_notice, service_times, unit_price,
        total_amount, discount_amount, actual_amount, service_address, service_address_detail, service_latitude,
        service_longitude, service_schedule, practitioner_oneid, practitioner_name, practitioner_phone,
        agency_id, agency_name, task_count, completed_task_count, task_progress, service_fee, agency_fee,
        platform_fee, create_time, update_time, creator, updater, deleted, tenant_id
    </sql>

    <!-- 根据订单ID查询家政服务订单详情 -->
    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_domestic_order
        WHERE order_id = #{orderId}
        AND deleted = 0
    </select>

    <!-- 根据订单号查询家政服务订单详情 -->
    <select id="selectByOrderNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_domestic_order
        WHERE order_no = #{orderNo}
        AND deleted = 0
    </select>

    <!-- 根据客户电话查询订单列表 -->
    <select id="selectByCustomerPhone" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_domestic_order
        WHERE customer_phone = #{customerPhone}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据服务人员OneID查询订单列表 -->
    <select id="selectByPractitionerOneid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_domestic_order
        WHERE practitioner_oneid = #{practitionerOneid}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据服务机构ID查询订单列表 -->
    <select id="selectByAgencyId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_domestic_order
        WHERE agency_id = #{agencyId}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据日期范围查询订单列表 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM publicbiz_domestic_order
        WHERE deleted = 0
        <if test="startDate != null">
            AND service_start_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND service_end_date &lt;= #{endDate}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 更新任务统计信息 -->
    <update id="updateTaskStatistics">
        UPDATE publicbiz_domestic_order
        SET completed_task_count = #{completedTasks},
            task_count = #{totalTasks},
            task_progress = #{taskProgress},
            update_time = NOW()
        WHERE id = #{orderId}
        AND deleted = 0
    </update>

</mapper>
