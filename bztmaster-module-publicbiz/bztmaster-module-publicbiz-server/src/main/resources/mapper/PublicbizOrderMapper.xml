<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="order_no" property="orderNo" />
        <result column="order_type" property="orderType" />
        <result column="business_line" property="businessLine" />
        <result column="opportunity_id" property="opportunityId" />
        <result column="lead_id" property="leadId" />
        <result column="project_name" property="projectName" />
        <result column="project_description" property="projectDescription" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="total_amount" property="totalAmount" />
        <result column="paid_amount" property="paidAmount" />
        <result column="refund_amount" property="refundAmount" />
        <result column="payment_status" property="paymentStatus" />
        <result column="order_status" property="orderStatus" />
        <result column="manager_id" property="managerId" />
        <result column="manager_name" property="managerName" />
        <result column="manager_phone" property="managerPhone" />
        <result column="contract_type" property="contractType" />
        <result column="contract_file_url" property="contractFileUrl" />
        <result column="contract_status" property="contractStatus" />
        <result column="remark" property="remark" />
        <result column="settlement_status" property="settlementStatus" />
        <result column="settlement_time" property="settlementTime" />
        <result column="settlement_method" property="settlementMethod" />
        <result column="is_selected_for_reconciliation" property="isSelectedForReconciliation" />
        <result column="selection_time" property="selectionTime" />
        <result column="selector_id" property="selectorId" />
        <result column="selector_name" property="selectorName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, creator, create_time, updater, update_time, deleted,
        order_no, order_type, business_line, opportunity_id, lead_id, project_name, project_description,
        start_date, end_date, total_amount, paid_amount, refund_amount, payment_status, order_status,
        manager_id, manager_name, manager_phone, contract_type, contract_file_url, contract_status,
        remark, settlement_status, settlement_time, settlement_method, is_selected_for_reconciliation,
        selection_time, selector_id, selector_name
    </sql>

    <!-- 分页查询高校实践订单（支持关键词搜索） -->
    <select id="selectPracticeOrderPageWithKeyword" resultMap="BaseResultMap">
        SELECT DISTINCT o.* 
        FROM publicbiz_order o
        <if test="reqVO.keyword != null and reqVO.keyword != ''">
            LEFT JOIN publicbiz_practice_order ppo ON o.id = ppo.order_id
        </if>
        WHERE o.order_type = 'practice' 
        AND o.business_line = '高校实践'
        AND o.deleted = 0
        <if test="reqVO.orderStatus != null and reqVO.orderStatus != ''">
            AND o.order_status = #{reqVO.orderStatus}
        </if>
        <if test="reqVO.paymentStatus != null and reqVO.paymentStatus != ''">
            AND o.payment_status = #{reqVO.paymentStatus}
        </if>
        <if test="reqVO.managerId != null">
            AND o.manager_id = #{reqVO.managerId}
        </if>
        <if test="reqVO.opportunityId != null and reqVO.opportunityId != ''">
            AND o.opportunity_id = #{reqVO.opportunityId}
        </if>
        <if test="reqVO.createTime != null and reqVO.createTime.length == 2">
            AND o.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
        </if>
        <if test="reqVO.keyword != null and reqVO.keyword != ''">
            AND (
                o.project_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
                OR o.manager_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
                OR ppo.university_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
                OR ppo.enterprise_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            )
        </if>
        ORDER BY o.id DESC
    </select>

</mapper>





