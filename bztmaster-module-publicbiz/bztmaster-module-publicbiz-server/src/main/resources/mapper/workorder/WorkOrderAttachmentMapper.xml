<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.workorder.WorkOrderAttachmentMapper">

    <!-- 根据工单ID查询附件列表 -->
    <select id="selectByWorkOrderId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderAttachmentDO">
        SELECT *
        FROM publicbiz_work_order_attachment
        WHERE work_order_no = (
            SELECT work_order_no 
            FROM publicbiz_work_order 
            WHERE id = #{workOrderId} 
              AND deleted = 0
        )
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据工单编号查询附件列表 -->
    <select id="selectByWorkOrderNo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.workorder.WorkOrderAttachmentDO">
        SELECT *
        FROM publicbiz_work_order_attachment
        WHERE work_order_no = #{workOrderNo}
          AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据工单ID删除附件 -->
    <delete id="deleteByWorkOrderId">
        DELETE FROM publicbiz_work_order_attachment
        WHERE work_order_no = (
            SELECT work_order_no 
            FROM publicbiz_work_order 
            WHERE id = #{workOrderId} 
              AND deleted = 0
        )
    </delete>

</mapper>
