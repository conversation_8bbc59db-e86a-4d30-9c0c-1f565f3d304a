<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.evaluation.OrderEvaluationMapper">

    <sql id="selectFields">
        id, tenant_id, creator, create_time, updater, update_time, deleted,
        order_id, aunt_id, reviewer_id, reviewer_name, reviewer_avatar, rating,
        attitude_rating, professional_rating, responsibility_rating,
        review_tags, review_content, review_images, review_type, is_anonymous,
        is_recommend, like_count, reply_content, reply_time, status, agency_id, service_package_id
    </sql>

    <!-- 根据订单ID查询评价信息 -->
    <select id="selectByOrderId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.evaluation.OrderEvaluationDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_aunt_review
        WHERE deleted = 0
        AND order_id = #{orderId}
        LIMIT 1
    </select>

    <!-- 根据订单号查询评价信息 -->
    <select id="selectByOrderNo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.evaluation.OrderEvaluationDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_aunt_review
        WHERE deleted = 0
        AND order_id = (SELECT id FROM publicbiz_order WHERE order_no = #{orderNo} AND deleted = 0 LIMIT 1)
        LIMIT 1
    </select>

    <!-- 根据服务人员ID查询评价列表 -->
    <select id="selectByPractitionerId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.evaluation.OrderEvaluationDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_aunt_review
        WHERE deleted = 0
        AND aunt_id = #{practitionerId}
        ORDER BY create_time DESC
    </select>

    <!-- 更新评价信息（排除生成列rating和自动更新字段） -->
    <update id="updateEvaluationExcludeRating" parameterType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.evaluation.OrderEvaluationDO">
        UPDATE publicbiz_aunt_review
        SET 
            attitude_rating = #{attitudeRating},
            professional_rating = #{professionalRating},
            responsibility_rating = #{responsibilityRating},
            review_tags = #{reviewTags},
            review_content = #{reviewContent},
            review_images = #{reviewImages},
            review_type = #{reviewType},
            is_anonymous = #{isAnonymous},
            is_recommend = #{isRecommend},
            like_count = #{likeCount},
            reply_content = #{replyContent},
            reply_time = #{replyTime},
            status = #{status},
            agency_id = #{agencyId},
            service_package_id = #{servicePackageId},
            updater = #{updater}
        WHERE id = #{id} 
        AND deleted = 0
    </update>

</mapper>
