<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper">

    <sql id="selectFields">
        id, aunt_oneid, name, phone, id_card, hometown, age, gender, avatar, service_type, experience_years,
        platform_status, rating, agency_id, agency_name, status, application_id, submit_time, current_status, current_order_id,
        total_orders, total_income, customer_satisfaction, profile, create_time, update_time, creator, updater, deleted, tenant_id
    </sql>
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO">
        <id column="id" property="id"/>
        <result column="aunt_oneid" property="auntOneid"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="id_card" property="idCard"/>
        <result column="hometown" property="hometown"/>
        <result column="age" property="age"/>
        <result column="gender" property="gender"/>
        <result column="avatar" property="avatar"/>
        <result column="service_type" property="serviceType"/>
        <result column="experience_years" property="experienceYears"/>
        <result column="platform_status" property="platformStatus"/>
        <result column="rating" property="rating"/>
        <result column="agency_id" property="agencyId"/>
        <result column="agency_name" property="agencyName"/>
        <result column="status" property="status"/>
        <result column="application_id" property="applicationId"/>
        <result column="submit_time" property="submitTime"/>
        <result column="current_status" property="currentStatus"/>
        <result column="current_order_id" property="currentOrderId"/>
        <result column="total_orders" property="totalOrders"/>
        <result column="total_income" property="totalIncome"/>
        <result column="customer_satisfaction" property="customerSatisfaction"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="deleted" property="deleted"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

 <sql id="selectFieldsWithAlias">
        p.id, p.aunt_oneid, p.name, p.phone, p.id_card, p.hometown, p.age, p.gender, p.avatar, p.service_type, p.experience_years,
        p.platform_status, p.rating, p.agency_id, p.agency_name, p.status, p.application_id, p.submit_time, p.current_status, p.current_order_id,
        p.total_orders, p.total_income, p.customer_satisfaction, p.profile, p.create_time, p.update_time, p.creator, p.updater, p.deleted, p.tenant_id
    </sql>
    <!-- 根据openid查询阿姨信息 -->
    <select id="selectByOpenId" resultMap="BaseResultMap">
        SELECT p.id, p.aunt_oneid, p.name, p.phone, p.id_card, p.hometown, p.age, p.gender, p.avatar, 
               p.service_type, p.experience_years, p.platform_status, p.rating, p.agency_id, p.agency_name, 
               p.status, p.application_id, p.submit_time, p.current_status, p.current_order_id, p.total_orders, p.total_income, 
               p.customer_satisfaction, p.create_time, p.update_time, p.creator, p.updater, p.deleted, p.tenant_id 
        FROM publicbiz_practitioner p 
        INNER JOIN mp_user m ON p.aunt_oneid COLLATE utf8mb4_unicode_ci = m.oneid COLLATE utf8mb4_unicode_ci
        WHERE m.openid = #{openId} AND p.deleted = 0 AND m.deleted = 0
    </select>

    <!-- 根据阿姨OneID查询阿姨信息 -->
    <select id="selectByAuntOneId" resultMap="BaseResultMap">
        SELECT id, aunt_oneid, name, phone, id_card, hometown, age, gender, avatar, 
               service_type, experience_years, platform_status, rating, agency_id, agency_name, 
               status, application_id, submit_time, current_status, current_order_id, total_orders, total_income, 
               customer_satisfaction, create_time, update_time, creator, updater, deleted, tenant_id 
        FROM publicbiz_practitioner 
        WHERE aunt_oneid = #{auntOneId} AND deleted = 0
    </select>

    <!-- 根据手机号查询家政人员信息 -->
    <select id="selectByMobile" resultMap="BaseResultMap">
        SELECT p.id, p.aunt_oneid, p.name, p.phone, p.id_card, p.hometown, p.age, p.gender, p.avatar, 
               p.service_type, p.experience_years, p.platform_status, p.rating, p.agency_id, p.agency_name, 
               p.status, p.application_id, p.submit_time, p.current_status, p.current_order_id, p.total_orders, p.total_income, 
               p.customer_satisfaction, p.create_time, p.update_time, p.creator, p.updater, p.deleted, p.tenant_id 
        FROM publicbiz_practitioner p 
        INNER JOIN mp_user m ON p.aunt_oneid COLLATE utf8mb4_unicode_ci = m.oneid COLLATE utf8mb4_unicode_ci
        WHERE m.mobile = #{mobile} AND p.deleted = 0 AND m.deleted = 0
    </select>

    <!-- 查询首页阿姨列表 -->
    <select id="selectHomeAuntList" resultMap="BaseResultMap">
        SELECT id, aunt_oneid, name, phone, id_card, hometown, age, gender, avatar, 
               service_type, experience_years, platform_status, rating, agency_id, agency_name, 
               status, application_id, submit_time, current_status, current_order_id, total_orders, total_income, 
               customer_satisfaction, create_time, update_time, creator, updater, deleted, tenant_id 
        FROM publicbiz_practitioner 
        WHERE platform_status = 'cooperating' 
          AND deleted = 0 
          AND status = 'active'
        ORDER BY rating DESC
        LIMIT #{limit}
    </select>

    <!-- 根据身份证号查询家政人员信息 -->
    <select id="selectByIdCard" resultMap="BaseResultMap">
        SELECT id, aunt_oneid, name, phone, id_card, hometown, age, gender, avatar, 
               service_type, experience_years, platform_status, rating, agency_id, agency_name, 
               status, application_id, submit_time, current_status, current_order_id, total_orders, total_income, 
               customer_satisfaction, create_time, update_time, creator, updater, deleted, tenant_id 
        FROM publicbiz_practitioner 
        WHERE id_card = #{idCard} AND deleted = 0
    </select>

    <!-- 根据申请单ID查询阿姨信息 -->
    <select id="selectByApplicationId" resultMap="BaseResultMap">
        SELECT id, aunt_oneid, name, phone, id_card, hometown, age, gender, avatar, 
               service_type, experience_years, platform_status, rating, agency_id, agency_name, 
               status, application_id, submit_time, current_status, current_order_id, total_orders, total_income, 
               customer_satisfaction, create_time, update_time, creator, updater, deleted, tenant_id 
        FROM publicbiz_practitioner 
        WHERE application_id = #{applicationId} AND deleted = 0
    </select>

    <!-- 根据OneID查询阿姨信息 -->
    <select id="selectByOneid" resultMap="BaseResultMap">
        SELECT id, aunt_oneid, name, phone, id_card, hometown, age, gender, avatar, 
               service_type, experience_years, platform_status, rating, agency_id, agency_name, 
               status, application_id, submit_time, current_status, current_order_id, total_orders, total_income, 
               customer_satisfaction, create_time, update_time, creator, updater, deleted, tenant_id 
        FROM publicbiz_practitioner 
        WHERE aunt_oneid = #{oneid} AND deleted = 0
    </select>

</mapper>