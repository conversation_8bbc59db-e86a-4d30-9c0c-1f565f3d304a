package cn.bztmaster.cnt.module.publicbiz.api.order.dto;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.LocalDate;

/**
 * 家政服务订单分页查询 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务订单分页查询 Request DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DomesticTaskOrderPageReqDTO extends PageParam {

    @Schema(description = "订单状态", example = "pending")
    private String orderStatus;

    @Schema(description = "支付状态", example = "unpaid")
    private String paymentStatus;

    @Schema(description = "服务类型", example = "maternity")
    private String serviceType;

    @Schema(description = "搜索关键词（客户姓名、服务人员、服务机构）", example = "李女士")
    private String keyword;

    @Schema(description = "开始日期", example = "2024-06-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-06-30")
    private LocalDate endDate;

    // 添加对前端传递的page和size参数的支持
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page;

    @Schema(description = "每页条数，最大值为100", example = "10")
    private Integer size;

    /**
     * 重写getPageNo方法，优先使用page参数
     */
    @Override
    public Integer getPageNo() {
        if (page != null) {
            return page;
        }
        return super.getPageNo();
    }

    /**
     * 重写getPageSize方法，优先使用size参数
     */
    @Override
    public Integer getPageSize() {
        if (size != null) {
            return size;
        }
        return super.getPageSize();
    }
}
