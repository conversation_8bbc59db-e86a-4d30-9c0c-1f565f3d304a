package cn.bztmaster.cnt.module.publicbiz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 收支记录类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum IncomeExpenseTypeEnum {

    // 收入类型
    ORDER_INCOME("order_income", "订单收入", "income"),
    EXTRA_INCOME("extra_income", "额外收入", "income"),
    REFUND_INCOME("refund_income", "退款收入", "income"),
    COMMISSION_INCOME("commission_income", "佣金收入", "income"),
    
    // 支出类型
    SERVICE_EXPENSE("service_expense", "服务支出", "expense"),
    COMPENSATION_EXPENSE("compensation_expense", "赔偿支出", "expense"),
    REFUND_EXPENSE("refund_expense", "退款支出", "expense"),
    COMMISSION_EXPENSE("commission_expense", "佣金支出", "expense"),
    OPERATION_EXPENSE("operation_expense", "运营支出", "expense");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型名称
     */
    private final String name;

    /**
     * 收支方向：income-收入，expense-支出
     */
    private final String direction;

    /**
     * 根据编码获取枚举
     *
     * @param code 类型编码
     * @return 枚举
     */
    public static IncomeExpenseTypeEnum getByCode(String code) {
        for (IncomeExpenseTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据编码获取类型名称
     *
     * @param code 类型编码
     * @return 类型名称
     */
    public static String getNameByCode(String code) {
        IncomeExpenseTypeEnum type = getByCode(code);
        return type != null ? type.getName() : "未知类型";
    }

    /**
     * 根据编码获取收支方向
     *
     * @param code 类型编码
     * @return 收支方向
     */
    public static String getDirectionByCode(String code) {
        IncomeExpenseTypeEnum type = getByCode(code);
        return type != null ? type.getDirection() : "unknown";
    }

    /**
     * 判断是否为收入类型
     *
     * @param code 类型编码
     * @return 是否为收入
     */
    public static boolean isIncome(String code) {
        return "income".equals(getDirectionByCode(code));
    }

    /**
     * 判断是否为支出类型
     *
     * @param code 类型编码
     * @return 是否为支出
     */
    public static boolean isExpense(String code) {
        return "expense".equals(getDirectionByCode(code));
    }
}
