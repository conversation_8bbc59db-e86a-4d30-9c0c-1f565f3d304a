package cn.bztmaster.cnt.module.publicbiz.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 家政服务订单导出 Response DTO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务订单导出 Response DTO")
@Data
public class DomesticTaskOrderExportRespDTO {

    @Schema(description = "导出文件URL", example = "https://example.com/export/domestic-task-order.xlsx")
    private String fileUrl;

    @Schema(description = "导出文件名", example = "家政服务订单列表.xlsx")
    private String fileName;
}
