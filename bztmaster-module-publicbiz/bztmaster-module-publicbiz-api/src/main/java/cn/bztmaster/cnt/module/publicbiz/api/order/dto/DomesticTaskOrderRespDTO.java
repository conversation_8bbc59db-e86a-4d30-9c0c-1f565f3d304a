package cn.bztmaster.cnt.module.publicbiz.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 家政服务订单 Response DTO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务订单 Response DTO")
@Data
public class DomesticTaskOrderRespDTO {

    @Schema(description = "订单ID", example = "1")
    private Long id;

    @Schema(description = "订单编号", example = "DS202406001")
    private String orderNumber;

    @Schema(description = "客户姓名", example = "李女士")
    private String customerName;

    @Schema(description = "客户电话", example = "13812345678")
    private String customerPhone;

    @Schema(description = "服务类型", example = "月嫂服务")
    private String serviceType;

    @Schema(description = "服务人员", example = "张阿姨")
    private String servicePersonnel;

    @Schema(description = "服务机构", example = "爱家月嫂服务中心")
    private String serviceAgency;

    @Schema(description = "服务金额", example = "12800.00")
    private BigDecimal serviceAmount;

    @Schema(description = "已完成任务数", example = "15")
    private Integer completedTasks;

    @Schema(description = "总任务数", example = "30")
    private Integer totalTasks;

    @Schema(description = "订单状态", example = "in_service")
    private String orderStatus;

    @Schema(description = "支付状态", example = "paid")
    private String paymentStatus;

    @Schema(description = "预约时间", example = "2024-06-01 09:00:00")
    private LocalDateTime appointmentTime;

    @Schema(description = "创建时间", example = "2024-06-01 09:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2024-06-15 10:30:00")
    private LocalDateTime updateTime;
}
