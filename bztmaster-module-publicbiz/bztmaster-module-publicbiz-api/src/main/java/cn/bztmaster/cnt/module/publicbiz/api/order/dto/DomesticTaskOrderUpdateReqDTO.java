package cn.bztmaster.cnt.module.publicbiz.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 家政服务订单更新 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务订单更新 Request DTO")
@Data
public class DomesticTaskOrderUpdateReqDTO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    @NotNull(message = "订单ID不能为空")
    private Long id;

    @Schema(description = "订单号", example = "DS202406005")
    private String orderNo;

    @Schema(description = "客户姓名", example = "王女士")
    private String customerName;

    @Schema(description = "客户电话", example = "13987654321")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "客户电话格式不正确")
    private String customerPhone;

    @Schema(description = "服务地址", example = "北京市朝阳区建国路88号")
    private String serviceAddress;

    @Schema(description = "服务开始日期", example = "2024-07-01")
    private LocalDate serviceStartDate;

    @Schema(description = "服务结束日期", example = "2024-07-31")
    private LocalDate serviceEndDate;

    @Schema(description = "服务人员OneID", example = "practitioner_001")
    private String practitionerOneid;

    @Schema(description = "服务人员姓名", example = "张阿姨")
    private String practitionerName;

    @Schema(description = "服务人员电话", example = "13800138000")
    private String practitionerPhone;

    @Schema(description = "服务机构ID", example = "1001")
    private Long agencyId;

    @Schema(description = "备注", example = "客户要求月嫂有3年以上经验")
    private String remark;

    @Schema(description = "支付状态", example = "pending")
    private String paymentStatus;

    @Schema(description = "已收款金额", example = "5000.00")
    private BigDecimal receivedAmount;

    @Schema(description = "单价", example = "100.00")
    private BigDecimal unitPrice;

    @Schema(description = "订单总金额", example = "5000.00")
    private BigDecimal totalAmount;

    @Schema(description = "实付金额", example = "4800.00")
    private BigDecimal actualAmount;

    @Schema(description = "支付方式", example = "cash")
    private String paymentMethod;

    @Schema(description = "支付时间")
    private LocalDateTime paymentTime;

    @Schema(description = "支付备注", example = "客户要求开具发票")
    private String paymentRemark;
}
