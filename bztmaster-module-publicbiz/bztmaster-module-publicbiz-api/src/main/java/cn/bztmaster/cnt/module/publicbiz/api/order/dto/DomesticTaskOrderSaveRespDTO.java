package cn.bztmaster.cnt.module.publicbiz.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 家政服务订单保存 Response DTO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务订单保存 Response DTO")
@Data
public class DomesticTaskOrderSaveRespDTO {

    @Schema(description = "订单ID", example = "1001")
    private Long orderId;

    @Schema(description = "订单编号", example = "DS202406005")
    private String orderNumber;

    @Schema(description = "订单号", example = "DS202406005")
    private String orderNo;

    @Schema(description = "客户姓名", example = "王女士")
    private String customerName;

    @Schema(description = "客户电话", example = "13987654321")
    private String customerPhone;

    @Schema(description = "服务类型", example = "月嫂服务")
    private String serviceType;

    @Schema(description = "订单总金额", example = "5000.00")
    private BigDecimal totalAmount;

    @Schema(description = "支付状态", example = "pending")
    private String paymentStatus;
}
