package cn.bztmaster.cnt.module.publicbiz.api.order.dto;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.*;

/**
 * 家政服务订单 API 接口
 *
 * <AUTHOR>
 */
public interface DomesticTaskOrderApi {

    /**
     * 分页查询家政服务订单列表
     *
     * @param reqDTO 查询条件
     * @return 订单列表
     */
    PageResult<DomesticTaskOrderRespDTO> pageDomesticTaskOrder(DomesticTaskOrderPageReqDTO reqDTO);

    /**
     * 新增家政服务订单
     *
     * @param reqDTO 订单信息
     * @return 创建结果
     */
    DomesticTaskOrderSaveRespDTO createDomesticTaskOrder(DomesticTaskOrderSaveReqDTO reqDTO);

    /**
     * 更新家政服务订单
     *
     * @param reqDTO 订单信息
     * @return 更新结果
     */
    Boolean updateDomesticTaskOrder(DomesticTaskOrderUpdateReqDTO reqDTO);

    /**
     * 删除家政服务订单
     *
     * @param id 订单ID
     * @return 删除结果
     */
    Boolean deleteDomesticTaskOrder(Long id);

    /**
     * 获取家政服务订单详情
     *
     * @param id 订单ID
     * @return 订单详情
     */
    DomesticTaskOrderDetailRespDTO getDomesticTaskOrderDetail(Long id);

    /**
     * 导出家政服务订单列表
     *
     * @param reqDTO 导出条件
     * @return 导出结果
     */
    DomesticTaskOrderExportRespDTO exportDomesticTaskOrder(DomesticTaskOrderExportReqDTO reqDTO);
}
