package cn.bztmaster.cnt.module.publicbiz.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 家政服务订单导出 Request DTO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务订单导出 Request DTO")
@Data
public class DomesticTaskOrderExportReqDTO {

    @Schema(description = "订单状态", example = "in_service")
    private String orderStatus;

    @Schema(description = "支付状态", example = "paid")
    private String paymentStatus;

    @Schema(description = "服务类型", example = "maternity")
    private String serviceType;

    @Schema(description = "搜索关键词", example = "李女士")
    private String keyword;

    @Schema(description = "开始日期", example = "2024-06-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-06-30")
    private LocalDate endDate;

    @Schema(description = "导出格式", example = "excel")
    private String format = "excel";
}
